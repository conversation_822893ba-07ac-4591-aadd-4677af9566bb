package internal

import (
	"context"
	"gitlab.ttyuyin.com/tyr/x/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pb "golang.52tt.com/protocol/services/aigc/aigc-zego-stream"
)

var (
	errUnimplemented = status.Error(codes.Unimplemented, "")
)

type StartConfig struct {
	// from config file

}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	s := &Server{}

	return s, nil
}

type Server struct {
}

func (s *Server) ShutDown() {

}

func (s *Server) ZegoStream(ctx context.Context, req *pb.ZegoStreamReq) (*pb.ZegoStreamResp, error) {
	return nil, errUnimplemented
}
