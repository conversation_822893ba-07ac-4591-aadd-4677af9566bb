package main

import (
	"context"
	"gitlab.ttyuyin.com/tyr/tt-ecosystem/startup/suit/grpc/server" // server startup
	"gitlab.ttyuyin.com/tyr/x/log"
	"google.golang.org/grpc"

	pb "golang.52tt.com/protocol/services/aigc/aigc-zego-stream"

	"golang.52tt.com/third-party/tt-protocol/service/quicksilver/aigc-zego-stream/internal"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server"
)

func main() {
	var (
		svr *internal.Server
		cfg = &internal.StartConfig{}
		err error
	)

	// config file support yaml & json, default aigc-zego-stream.json/yaml
	if err := grpcserver.NewServer("aigc-zego-stream", cfg).
		AddGrpcServer(grpcserver.NewPlugin().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				if svr, err = internal.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterZegoStreamServer(s, svr)
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
