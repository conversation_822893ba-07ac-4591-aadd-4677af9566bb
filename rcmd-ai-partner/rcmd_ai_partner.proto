syntax = "proto3";

package rcmd.rcmd_ai_partner;

option go_package = "golang.52tt.com/protocol/services/rcmd/rcmd_ai_partner";
import "rcmd-ai-partner/partner_common.proto";
service RCMDAIPartner {
  // 设置AI伴侣信息
  rpc SetAIPartnerInfo(SetAIPartnerInfoReq) returns (SetAIPartnerInfoResp);
  // 接受用户发给AI伴侣的消息
  rpc ReceiveMsgFromUser(ReceiveMsgFromUserReq) returns (ReceiveMsgFromUserResp);
  // 接受用户发给AI群组的消息
  rpc ReceiveGroupMsgFromUser(ReceiveGroupMsgFromUserReq) returns (ReceiveGroupMsgFromUserResp);
  // 接收用户发给AI伴侣的特殊消息(如：送礼等）
  rpc ReceiveSpecialMsgFromUser(ReceiveSpecialMsgFromUserReq) returns (ReceiveSpecialMsgFromUserResp);
  // 用户进入虚拟陪伴聊天页通知
  rpc UserEnterChattingNotify(UserEnterChattingNotifyReq) returns (UserEnterChattingNotifyResp);
  // 设置虚拟伴侣是否可以聊天
  rpc SetAIPartnerChattingStatus(SetAIPartnerChattingStatusReq) returns (SetAIPartnerChattingStatusResp);

  // 添加或删除虚拟伴侣功能设备白名单
  rpc SetDeviceWhitelist(SetDeviceWhitelistReq) returns (SetDeviceWhitelistResp);

  // 添加或删除虚拟伴侣功能uid白名单
  // rpc SetUserWhitelist(SetUserWhitelistReq) returns (SetUserWhitelistResp);

  // 虚拟伴侣是否开启
  rpc Trigger(TriggerReq) returns (TriggerResp);
  // 设置关系
  rpc SetRelationship(SetRelationshipReq) returns (SetRelationshipResp);
  // 新版虚拟伴侣是否开启
  rpc NewTrigger(NewTriggerReq) returns (NewTriggerResp);
  // 删除树洞
  rpc DeletePartner(DeletePartnerReq) returns (DeletePartnerResp);

  // 测试接口
  // 内部测试接口：机器人回复文本切分
  rpc ReplyTextFormat(ReplyTextFormatReq) returns (ReplyTextFormatResp);

  // 添加黑名单接口
  rpc AddBlackList(AddBlackListReq) returns (AddBlackListResp);
  // 删除黑名单接口
  rpc DelBlackList(DelBlackListReq) returns (DelBlackListResp);
  rpc SendAnimatedMeme(SendAnimatedMemeReq) returns (SendAnimatedMemeResp);

  // 获取故事信息
  rpc GetStoryInfo(GetStoryInfoReq) returns (GetStoryInfoResp);
  // 故事开始
  rpc StartStory(StartStoryReq) returns (StartStoryResp);
  // 故事进度操作
  rpc SetStoryProgress(SetStoryProgressReq) returns (SetStoryProgressResp);
  // 获取剧幕信息
  rpc GetStoryScene(GetStorySceneReq) returns (GetStorySceneResp);
  // 获取回复信息
  rpc GetStoryReply(GetStoryReplyReq) returns (GetStoryReplyResp);
  // 获取故事聊天记录
  rpc GetStoryHistory(GetStoryHistoryReq) returns(GetStoryHistoryResp);

  // 生成角色人设说明(角色设定一键生成)
  rpc GenRoleDesc(GenRoleDescReq) returns(GenRoleDescResp);
  // 推荐回复生成
  rpc GenRecommendReply(GenRecommendReplyReq) returns(GenRecommendReplyResp);
  // 获取角色设定一键生成次数限制（用于前端是否显示一键生成按钮）
  rpc GetGenStatus(GetGenStatusReq) returns(GetGenStatusResp);

  // 获取用户消息统计
  rpc GetUserMsgStat(GetUserMsgStatReq) returns(GetUserMsgStatResp);

  // 获取用户信息
  rpc GetUserInfo(GetUserInfoReq) returns(GetUserInfoResp);

   // 快捷回复(一键回复)
   rpc GetQuickReplyList(GetQuickReplyListReq) returns(GetQuickReplyListResp);
  // Get GPT Req info
  rpc GetGPTReqInfo(GetGPTReqInfoReq) returns(GetGPTReqInfoResp);
  // 获取开场白
  rpc GetGreetingMsg(GetGreetingMsgReq) returns(GetGreetingMsgResp);
  rpc FormatGptAnswer(FormatGptAnswerReq) returns(FormatGptAnswerResp);
  // 通过role_id获取partner
  rpc GetPartnerByRoleId(GetPartnerByRoleIdReq) returns(GetPartnerByRoleIdResp);
  // 设置树洞主动触发的标签
  rpc SetPartnerLoginPushTag(SetPartnerLoginPushTagReq) returns(SetPartnerLoginPushTagResp);
  rpc GetPartner(GetPartnerReq) returns(GetPartnerResp);

  rpc SetPartnerSilent(SetPartnerSilentReq) returns(SetPartnerSilentResp);//设置免打扰，不区分树洞和多角色
  rpc GetPartnerSettings(GetPartnerSettingsReq) returns(GetPartnerSettingsResp);//获取用户设置，不区分树洞和多角色
  rpc SetPartnerAutoPlayStatus(SetPartnerAutoPlayStatusReq) returns(SetPartnerAutoPlayStatusResp);//设置语音自动播放，不区分树洞和多角色

  // 是否显示继续说
  rpc ShowContinueChat(ShowContinueChatReq) returns(ShowContinueChatRsp);

  // 继续说
  rpc ContinueChat(ContinueChatReq) returns(ContinueChatRsp);

  rpc TestASR(TestASRReq) returns(TestASRResp);//测试ASR

  // 生成语音打招呼
  rpc GenVoiceChatGreeting(GenVoiceChatGreetingReq) returns(GenVoiceChatGreetingResp);

  // 判断是否开启伴侣功能
  rpc GetPartnerOpen(GetPartnerOpenReq) returns(GetPartnerOpenRsp);

  // 角色向多人群组主动发送消息
  rpc AISendMsgToGroup(AISendMsgToGroupReq) returns (AISendMsgToGroupResp);

  // 角色批量向多人群组主动发送消息
  rpc BatchAISendMsgToGroup(AIBatchSendMsgToGroupReq) returns (AIBatchSendMsgToGroupResp);

  // 奖励句数增加
  rpc RewardSentenceAdd(RewardSentenceAddReq) returns (RewardSentenceAddResp);
}
message GenVoiceChatGreetingReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string voice_id = 4;
    string voice_json = 5;
    string text = 6;
    string session_id = 7;
}

message GenVoiceChatGreetingResp {
    string url = 1;
    int64 seconds = 2; // 消息类型
    string content = 3; // 内容
}
message TestASRReq {
    int64 timestamp = 1;
    string text = 2;
    string ctx = 3;
}

message TestASRResp {
    string msg = 1;
}

message SetPartnerSilentReq {
    uint32 uid = 1;
    uint32 role_id = 2;
    uint32 partner_id = 3;
    bool is_silent = 4;// 是否免打扰
}

message SetDeviceWhitelistReq {
    repeated string device_id_list = 1;
    bool is_delete = 2; //删除白名单，优先级比is_check高
    bool is_check = 3;//检查是否白名单
    string group = 4;//分组
}
message SetDeviceWhitelistResp {
    string msg = 1;
}
// message SetUserWhitelistReq {
//     repeated uint32 user_id_list = 1;
// }
// message SetUserWhitelistResp {
//     string msg = 1;
// }

message SetPartnerSilentResp {
    bool is_silent = 1;// 是否免打扰
}


message SetPartnerAutoPlayStatusReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    bool auto_play_status = 3;  // 是否开启语音自动播放
}
message SetPartnerAutoPlayStatusResp {
    bool auto_play_status = 1;  // 是否开启语音自动播放
}

message GetPartnerSettingsReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
}

message GetPartnerSettingsResp {
    bool is_silent = 1;  // 是否免打扰
    bool auto_play_status = 2;  // 是否自动播放语音
}

message GetPartnerReq {
    uint32 id = 1;
    uint32 uid = 2;
}

message GetPartnerResp {
    Partner partner = 1;
    bool exist = 2;
}

message SetPartnerLoginPushTagReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    string tag = 3;
}

message SetPartnerLoginPushTagResp {
    bool ok = 1;
}

message GetPartnerByRoleIdReq {
    uint32 role_id = 1;
    uint32 uid = 2;
}

message GetPartnerByRoleIdResp {
    Partner partner = 1;
    bool exist = 2;
}

message Partner {
    uint32 id = 1;
    uint32 uid = 2;
    string name = 3;
    string call_name = 4;
    uint32 role_id = 5;
    uint32 chatting_status = 6;
    uint32 relationship_id = 7;
    repeated string animate_memes = 8;
    int64 create_time = 9;
    int64 update_time = 10;
    uint32 user_msg_count = 11;//用户发送消息数
}

message FormatGptAnswerReq {
    string gpt_a = 1;
}

message FormatGptAnswerResp {
   repeated rcmd.partner_common.FormatMsg msg_list = 1;
}

message GetQuickReplyListReq{
    uint32 role_id = 1;
}
message GetQuickReplyListResp{
    repeated string reply_list = 1;
}

message GetGreetingMsgReq {
    uint32 role_id = 1;
}

message GetGreetingMsgResp {
   repeated rcmd.partner_common.FormatMsg msg_list = 1;
}

message GetGPTReqInfoReq {
    uint32 uid = 1;
    uint32 role_id = 2;
}

message GetGPTReqInfoResp {
    string placeholder_str = 1; // 占位符, map<string, string> 的json字符串
    string prompt_id = 2;
    string prompt_version = 3;
}

message GetUserInfoReq{
    uint32 uid = 1;
}
message GetUserInfoResp{
    uint32 user_today_msg_count = 1;//当天用户发送消息数
    uint32 user_total_msg_count = 2;//  用户发送消息总数

}

message GetUserMsgStatReq {
    uint32 uid = 1;
}

message GetUserMsgStatResp {
    uint32 talked_partner_count = 1; //用户发送的消息数
    uint32 fallback_page = 2; //1 首页 2 聊过TAb
}

message GenRoleDescReq{
    uint32 uid = 1;
    string nickname = 2;
    uint32 sex = 3;//0女 1男
}
message GenRoleDescResp{
    string desc = 1;
}
//	"uid":111,//uint32 用户ID
//
// "partner_id":123,//uint32,机器人ID
// "role_id":333,//uint32,角色ID
message GenRecommendReplyReq{
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;

}
message GenRecommendReplyResp{
    repeated string reply_list = 1;
}
message GetGenStatusReq{
    uint32 uid = 1;
}
message GetGenStatusResp{
    bool gen_role_exceeded = 1;// 是否超过生成次数
}
message GetStoryHistoryReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 story_id = 3;
    int64 max_sent_at = 4; //返回比max_sent_at小的数据，不传默认拉取最新的
    uint32 size = 5; //拉取的大小
}

message StoryMsg {
    uint32 type = 1; // 消息类型
    string content = 2; // 内容
    bytes ext = 3; // 扩展内容
    int64 sent_at = 4; // 发送时间
    string msg_id = 5; // 消息ID
    string sender = 6; //发送者
}
message GetStoryHistoryResp {
    repeated StoryMsg msg_list = 1;
    uint32 size = 2;
}

message GetStoryReplyReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 story_id = 3;
}

message GetStoryReplyResp {
    string scene_id = 1;
    string dialog_id = 2;
    message ReplyOptions {
        string id = 1;// content id
        string content = 2; // content
    }
    repeated ReplyOptions reply_options = 3;
    bool enable_custom_reply = 4; // 是否启用自定义回复
    string custom_content_id = 5; // 自定义回复的content id 字段
}

message GetStorySceneReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 story_id = 3;
}

message GetStorySceneResp {
    string scene_name = 1; // 剧幕，如第一幕，第二幕
    string scene_id = 2; // 剧幕 ID
    string background_url = 3; //  剧幕背景
    string background_music_url = 4; // 剧幕背景音乐

}

message SetStoryProgressReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 story_id = 3;
    uint32 op = 4;// 0 继续游戏， 1 重新开始
}

message SetStoryProgressResp {
    bool ok = 1;// 是否成功开始故事
}

message StartStoryReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 story_id = 3;
}

message StartStoryResp {
    bool ok = 1;// 是否成功开始故事
}

message GetStoryInfoReq {
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 story_id = 3;
}

message GetStoryInfoResp {
    int64 trigger_ts = 1; // 故事触发的时间戳
    bool is_expire = 2; // 故事是否过期
    uint32 status = 3; // 故事状态
    string story_name = 4; // 故事昵称
}

message SendAnimatedMemeReq {
    uint32 id = 1;
    uint32 uid = 2;
    string animated_meme = 3; // 要发送的轻互动表情
}

message SendAnimatedMemeResp {

}

message SetRelationshipReq {
    uint32 id = 1;
    uint32 uid = 2;
    uint32 relationship_id = 3;
}

message SetRelationshipResp {}

message AddBlackListReq {
    repeated uint32 uid_list = 1;
}
message AddBlackListResp {
}

message DelBlackListReq {
    repeated uint32 uid_list = 1;
}

message DelBlackListResp {
}

message ReplyTextFormatReq {
    string text = 1;
    FormatType format_type = 2;
    enum FormatType {
        FormatType_Default = 0;
        FormatType_TextProcDefault = 1;
        FormatType_RemoveBracket = 2;
        FormatType_TextProcV1 = 3;
        FormatType_LongReplyDetect = 4;
    }
    string last_reply = 3; // format_type == FormatType_LongReplyDetect 时存在
}
message ReplyTextFormatResp {
    repeated string texts = 1;
    repeated int64 delay_times = 2;
    string data = 3;
}
message TriggerReq {
    uint32 uid = 1;
}

message TriggerResp {
    bool is_open = 1;
    bool is_open_no_role = 2; // 是否开启去形象化
}

enum ChattingStatus {
    Chatting = 0; // 可聊天
    Silence = 1; // 沉默
}

message SetAIPartnerChattingStatusReq {
    uint32 uid = 1; // 请求用户
    uint32 id = 2; // AI玩伴id
    ChattingStatus status = 3; // 聊天状态配置
}

message SetAIPartnerChattingStatusResp {

}
enum AIRoleType {
  AIRoleTypeDefault = 0; // 树洞
  AIRoleTypeGame = 1;// 游戏角色
  AIRoleTypePet = 2;// 桌宠
  AIRoleTypeGroup = 3; // 群聊角色
}
message SetAIPartnerInfoReq {
  uint32 uid = 1; // 请求用户id
  uint32 id = 2; // AI玩伴id
  string name = 3; // AI玩伴名称
  string call_name = 4; // AI玩伴对用户称呼
  enum Relationship {
      RelationshipUnknown = 0;
      // 朋友
      RelationshipFriend = 1;
      // 恋人
      RelationshipLover = 2;
  }

  Relationship relationship = 5; // 请求用户与AI伴侣的关系
  AIRole role = 6; // AI伴侣的角色特征
  ChattingStatus status = 7; // 聊天状态配置
  AIRoleType ai_role_type = 8; // 角色类型
  uint32 source = 9; // 创建来源 参考business_ai_partner.proto PartnerSource
}

message AIRole {
    uint32 id = 1;
    // AI风格
    string style = 2;
    // AI性别 0:女 1:男
    int32 sex = 3;
    Type type = 4; // role类型
    enum Type {
        Type_Default = 0; // 默认类型
        Type_NoChoose = 1; // 去形象化的角色类型
    }
}

message SetAIPartnerInfoResp {
}

message ReceiveMsgFromUserReq {
    uint32 uid = 1;
    uint32 ai_partner_id = 2;
    enum MsgType {
        MsgType_Unknown = 0;
        // 文本
        MsgType_Text = 1;
        // 表情
        MsgType_Emoticon = 3;
        MsgType_ImMsgAIPartner = 6; //AI伴侣统一消息通道
    }

    // 消息类型
    MsgType msg_type = 3;
    // 消息内容
    string content = 4;
    bytes ext = 5;
    string msg_id = 6;// 消息ID
    AIRoleType ai_role_type = 7; // 角色类型
    uint32 im_busi_type = 8; //业务类型 ImBusiType, 定义见 chat-bot.proto.ImBusiType
    uint32 im_content_type = 9; //内容类型 ImMsgContentType, 定义见 chat-bot.proto.ImMsgContentType
    uint32 im_cmd_type = 10; //命令类型 ImCmdType, 定义见 chat-bot.proto.ImCmdType
    map<string,string> extra_map = 11; // 扩展字段
    bool is_exactly_reach_limit = 12; // 是否达到回复上限(刚好达到上限的那一次)
    rcmd.partner_common.GameInfo interactive_game_info = 13;  // 互动玩法信息
}

message ReceiveMsgFromUserResp {
}

message UserEnterChattingNotifyReq {
    uint32 uid = 1;
    uint32 ai_partner_id = 2;
    AIRoleType ai_role_type = 3; // 角色类型
}

message UserEnterChattingNotifyResp {
}

message NewTriggerReq {
    uint32 uid = 1;
    uint32 cli_version = 2;
    uint32 source = 3; // 触发来源:  1 聊天页, 2 登录, 3 树洞-web, 1000 内部服务器调用
    string req_dev_id = 4; // 请求设备ID
}

enum TriggerSource {
    Invalid = 0;
    ChattingPage = 1; // 聊天页
    LoginPage = 2; // 登录页
    PartnerWeb = 3; // 树洞-web
    Internal = 1000; // 内部服务调用
}

message NewTriggerResp {
    bool is_open = 1;
    bool is_open_no_role = 2; // 是否开启去形象化
    bool enable_multi_roles = 3; // 是否启用多角色
    bool enable_pet = 4; // 是否启用桌宠
    AIPartnerNormalUserView user_view = 5; // 用户信息
}

message DeletePartnerReq {
    uint32 id = 1;
    uint32 uid = 2;
}

message DeletePartnerResp {
}

message ShowContinueChatReq{
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;
    string req_dev_id = 4;
}
message ShowContinueChatRsp{
    bool show = 1;
    uint32 chat_left_num = 2;  // 聊天剩余句数
    uint32 continue_round_left_num = 3; // 继续说一轮剩余次数
    uint32 continue_total_left_num = 4; // 继续说总剩余次数
}

message ContinueChatReq{
    uint32 uid = 1;
    uint32 partner_id = 2;
    uint32 role_id = 3;

}
message ContinueChatRsp{
}

message GetPartnerOpenReq {
    uint32 uid = 1;
    string device_id = 2;
    uint32 market_id = 3;
}

message AIPartnerNormalUserView {
    uint32 is_online = 1; //年龄(空)
    uint32 sex = 2;//性别  0 female, 1 male
    uint32 reg_time = 3;
    uint32 market_id = 4;
    string nickname = 5;
    uint32 birthday = 6;//生日
    string device_id_hex = 7;
    uint32 client_type  = 8;
}

message GetPartnerOpenRsp {
    bool is_open = 1;
    AIPartnerNormalUserView user_view = 2;
}

enum AIGroupType {
    AIGroupType_Unknown = 0; // 未知
    AIGroupType_SingleUser = 1; // 单用户群
    AIGroupType_MultiUser = 2; // 多用户群
    AIGroupType_MultiUserScript = 3; // 多用户剧本群
}

message ReceiveGroupMsgFromUserReq {
    message UserInfo {
      string name = 1; // 用户昵称或扮演角色名
      uint32 play_role_id = 2; // 扮演角色ID (若无置 0 )
      uint32 sex = 3; // 性别 0:女 1:男; 无剧本玩法传用户真实性别
    }
    uint32 uid = 1;
    uint32 group_template_id = 2;  // 群模板 ID
    uint32 group_instance_id = 3;  // 群实例 ID
    enum MsgType {
      MsgType_Unknown = 0;
      // 文本
      MsgType_Text = 1;
      // 表情
      MsgType_Emoticon = 3;
      MsgType_ImMsgAIPartner = 6; //AI伴侣统一消息通道
    }

    MsgType msg_type = 4;  // 消息类型
    string content = 5;  // 消息内容
    bytes ext = 6;
    uint32 seq_id = 7;  // 消息ID
    AIGroupType ai_group_type = 8;  // 群类型
    repeated uint32 target_role_ids = 9;  // 被 @ 的角色 ID 列表
    repeated uint32 target_uids = 10;  // 被 @ 的用户 ID 列表
    map<uint32, UserInfo> uid_to_info = 11;  // 所有用户 ID 到用户信息的映射
    map<string, string> extra_map = 12; // 扩展字段
}

message ReceiveGroupMsgFromUserResp {
}

message ReceiveSpecialMsgFromUserReq {
  enum SpecialMsgType {
    Unknown = 0;
    GiveGift = 1;
  }
  uint32 uid = 1;
  uint32 partner_id = 2;
  SpecialMsgType msg_type = 3;
}

message ReceiveSpecialMsgFromUserResp {

}

message AISendMsgToGroupReq {
  message UserInfo {
    string name = 1; // 用户昵称或扮演角色名
    uint32 play_role_id = 2; // 扮演角色ID (若无置 0 )
    uint32 sex = 3; // 性别 0:女 1:男; 无剧本玩法传用户真实性别
  }
  uint32 group_template_id = 1;  // 群模板 ID
  uint32 group_instance_id = 2;  // 群实例 ID
  uint32 seq_id = 3;  // 消息ID
  map<uint32, UserInfo> uid_to_info = 4;  // 所有用户 ID 到用户信息的映射
  repeated uint32 target_uids = 5;  // 被 @ 的用户 ID 列表
}

message AISendMsgToGroupResp {
  string err_msg = 1; // 错误信息
}

message AIBatchSendMsgToGroupReq {
  message UserInfo {
    string name = 1; // 用户昵称或扮演角色名
    uint32 play_role_id = 2; // 扮演角色ID (若无置 0 )
    uint32 sex = 3; // 性别 0:女 1:男; 无剧本玩法传用户真实性别
  }
  message TargetUidList {
    repeated uint32 target_uids = 1;  // 被 @ 的用户 ID 列表
  }

  uint32 group_template_id = 1;  // 群模板 ID
  uint32 group_instance_id = 2;  // 群实例 ID
  map<uint32, UserInfo> uid_to_info = 3;  // 所有用户 ID 到用户信息的映射
  repeated TargetUidList target_uids_list = 4;  // 被 @ 的用户 ID 列表
}
message AIBatchSendMsgToGroupResp {
}

message RewardSentenceAddReq {
  enum SentenceType {
    SentenceType_Unknown = 0; // 未知
    SentenceType_Extra = 1; // 额外句数
    SentenceType_Curday = 2; // 当日句数
  }
  uint32 uid = 1;  // 用户ID
  SentenceType sentence_type = 2; // 句数类型
  uint32 valid_day = 3; // 句数有效时间(单位天)
  uint32 add_num = 4; // 增加的句数
}
message RewardSentenceAddResp {
}
