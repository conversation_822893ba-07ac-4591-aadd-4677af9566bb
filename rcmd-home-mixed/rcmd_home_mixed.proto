syntax="proto3";

import "common/common.proto";

package rcmd.rcmd_home_mixed;

option go_package = "golang.52tt.com/protocol/services/rcmd/rcmd_home_mixed";


//首页混推流推荐接口
service RcmdHomeMixed {
    // 获取推荐列表
    rpc GetRecommendationList (GetRecommendationListReq) returns (GetRecommendationListResp);

    // 异步获取推荐理由
    rpc GetRecommendationReason (GetRecommendationReasonReq) returns (GetRecommendationReasonResp);
}

message GetRecommendationListReq {
    uint32 uid = 1; // 请求用户uid
    uint32 limit = 2; //
    uint32 get_mode = 3; // DEFAULT = 0;    NEXTPAGE= 1; // 请求下一页    REFRESH = 2; // 刷新  INSERT = 3; //强插同逻辑房间
    uint32 channel_enter_source = 4;    // 由推荐同事定义,参考ChannelEnterReq中的EChannelEnterSource枚举值
    uint32 client_type = 5;             // 客户端设备类型 0 安卓， 1 ios， 2 web, 具体可以查询ga_base.proto  TT_CLIENT_TYPE
    uint32 client_version = 6;          // 客户端版本号
    uint32 market_id = 7;               // 0:tt, 2: 欢游, 5: 麦可, 6:迷境, 具体可以查询ga_base.proto MarketId

	string trace_id = 8; //增加一个spanId 用于算法版v0.2, 选填
	uint32 debug_flag = 9; //测试标志, 选填
    uint32 regulatory_level = 10; // 监管等级,选填    FREE = 0; //不限制    SIMPLE_MINOR = 1; // 未成年
    repeated uint32 no_browse_list  = 11; //上次请求未曝光id列表
	repeated string interest_labels = 12; 	//兴趣标签
	string delivery_type = 13; // 强插同逻辑房间需要的类型
}

enum RCMDLabel {
    None = 0;
    GangUpWithHomeOwner = 1;
    ChatWithHomeOwner = 2;
	FollowUserInChannel = 3; // 关注
    LocShow = 4; // 同城
	MtFollowUserInChannel = 5; // 有好友/单关在的房间
	MtEverEnterChannel = 6; // 过去30天进过的房间
	MtManyUsersInChannel = 7; // 超多人围观
	MtManyUsersFollowChannelOwner = 8; // 很多人关注房主
	MtGuessYouLikeChannelOwner = 9; // 最近关注人的相似房主推荐
	MtRecentLikeTabChannelDouDi = 10; // 最近进过房间同玩法兜底
	MtHotGameHotChannelDouDi = 11; // 热聊挑战上榜房间兜底
	MtFriendOfFriend = 12; // 好友的好友
	MtRecentFollow = 13; // 最近关注
}

message ChannelInfo {
    uint32 tag_id                  = 1; // 娱乐房tag id（区别于游戏卡片tag id）
    uint32 recall_flag             = 2; // 召回标识
    rcmd.common.LocationInfo loc   = 3;
    repeated RCMDLabel rcmd_labels = 4; // 推荐标签
    LocShowType loc_show_type = 5; // 建议显示的IP信息
    enum LocShowType {
        LocShowType_DEFAULT = 0; // 默认
        LocShowType_PROVINCE = 2; // 建议显示省份IP位置信息
        LocShowType_CITY = 3; // 建议显示城市IP位置信息
    }
    bool is_new_user_undertake = 6; // 是否萌新承接房
    repeated uint32 follow_uid_list  = 7; // 在房关注用户uid，去除房主
    repeated uint32 play_uid_list = 8; // 在房一起玩过用户uid
    repeated uint32 follow_uid_list_v2 = 9; // 新版首页在房关注用户uid
    bool is_ever_enter = 10; // 是否为过去停留时长>x的房间
    string delivery_type = 11; // 下发的房间类型
}

message GetRecommendationListResp {
    repeated uint32 channel_id                = 1; // 下发的房间列表
    bool bottom_reached                       = 2; // 是否已经到底了，即下一页将没有数据
    map<uint32, ChannelInfo> channel_info_map = 3;
    string trace_id                           = 4;
    map<string,string> debug_info_map         = 5; //测试打点信息
    rcmd.common.LocationInfo self_loc         = 6;

    enum NotifyType{
        DEFAULT = 0;
        APPOINTMENT = 1;
        RefreshSucc = 2;
    }
    repeated NotifyType notify_list = 9; // 通知事件
    string footprint = 10;// 推荐trace id
    BaseRCMDResp base_resp = 11; //基础的resp
}

message BaseRCMDResp {
    bool ok = 1;
    uint64 code = 2;
    string msg = 3;
    enum PlanBStrategy{
        PlanBStrategy_INVALID = 0; // TT业务兜底
        PlanBStrategy_TT = 1; // TT业务兜底
        PlanBStrategy_RANDOM = 2; // 随机兜底
    }
    PlanBStrategy plan_b_strategy = 4; // 兜底策略, ok=false时，才会使用该字段
    bool is_algo = 5; // 是否走算法版
}

message GetRecommendationReasonReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

enum RCMDReason {
    Reason_None = 0;
    Reason_HotGameTop = 1; // 热聊挑战前分位
}

message GetRecommendationReasonResp {
    uint32 reason = 1; 
}