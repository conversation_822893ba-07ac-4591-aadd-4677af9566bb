syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channelbox";
package channelbox;


service ChannelBox {
    rpc EnterBox (EnterBoxReq) returns (EnterBoxResp) {}
    rpc ExitBox (ExitBoxReq) returns (ExitBoxResp) {}
    rpc JoinBox (JoinBoxReq) returns (JoinBoxResp) {}
    rpc GetBoxInfo (GetBoxInfoReq) returns (GetBoxInfoResp) {}
    rpc ClearJoinBox (ClearJoinBoxReq) returns (ClearJoinBoxResp) {}
    rpc BatchGetBoxInfos (BatchGetBoxInfosReq) returns (BatchGetBoxInfosResp) {}
    rpc GetBoxInfosByLimit (GetBoxInfosByLimitReq) returns (GetBoxInfosByLimitResp) {}
    rpc GetBoxUserInMicInfos (GetBoxUserInMicInfosReq) returns (GetBoxUserInMicInfosResp) {}
    rpc ExitChannel (ExitChannelReq) returns (ExitChannelResp) {}

    rpc UpsertBoxInfo (UpsertBoxInfoReq) returns (UpsertBoxInfoResp) {}
    rpc DelBoxInfo (DelBoxInfoReq) returns (DelBoxInfoResp) {}
    rpc ApplyOnMicToken (ApplyOnMicTokenReq) returns (ApplyOnMicTokenResp) {}
    rpc SetChannelMicBoxId (SetChannelMicBoxIdReq) returns (SetChannelMicBoxIdResp) {}
    
    // 麦位申请记录
    rpc SetMicApplyRecords(SetMicApplyRecordsReq) returns (SetMicApplyRecordsResp) {}
    rpc GetMicApplyRecords(GetMicApplyRecordsReq) returns (GetMicApplyRecordsResp) {}
}

enum BoxType {
    BoxTypeRoleplay = 0; // 角色扮演
    BoxTypeMelee = 1; // 团战
}

message BoxInfo {
    uint32 channel_id = 1;
    uint32 box_id = 2;
    uint32 creator_uid = 3;
    BoxType box_type = 4;
    string box_name = 5;
    uint32 mic_cap = 6;
    repeated uint32 public_mic_list = 7;
    repeated uint32 normal_mic_list = 8;
}

message EnterBoxReq {
    uint32 uid = 1;
    uint32 channelid = 2;
    uint32 boxid = 3;
    BoxType box_type = 4;
    uint32 common_boxid = 5;
    uint32 broadcast_boxid = 6;
}

message EnterBoxResp {
    bool is_switch = 1;
    repeated EnterBoxInfo enter_box_infos = 2;
    uint64 opts = 3;    //进出包厢的毫秒时间戳
    uint32 channelid = 4;
}

enum EnterResultBoxType {
    EnterType = 0;
    SwitchType = 1;
}

message EnterBoxInfo {
    EnterResultBoxType box_type = 1;
    uint32 boxid = 2;
    uint32 box_cnt = 3;
    repeated uint32 audio_box_ids = 4;
}

message ExitBoxReq {
    uint32 uid = 1;
    uint32 channelid = 2;
    uint32 boxid = 3;
    BoxType box_type = 4;
    uint32 common_boxid = 5;
    uint32 broadcast_boxid = 6;
}

message ExitBoxResp {
    uint32 box_cnt = 1;
    uint64 opts = 2;    //进出包厢的毫秒时间戳
}

enum JoinBoxType {
    NoneType = 0;
    AcceptType = 1;
    RejectType = 2;
}

message JoinBoxReq {
    uint32 uid = 1;
    uint32 channelid = 2;
    uint32 boxid = 3;
    JoinBoxType join_box_type = 4;
}

message JoinBoxResp {
    bool is_switch = 1;
    repeated EnterBoxInfo enter_box_infos = 2;
    uint64 opts = 3;    //进出包厢的毫秒时间戳
    uint32 channelid = 4;
    bool is_ignore = 5;
}

message BoxBaseInfo {
    uint32 boxid = 1;
    uint32 uid = 2;
    uint64 opts = 3;    //进出包厢的毫秒时间戳
    repeated uint32 audio_boxids = 4; //音频特殊频道列表
}


message BoxCntInfo {
    uint32 boxid = 1;
    uint32 box_user_cnt = 2; //包厢全量人数
    uint32 channelid = 3;
}



//客户端协议
message GetBoxInfoReq {
    uint32 ope_uid = 1;  //客户端用的uid
    uint32 channelid = 2;
    BoxType box_type = 3;
}

message GetBoxInfoResp {
    repeated BoxCntInfo box_info = 1;
    uint32 user_boxid = 2;
    repeated BoxInfo box_list = 3;
    uint32 mic_total = 4;
    repeated uint32 user_audio_boxids = 5;
    uint32 main_common_boxid = 6;
}

message ClearJoinBoxReq {
    uint32 uid = 1;
    uint32 channelid = 2;
    uint32 boxid = 3;
    BoxType box_type = 4;
}

message ClearJoinBoxResp {
}

message BatchGetBoxInfosReq {
    repeated uint32 uid_list = 1;
    uint32 channelid = 2;
    BoxType box_type = 3;
}

message BatchGetBoxInfosResp {
    repeated BoxBaseInfo box_base_infos = 1;
}

message GetBoxInfosByLimitReq {
    uint32 channelid = 1;
    uint32 boxid = 2;
    int64 last_time = 3;
    int64 get_cnt = 4;
    BoxType box_type = 5;
}

message GetBoxInfosByLimitResp {
    repeated BoxBaseInfo box_base_infos = 1;
}

message GetBoxUserInMicInfosReq {
    uint32 channelid = 1;
    uint32 boxid = 2;
    repeated uint32 mic_uids = 3;
    BoxType box_type = 4;
}

message GetBoxUserInMicInfosResp {
    repeated uint32 box_uids = 1;
}

message ExitChannelReq {
    uint32 uid = 1;
    uint32 channelid = 2;
    BoxType box_type = 3;
}

message ExitChannelResp {
    uint32 box_cnt = 1;
    uint64 opts = 2;    //进出包厢的毫秒时间戳
    uint32 box_id = 3;
    BoxType box_type = 4;
}

message UpsertBoxInfoReq {
	BoxInfo box = 1;
}

message UpsertBoxInfoResp {
    BoxInfo box = 1;
}

message DelBoxInfoReq {
    uint32 channel_id = 1;
    uint32 box_id = 2;
    BoxType box_type = 3;
}

message DelBoxInfoResp {
    // 房间内剩余子频道数量
    uint32 box_num = 1;
}

message ApplyOnMicTokenReq{
  uint32 channel_id = 1;
  uint32 mic_id = 2; //子频道公共麦带上，其它自由麦传0
}

message ApplyOnMicTokenResp{
  string on_mic_token = 1;
  uint32 mic_id = 2;
}

message SetChannelMicBoxIdReq{
  uint32 channel_id = 1;
  uint32 open_mic_box_id = 2;
  uint32 ope_uid = 3;
}

message SetChannelMicBoxIdResp{
    int64 opts = 1;
}

enum MicType {
    // 自由麦
    MicTypeFree = 0;
    // 公共麦
    MicTypePublic = 1;
}

// 麦位状态
enum MicStatus {
    // 申请中
    MicStatusApplying = 0;
    // 试用中
    MicStatusUsing = 1;
}

// 1000以内系统频道ID
enum SystemBoxID {
    SystemBoxIDInvalid = 0;
    // 子频道公共频道
    SystemBoxIDSubChannel = 2;
    // 主房间公共频道
    SystemBoxIDMainChannel = 3;
    // 广播频道
    SystemBoxIDBroadcast = 4;
}

// 麦位申请记录
message MicApplyRecord {
    uint32 mic_id = 1;
    MicType mic_type = 2;
    MicStatus mic_status = 3;

    uint32 uid = 4;
    uint32 box_id = 5;
    uint32 update_at = 6;
}

message SetMicApplyRecordsReq {
    uint32 channel_id = 1;
    repeated MicApplyRecord records = 2;
}

message SetMicApplyRecordsResp {
}

message GetMicApplyRecordsReq {
    uint32 channel_id = 1;
}

message GetMicApplyRecordsResp {
    repeated MicApplyRecord records = 1;
}
