syntax = "proto3";

package game_pay_http_logic;

import "google/api/annotations.proto";

option go_package = "golang.52tt.com/protocol/services/game-pay-http-logic";

service GamePayHttpLogic {
    // PC开黑加速器回调接口
    rpc AcceleratorPayTest(AcceleratorPayTestReq) returns (AcceleratorPayTestResp) {
        option (google.api.http) = {
            post: "/game-pay-http-logic/acceleratorPayTest"
        };
    }
}

message AcceleratorPayTestReq {
    string message = 1; // 请求消息
    int32 code = 2; // 请求码
    string data = 3; // 请求数据
    string sign = 4; // 签名
    string timestamp = 5; // 时间戳
    string nonce = 6; // 随机数
    string appid = 7; // 应用ID
}

message AcceleratorPayTestResp {
    string message = 1; // 响应消息
    int32 code = 2; // 响应码
    string data = 3; // 响应数据
}