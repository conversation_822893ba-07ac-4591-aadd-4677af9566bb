syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "abtest/abtest_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/abtest-logic";

service AbtestLogic {
    option (logic.gateway.service_ext) = {
        service_name: "abtest-logic"
    };

    rpc AbtestExpSync (ga.abtest.AbtestExpSyncReq) returns (ga.abtest.AbtestExpSyncResp) {
        option (logic.gateway.command) = {
			id: 30125
		};
    }
}
