syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/ad-center-logic";
import "ad_center/ad-center-logic_.proto";
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";

service AdCenterLogic {
    option (logic.gateway.service_ext) = {
        service_name: "ad-center-logic"
    };

    rpc BatchGetAd (ga.ad_center.BatchGetAdReq) returns (ga.ad_center.BatchGetAdResp) {
        option (logic.gateway.command) = {
            id: 88888
        };
    }
    rpc CheckTagIdMate (ga.ad_center.CheckTagIdMateReq) returns (ga.ad_center.CheckTagIdMateResp) {
        option (logic.gateway.command) = {
            id: 30490
        };
    }
    rpc CommitAdExposure (ga.ad_center.CommitAdExposureReq) returns (ga.ad_center.CommitAdExposureResp) {
        option (logic.gateway.command) = {
            id: 30491
        };
    }
    rpc CommitAdClick (ga.ad_center.CommitAdClickReq) returns (ga.ad_center.CommitAdClickResp) {
        option (logic.gateway.command) = {
            id: 30492
        };
    }
}