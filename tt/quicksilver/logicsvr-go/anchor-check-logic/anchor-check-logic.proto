syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto"; //-I=golang.52tt.com/third-party/tt-protocol/service/quicksilver
import "anchor_check_logic/anchor-check-logic_.proto";  //-I=golang.52tt.com/third-party/tt-protocol

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/anchor-check-logic";

service AnchorCheckLogic {
  option (logic.gateway.service_ext) = {
    service_name: "anchor-check-logic"
  };

  //检查是否显示主播考核按钮
  rpc CheckShowBtn(ga.anchor_check_logic.CheckShowBtnReq) returns (ga.anchor_check_logic.CheckShowBtnResp) {
    option (logic.gateway.command) = {
      id: 31630
    };
  }

  //开始录制
  rpc StartRecord(ga.anchor_check_logic.StartRecordReq) returns (ga.anchor_check_logic.StartRecordResp) {
    option (logic.gateway.command) = {
      id: 31631
    };
  }

  //结束录制
  rpc StopRecord(ga.anchor_check_logic.StopRecordReq) returns (ga.anchor_check_logic.StopRecordResp) {
    option (logic.gateway.command) = {
      id: 31632
    };
  }

   //娱乐厅从业者中心入口
  rpc GetMultiPlayerCenterEntry(ga.anchor_check_logic.GetMultiPlayerCenterEntryReq) returns (ga.anchor_check_logic.GetMultiPlayerCenterEntryResp) {
    option (logic.gateway.command) = {
      id: 32008
    };
  }

  //直播间主播任务入口
  rpc GetLiveAnchorTaskEntry(ga.anchor_check_logic.GetLiveAnchorTaskEntryReq) returns (ga.anchor_check_logic.GetLiveAnchorTaskEntryResp) {
    option (logic.gateway.command) = {
      id: 32009
    };
  }
}