syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/auth-logic";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "auth/auth.proto";
import "game/game_.proto";
import "im_activity_center_logic/im-activity-center-logic_.proto";

service AuthLogic {
    option (logic.gateway.service_ext) = {
        service_name: "auth-logic"
    };
  
    rpc Auth(ga.auth.AuthReq) returns (ga.auth.AuthResp) {
        option (logic.gateway.command) = {
            id: 10
        };
    }

    rpc Reg(ga.auth.RegReq) returns (ga.auth.RegResp) {
        option (logic.gateway.command) = {
            id: 12
        };
    }
  
    rpc ThirdPartyAuth(ga.auth.ThirdPartyAuthReq) returns (ga.auth.ThirdPartyAuthResp) {
        option (logic.gateway.command) = {
            id: 210
        };
    }

    rpc ThirdPartyReg(ga.auth.ThirdPartyRegReq) returns (ga.auth.ThirdPartyRegResp) {
        option (logic.gateway.command) = {
            id: 211
        };
    }

    rpc AccountVerifyCode(ga.auth.AccountVerifyCodeReq) returns (ga.auth.AccountVerifyCodeResp) {
        option (logic.gateway.command) = {
            id: 14
        };
    }

    rpc AccountVoiceVerifyCode(ga.auth.AccountVoiceVerifyCodeReq) returns (ga.auth.AccountVoiceVerifyCodeResp) {
        option (logic.gateway.command) = {
            id: 5050
        };
    }

    rpc SubmitVerifyCode(ga.auth.SubmitVerifyCodeReq) returns (ga.auth.SubmitVerifyCodeResp) {
        option (logic.gateway.command) = {
            id: 232
        };
    }

    rpc ThirdpartyVerifyCheck(ga.auth.ThridpartVerifyCheckReq) returns (ga.auth.ThridpartVerifyCheckResp) {
        option (logic.gateway.command) = {
            id: 214
        };
    }

    rpc BindPhoneBeforeAuth(ga.auth.BindPhoneBeforeAuthReq) returns (ga.auth.BindPhoneBeforeAuthResp) {
        option (logic.gateway.command) = {
            id: 404
        };
    }

    rpc BindPhoneAfterAuth(ga.auth.BindPhoneAfterAuthReq) returns (ga.auth.BindPhoneAfterAuthResp) {
        option (logic.gateway.command) = {
            id: 405
        };
    }

    rpc GetPcAuthApplyResult(ga.auth.GetPcAuthApplyResultReq) returns (ga.auth.GetPcAuthApplyResultResp) {
        option (logic.gateway.command) = {
            id: 6600
        };
    }
  
    rpc GetPcAuthApply(ga.auth.GetPcAuthApplyReq) returns (ga.auth.GetPcAuthApplyResp) {
        option (logic.gateway.command) = {
            id: 6601
        };
    }
  
    rpc ProcPcAuthApply(ga.auth.ProcPcAuthApplyReq) returns (ga.auth.ProcPcAuthApplyResp) {
        option (logic.gateway.command) = {
            id: 6602
        };
    }

    rpc GetPcAuthApplyResultV2(ga.auth.GetPcAuthApplyResultReq) returns (ga.auth.GetPcAuthApplyResultResp) {
        option (logic.gateway.command) = {
            id: 6603
        };
    }
    
    // 接管废弃命令，踢下线
    rpc GetGameConfig(ga.game.GetGameConfigReq) returns (ga.game.GetGameConfigResp) {
        option (logic.gateway.command) = {
            id: 166
        };
    }

    // 接管废弃命令，踢下线
    rpc ImActivityCenterEntrance(ga.im_activity_center_logic.ImActivityCenterEntranceReq) returns (ga.im_activity_center_logic.ImActivityCenterEntranceResp) {
        option (logic.gateway.command) = {
            id: 30380
        };
    }
}
