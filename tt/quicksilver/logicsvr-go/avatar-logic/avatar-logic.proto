syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";

import "avatar_logic/avatar-logic_.proto";
import "face/face.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/avatar-logic";

service AvatarLogic {
    option (logic.gateway.service_ext) = {
        service_name: "avatar-logic"
    };
    rpc UploadAvatar (ga.avatar_logic.Avatar_UploadAvatarReq) returns (ga.avatar_logic.Avatar_UploadAvatarResp) {
        option (logic.gateway.command) = {
            id: 20020   //改成正确的id
        };
    }
    rpc BatchGetSmallFaceUrl(ga.face.BatchGetSmallFaceUrlReq) returns (ga.face.BatchGetSmallFaceUrlResp) {
        option (logic.gateway.command) = {
            id: 600
        };
    }
    rpc GetBigFace(ga.face.GetBigFaceReq) returns (ga.face.GetBigFaceResp) {
        option (logic.gateway.command) = {
            id: 22
        };
    }
    rpc GetSmallFace(ga.face.GetSmallFaceReq) returns (ga.face.GetSmallFaceResp) {
        option (logic.gateway.command) = {
            id: 21
        };
    }
    rpc UploadFace(ga.face.UserUploadFaceReq) returns (ga.face.UserUploadFaceResp) {
        option (logic.gateway.command) = {
            id: 20
        };
    }
}

