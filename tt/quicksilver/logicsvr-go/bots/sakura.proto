syntax = "proto3";

package logic.bots;

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/bots";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "auth/auth.proto";

service Sakura {
    option (logic.gateway.service_ext) = {
        service_name: "sakura-bot"
    };

    // 上报日志结果
    rpc UploadLog (ga.auth.UploadLogReq) returns (ga.auth.UploadLogResp) {
        option (logic.gateway.command) = {
            id: 163
        };
    }
}
