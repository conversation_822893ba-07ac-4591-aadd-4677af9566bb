syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "cat_canteen_logic/cat_canteen_logic.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/cat-canteen-logic";

service CatCanteenLogic {
  option (logic.gateway.service_ext) = {
    service_name: "cat-canteen-logic"
  };

  // 获取权限及提醒
  rpc GetChanceGameAccessNotifyInfo(ga.cat_canteen_logic.GetChanceGameAccessNotifyInfoRequest) returns (ga.cat_canteen_logic.GetChanceGameAccessNotifyInfoResponse) {
    option (logic.gateway.command) = {
      id: 36571
    };
  }

  // 开始经营（抽奖）
  rpc LotteryDraw(ga.cat_canteen_logic.LotteryDrawRequest) returns (ga.cat_canteen_logic.LotteryDrawResponse) {
    option (logic.gateway.command) = {
      id: 36572
    };
  }

  // 购买鱼干（抽奖机会）
  rpc BuyChance(ga.cat_canteen_logic.BuyChanceRequest) returns (ga.cat_canteen_logic.BuyChanceResponse) {
    option (logic.gateway.command) = {
      id: 36573
    };
  }

  // 获游戏玩法信息
  rpc GetGameInfo(ga.cat_canteen_logic.GetGameInfoRequest) returns (ga.cat_canteen_logic.GetGameInfoResponse) {
    option (logic.gateway.command) = {
      id: 36574
    };
  }

  // 获取用户中奖纪录
  rpc GetWinningRecords(ga.cat_canteen_logic.GetWinningRecordsRequest) returns (ga.cat_canteen_logic.GetWinningRecordsResponse) {
    option (logic.gateway.command) = {
      id: 36575
    };
  }

  // 获取全平台最新的中奖纪录
  rpc GetRecentWinningRecords(ga.cat_canteen_logic.GetRecentWinningRecordsRequest) returns (ga.cat_canteen_logic.GetRecentWinningRecordsResponse) {
    option (logic.gateway.command) = {
      id: 36576
    };
  }

  // 获取用户游戏存档信息
  rpc GetUserPlayFile(ga.cat_canteen_logic.GetUserPlayFileRequest) returns (ga.cat_canteen_logic.GetUserPlayFileResponse) {
    option (logic.gateway.command) = {
      id: 36577
    };
  }

  //获取用户道具(凭证+小鱼干)列表
  rpc GetUserCatPropList(ga.cat_canteen_logic.GetUserCatPropListRequest) returns (ga.cat_canteen_logic.GetUserCatPropListResponse){
    option (logic.gateway.command) = {
      id: 36578
    };
  }

  //获取用户即将过期道具(凭证+小鱼干)提醒
  rpc GetUserExpireCatPropNotify(ga.cat_canteen_logic.GetUserExpireCatPropNotifyRequest) returns (ga.cat_canteen_logic.GetUserExpireCatPropNotifyResponse){
    option (logic.gateway.command) = {
      id: 36579
    };
  }

  // 获取资源配置
  rpc GetCatCanteenResource(ga.cat_canteen_logic.GetCatCanteenResourceRequest) returns (ga.cat_canteen_logic.GetCatCanteenResourceResponse){
    option (logic.gateway.command) = {
      id: 36580
    };
  }

}
