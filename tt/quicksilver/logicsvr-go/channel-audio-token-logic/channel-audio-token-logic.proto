syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-audio-token-logic";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_audio_token/channel-audio-token_.proto";

service ChannelAudioTokenLogic {
  option (logic.gateway.service_ext) = {
    service_name: "channel-audio-token-logic"
  };

  rpc GetChannelAudioToken(ga.channel_audio_token.GetChannelAudioTokenReq) returns (ga.channel_audio_token.GetChannelAudioTokenResp) {
    option (logic.gateway.command) = {
      id: 50851
    };
  }
}