syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-core-logic";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel/channel_.proto";
import "channel_core/channel_core.proto";

service ChannelCoreLogic {
  option (logic.gateway.service_ext) = {
    service_name: "channel-core-logic"
  };

  rpc ChannelEnter(ga.channel.ChannelEnterReq) returns (ga.channel.ChannelEnterResp) {
    option (logic.gateway.command) = {
      id: 423
    };
  }

  rpc ChannelQuit(ga.channel.ChannelQuitReq) returns (ga.channel.ChannelQuitResp) {
    option (logic.gateway.command) = {
      id: 424
    };
  }

  rpc ChannelMicHold(ga.channel.ChannelGetMicReq) returns (ga.channel.ChannelGetMicResp) {
    option (logic.gateway.command) = {
    id: 430
    };
  }

  rpc ChannelMicRelease(ga.channel.ChannelReleaseMicReq) returns (ga.channel.ChannelReleaseMicResp) {
    option (logic.gateway.command) = {
    id: 431
    };
  }

  rpc ChannelMicTakeHold(ga.channel.TakeUserToChannelMicReq) returns (ga.channel.TakeUserToChannelMicResp) {
    option (logic.gateway.command) = {
    id: 2069
    };
  }

  rpc ChannelEnterV2(ga.channel_core.ChannelEnterRequest) returns (ga.channel_core.ChannelEnterResponse) {
    option (logic.gateway.command) = {
    id: 50858
    };
  }
}