syntax = "proto3";

package logic.channel_cp_game_logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_cp_game_logic/channel-cp-game-logic_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-cp-game-logic";

service ChannelCpGameLogic {
  option (logic.gateway.service_ext) = {
    service_name: "channel-cp-game-logic"
  };

  rpc CheckChannelCpGameEntry (ga.channel_cp_game_logic.CheckChannelCpGameEntryReq) returns (ga.channel_cp_game_logic.CheckChannelCpGameEntryResp) {
    option (logic.gateway.command) = {
      id: 30751   //改成正确的id
    };
  }

  rpc SetCpGamePhase (ga.channel_cp_game_logic.SetCpGamePhaseReq) returns (ga.channel_cp_game_logic.SetCpGamePhaseResp) {
    option (logic.gateway.command) = {
      id: 30752   //改成正确的id
    };
  }

  rpc AddCpGamePhaseEndTime (ga.channel_cp_game_logic.AddCpGamePhaseEndTimeReq) returns (ga.channel_cp_game_logic.AddCpGamePhaseEndTimeResp) {
    option (logic.gateway.command) = {
      id: 30753   //改成正确的id
    };
  }

  rpc GetCurrCpGameInfo (ga.channel_cp_game_logic.GetCurrCpGameInfoReq) returns (ga.channel_cp_game_logic.GetCurrCpGameInfoResp) {
    option (logic.gateway.command) = {
      id: 30754   //改成正确的id
    };
  }
  rpc MvpAutoHoldMic (ga.channel_cp_game_logic.MvpAutoHoldMicReq) returns (ga.channel_cp_game_logic.MvpAutoHoldMicResp) {
    option (logic.gateway.command) = {
      id: 30755   //改成正确的id
    };
  }
  rpc ChoseCpRare (ga.channel_cp_game_logic.ChoseCpRareReq) returns (ga.channel_cp_game_logic.ChoseCpRareResp) {
    option (logic.gateway.command) = {
      id: 30756 // 改成正确的id
    };
  }
}