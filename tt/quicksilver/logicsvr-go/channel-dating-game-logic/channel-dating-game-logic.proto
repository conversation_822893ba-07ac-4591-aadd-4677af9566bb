syntax = "proto3";

package channeldatinggame;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel/channel_dating_game_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-daging-game-logic";

service ChannelDatingGameLogic {
  option (logic.gateway.service_ext) = {
    service_name: "channel-daging-game-logic"
  };
  
  rpc SelectLikeDatingUser (ga.channel.SelectLikeDatingUserReq) returns (ga.channel.SelectLikeDatingUserResp) {
    option (logic.gateway.command) = {
      id: 2431   //改成正确的id
      deprecated: true
    };
  }

  rpc OpenLikeDatingUser (ga.channel.OpenLikeDatingUserReq) returns (ga.channel.OpenLikeDatingUserResp) {
    option (logic.gateway.command) = {
      id: 2432   //改成正确的id
      deprecated: true
    };
  }

  rpc ApplyDatingMic (ga.channel.ApplyDatingMicReq) returns (ga.channel.ApplyDatingMicResp) {
    option (logic.gateway.command) = {
      id: 2434   //改成正确的id
      deprecated: true
    };
  }

  rpc GetApplyDatingMicUserList (ga.channel.GetApplyDatingMicUserListReq) returns (ga.channel.GetApplyDatingMicUserListResp) {
    option (logic.gateway.command) = {
      id: 2435   //改成正确的id
      deprecated: true
    };
  }

 
  rpc SetChannelDatingGamePhase (ga.channel.SetDatingGamePhaseReq) returns (ga.channel.SetDatingGamePhaseResp) {
    option (logic.gateway.command) = {
      id: 2436   //改成正确的id
      deprecated: true
    };
  }

  rpc CheckChannelDatingGameEntry (ga.channel.CheckChannelDatingGameEntryReq) returns (ga.channel.CheckChannelDatingGameEntryResp) {
    option (logic.gateway.command) = {
      id: 2437   //改成正确的id
      deprecated: true
    };
  }

  rpc DatingGameHoldVipMic (ga.channel.DatingGameHoldVipMicReq) returns (ga.channel.DatingGameHoldVipMicResp) {
    option (logic.gateway.command) = {
      id: 2438   //改成正确的id
      deprecated: true
    };
  }

  rpc GetDatingGameInitInfo (ga.channel.GetDatingGameInitInfoReq) returns (ga.channel.GetDatingGameInitInfoResp) {
    option (logic.gateway.command) = {
      id: 2439   //改成正确的id
      deprecated: true
    };
  }
}