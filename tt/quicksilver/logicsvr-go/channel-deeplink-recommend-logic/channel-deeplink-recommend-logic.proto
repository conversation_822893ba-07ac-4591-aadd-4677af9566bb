syntax = "proto3";

package logic.channel_deeplink_recommend_logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_deeplink_recommend_logic/channel-deeplink-recommend-logic_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-deeplink-recommend-logic";

service ChannelDeeplinkRecommendLogic {
  option (logic.gateway.service_ext) = {
    service_name: "channel-deeplink-recommend-logic"
  };

  rpc GetRecommendChannelByRules (ga.channel_deeplink_recommend_logic.GetRecommendChannelByRulesReq) returns (ga.channel_deeplink_recommend_logic.GetRecommendChannelByRulesResp) {
    option (logic.gateway.command) = {
      id: 30400   //改成正确的id
    };
  }

  rpc GetRecommendBusinessChannel (ga.channel_deeplink_recommend_logic.GetRecommendBusinessChannelReq) returns (ga.channel_deeplink_recommend_logic.GetRecommendBusinessChannelResp) {
    option (logic.gateway.command) = {
      id: 30401   //改成正确的id
    };
  }
}