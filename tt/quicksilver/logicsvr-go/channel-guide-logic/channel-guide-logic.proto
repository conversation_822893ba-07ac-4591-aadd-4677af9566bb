syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_guide_logic/channel-guide-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-guide-logic";

service ChannelGuideLogic {
  option (logic.gateway.service_ext) = {
    service_name: "channel-guide-logic"
  };

  rpc GetGangUpConf (ga.channel_guide_logic.GetGangUpConfReq) returns (ga.channel_guide_logic.GetGangUpConfRsp) {
    option (logic.gateway.command) = {
      id: 102001
    };
  }

  rpc Appoinment (ga.channel_guide_logic.AppoinmentReq) returns (ga.channel_guide_logic.AppoinmentRsp) {
    option (logic.gateway.command) = {
      id: 102002
    };
  }
  rpc JoinCarTeam (ga.channel_guide_logic.JoinCarTeamReq) returns (ga.channel_guide_logic.JoinCarTeamRsp) {
    option (logic.gateway.command) = {
      id: 102003
    };
  }

  rpc AppStatusReport (ga.channel_guide_logic.AppStatusReportReq) returns (ga.channel_guide_logic.AppStatusReportRsp) {
    option (logic.gateway.command) = {
      id: 102004
    };
  }

  rpc CloseGameGroupGuide (ga.channel_guide_logic.CloseGameGroupGuideReq) returns (ga.channel_guide_logic.CloseGameGroupGuideRsp) {
    option (logic.gateway.command) = {
      id: 102005
    };
  }

  rpc GetTeamFightGuide (ga.channel_guide_logic.GetTeamFightGuideReq) returns (ga.channel_guide_logic.GetTeamFightGuideRsp){
    option (logic.gateway.command) = {
      id: 50051
    };
  }
}