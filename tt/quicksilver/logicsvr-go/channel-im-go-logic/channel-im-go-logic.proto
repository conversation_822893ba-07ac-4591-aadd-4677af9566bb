syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-im-go-logic";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel/channel_.proto";
import "channel_im/channel_im.proto";

service ChannelImGoLogic {
  option (logic.gateway.service_ext) = {
    service_name: "channel-im-go-logic"
  };

  rpc SendChannelTextMsg(ga.channel.SendChannelTextMsgReq) returns (ga.channel.SendChannelTextMsgResp) {
    option (logic.gateway.command) = {
      id: 434
    };
  }

  rpc GetChannelMsg(ga.channel.GetChannelMsgReq) returns (ga.channel.GetChannelMsgResp) {
    option (logic.gateway.command) = {
      id: 435
    };
  }

  rpc SendChannelAttachmentMsg(ga.channel.SendChannelAttachmentMsgReq) returns (ga.channel.SendChannelAttachmentMsgResp) {
    option (logic.gateway.command) = {
      id: 456
    };
  }

  rpc DownloadChannelMsgAttachment(ga.channel.DownloadChannelMsgAttachmentReq) returns (ga.channel.DownloadChannelMsgAttachmentResp) {
    option (logic.gateway.command) = {
      id: 457
    };
  }
  rpc GetChannelReliableMsg(ga.channel_im.GetChannelReliableMsgRequest) returns (ga.channel_im.GetChannelReliableMsgResponse) {
    option (logic.gateway.command) = {
      id: 50869
    };
  }
}
