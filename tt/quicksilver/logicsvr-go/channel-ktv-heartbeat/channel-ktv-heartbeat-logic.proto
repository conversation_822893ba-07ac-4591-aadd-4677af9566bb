syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-ktv-heartbeat-logic";
import "channel_ktv_heartbeat/channel-ktv-heartbeat-logic_.proto";
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";

service ChannelKTVHeartbeatLogic {
  option (logic.gateway.service_ext) = {
    service_name: "channel-ktv-heartbeat-logic"
  };
  rpc ConfirmKTVHeartBeat(ga.channel_ktv_heartbeat.ConfirmKTVHeartBeatReq) returns (ga.channel_ktv_heartbeat.ConfirmKTVHeartBeatResp) {
    option (logic.gateway.command) = {
       id: 30987
    };
  }
  rpc ReportKTVHeartbeat (ga.channel_ktv_heartbeat.ReportKTVHeartbeatReq) returns (ga.channel_ktv_heartbeat.ReportKTVHeartbeatResp) {
    option (logic.gateway.command) = {
      id: 30988
    };
  }
}