// 文档地址： https://q9jvw0u5f5.feishu.cn/docs/doccnk6zbA5gNsbKmrhUAGDU8yL#
syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-ktv-logic";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "channel_ktv/channel-ktv-logic_.proto";
import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";

service ChannelKTVLogic {
  option (logic.gateway.service_ext) = {
    service_name: "channel-ktv-logic"
  };

  rpc GetChannelKTVSongList(ga.channel_ktv.GetChannelKTVSongListReq) returns (ga.channel_ktv.GetChannelKTVSongListResp) {
    option (logic.gateway.command) = {
      id: 30971
    };
  }

  rpc GetChannelKTVHistoryList(ga.channel_ktv.GetChannelKTVHistoryListReq) returns (ga.channel_ktv.GetChannelKTVHistoryListResp) {
    option (logic.gateway.command) = {
      id: 30972
    };
  }

  rpc GetChannelKTVRecommendList(ga.channel_ktv.GetChannelKTVRecommendListReq) returns (ga.channel_ktv.GetChannelKTVRecommendListResp) {
    option (logic.gateway.command) = {
      id: 30973
    };
  }

  rpc GetChannelKTVPlayList(ga.channel_ktv.GetChannelKTVPlayListReq) returns (ga.channel_ktv.GetChannelKTVPlayListResp) {
    option (logic.gateway.command) = {
      id: 30974
    };
  }

  rpc GetChannelKTVGuessLikeSongList(ga.channel_ktv.GetChannelKTVGuessLikeSongListReq) returns (ga.channel_ktv.GetChannelKTVGuessLikeSongListResp) {
    option (logic.gateway.command) = {
      id: 30975
    };
  }

  rpc AddChannelKTVSongToPlayList(ga.channel_ktv.AddChannelKTVSongToPlayListReq) returns (ga.channel_ktv.AddChannelKTVSongToPlayListResp) {
    option (logic.gateway.command) = {
      id: 30976
    };
  }

  rpc MoveUpChannelKTVSong(ga.channel_ktv.MoveUpChannelKTVSongReq) returns (ga.channel_ktv.MoveUpChannelKTVSongResp) {
    option (logic.gateway.command) = {
      id: 30977
    };
  }

  rpc RemoveChannelKTVSong(ga.channel_ktv.RemoveChannelKTVSongReq) returns (ga.channel_ktv.RemoveChannelKTVSongResp) {
    option (logic.gateway.command) = {
      id: 30978
    };
  }

  rpc BeginChannelKTVSing(ga.channel_ktv.BeginChannelKTVSingReq) returns (ga.channel_ktv.BeginChannelKTVSingResp) {
    option (logic.gateway.command) = {
      id: 30979
    };
  }

  rpc GetChannelKTVInfo(ga.channel_ktv.GetChannelKTVInfoReq) returns (ga.channel_ktv.GetChannelKTVInfoResp) {
    option (logic.gateway.command) = {
      id: 30980
    };
  }

  rpc JoinChannelKTVSing(ga.channel_ktv.JoinChannelKTVSingReq) returns (ga.channel_ktv.JoinChannelKTVSingResp) {
    option (logic.gateway.command) = {
      id: 30981
    };
  }

  rpc QuitChannelKTVSing(ga.channel_ktv.QuitChannelKTVSingReq) returns (ga.channel_ktv.QuitChannelKTVSingResp) {
    option (logic.gateway.command) = {
      id: 30982
    };
  }

  rpc UpdateChannelKTVScore(ga.channel_ktv.UpdateChannelKTVScoreReq) returns (ga.channel_ktv.UpdateChannelKTVScoreResp) {
    option (logic.gateway.command) = {
      id: 30983
    };
  }

  rpc SwitchChannelKTVBG(ga.channel_ktv.SwitchChannelKTVBGReq) returns (ga.channel_ktv.SwitchChannelKTVBGResp) {
    option (logic.gateway.command) = {
      id: 30984
    };
  }

  rpc ChannelKTVHandClap(ga.channel_ktv.ChannelKTVHandClapReq) returns (ga.channel_ktv.ChannelKTVHandClapResp) {
    option (logic.gateway.command) = {
      id: 30985
    };
  }

  rpc EndChannelKTVSing(ga.channel_ktv.EndChannelKTVSingReq) returns (ga.channel_ktv.EndChannelKTVSingResp) {
    option (logic.gateway.command) = {
      id: 30986
    };
  }

  rpc KickOutKTVMember(ga.channel_ktv.KickOutKTVMemberReq) returns (ga.channel_ktv.KickOutKTVMemberResp) {
    option (logic.gateway.command) = {
      id: 30989
    };
  }
  rpc CutChannelKTVSong(ga.channel_ktv.CutChannelKTVSongReq) returns (ga.channel_ktv.CutChannelKTVSongResp) {
    option (logic.gateway.command) = {
      id: 30990
    };
  }

  rpc GetChannelKTVBG(ga.channel_ktv.GetChannelKTVBGReq) returns (ga.channel_ktv.GetChannelKTVBGResp) {
    option (logic.gateway.command) = {
      id: 30992
    };
  }
  // 根据父id获取歌单分类列表
  rpc ListChannelKTVSongListType(ga.channel_ktv.ListChannelKTVSongListTypeReq) returns (ga.channel_ktv.ListChannelKTVSongListTypeResp) {
    option (logic.gateway.command) = {
      id: 30991
    };
  }
  // 获取特殊事件的文案
  rpc GetSpecialEventCopyWriting(ga.channel_ktv.GetSpecialEventCopyWritingReq) returns (ga.channel_ktv.GetSpecialEventCopyWritingResp) {
    option (logic.gateway.command) = {
      id: 30993
    };
  }
  // 根据歌单获取歌曲信息
  rpc GetChannelKTVSongListById(ga.channel_ktv.GetChannelKTVSongListByIdReq) returns (ga.channel_ktv.GetChannelKTVSongListByIdResp) {
    option (logic.gateway.command) = {
      id: 30994
    };
  }
  // 爆灯
  rpc ChannelKTVBurstLight(ga.channel_ktv.ChannelKTVBurstLightReq) returns (ga.channel_ktv.ChannelKTVBurstLightResp) {
    option (logic.gateway.command) = {
      id: 30995
    };
  }
  // 一键击掌
  rpc ChannelKTVHighFive(ga.channel_ktv.ChannelKTVHighFiveReq) returns (ga.channel_ktv.ChannelKTVHighFiveResp) {
    option (logic.gateway.command) = {
      id: 30996
    };
  }

  // 获取引导高潮歌单列表
  rpc ChannelKTVGuideClimaxSongList(ga.channel_ktv.ChannelKTVGuideClimaxSongListReq) returns (ga.channel_ktv.ChannelKTVGuideClimaxSongListResp) {
    option (logic.gateway.command) = {
      id: 30997
    };
  }

  // 批量获取歌曲高潮片段
  rpc ChannelKTVBatchSongClimaxInfos(ga.channel_ktv.ChannelKTVBatchSongClimaxInfosReq) returns (ga.channel_ktv.ChannelKTVBatchSongClimaxInfosResp) {
    option (logic.gateway.command) = {
      id: 30998
    };
  }

  // 跳过前奏
  rpc ChannelKTVSkipTheIntro(ga.channel_ktv.ChannelKTVSkipTheIntroReq) returns (ga.channel_ktv.ChannelKTVSkipTheIntroResp) {
    option (logic.gateway.command) = {
      id: 30999
    };
  }

  // 公屏引导回关
  rpc ChannelKTVFollowTrigger(ga.channel_ktv.ChannelKTVFollowTriggerReq) returns (ga.channel_ktv.ChannelKTVFollowTriggerResp) {
    option (logic.gateway.command) = {
      id: 31000
    };
  }

  // 搜索歌曲
  rpc ChannelKTVQuerySong(ga.channel_ktv.ChannelKTVQuerySongReq) returns (ga.channel_ktv.ChannelKTVQuerySongResp) {
    option (logic.gateway.command) = {
      id: 31001
    };
  }
}
