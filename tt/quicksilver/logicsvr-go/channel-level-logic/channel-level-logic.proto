syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_level/channel-level_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-level-logic";

service ChannelLevelLogic {
    rpc GetChannelLevelInfo (ga.channel_level.GetChannelLevelInfoReq) returns (ga.channel_level.GetChannelLevelInfoResp) {
        option (logic.gateway.command) = {
            id: 30370
      };
    }

    rpc GetChannelLevelSettings (ga.channel_level.GetChannelLevelSettingsReq) returns (ga.channel_level.GetChannelLevelSettingsResp) {
        option (logic.gateway.command) = {
            id: 30371
      };
    }
}