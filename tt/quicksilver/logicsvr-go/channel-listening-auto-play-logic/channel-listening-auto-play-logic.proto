syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channellisteningautoplaylogic/channel-listening-auto-play-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channellisteningautoplaylogic";

service ChannelListeningAutoPlayLogic {
  rpc ChannelPlayStatus(ga.channellisteningautoplaylogic.ChannelPlayStatusReq) returns (ga.channellisteningautoplaylogic.ChannelPlayStatusResp) {
    option (logic.gateway.command) = {
      id: 50800
    };
  }

  rpc SwitchChannelPlay(ga.channellisteningautoplaylogic.SwitchChannelPlayReq) returns (ga.channellisteningautoplaylogic.SwitchChannelPlayResp) {
    option (logic.gateway.command) = {
      id: 50801
    };
  }

  rpc CutAutoModeSong(ga.channellisteningautoplaylogic.CutAutoModeSongReq) returns (ga.channellisteningautoplaylogic.CutAutoModeSongResp) {
    option (logic.gateway.command) = {
      id: 50802
    };
  }

  rpc ReportChannelAutoSongProgress(ga.channellisteningautoplaylogic.ReportChannelAutoSongProgressReq) returns (ga.channellisteningautoplaylogic.ReportChannelAutoSongProgressResp) {
    option (logic.gateway.command) = {
      id: 50803
    };
  }

  rpc ChannelRcmdMusicMenu(ga.channellisteningautoplaylogic.ChannelRcmdMusicMenuReq) returns (ga.channellisteningautoplaylogic.ChannelRcmdMusicMenuResp) {
    option (logic.gateway.command) = {
      id: 50804
    };
  }

  rpc SetVolume(ga.channellisteningautoplaylogic.SetVolumeReq) returns (ga.channellisteningautoplaylogic.SetVolumeResp) {
    option (logic.gateway.command) = {
      id: 50805
    };
  }

  rpc SetChannelPlayMode(ga.channellisteningautoplaylogic.SetChannelPlayModeReq) returns (ga.channellisteningautoplaylogic.SetChannelPlayModeResp) {
    option (logic.gateway.command) = {
      id: 50806
    };
  }

  rpc SetChannelPlayStatus(ga.channellisteningautoplaylogic.SetChannelPlayStatusReq) returns (ga.channellisteningautoplaylogic.SetChannelPlayStatusResp) {
    option (logic.gateway.command) = {
      id: 50807
    };
  }

  rpc SelectAutoPlayPlayMode(ga.channellisteningautoplaylogic.SelectAutoPlayPlayModeRequest) returns (ga.channellisteningautoplaylogic.SelectAutoPlayPlayModeResponse) {
    option (logic.gateway.command) = {
      id: 50808
    };
  }

  rpc ListeningChangePlayer(ga.channellisteningautoplaylogic.ListeningChangePlayerReq) returns (ga.channellisteningautoplaylogic.ListeningChangePlayerResp) {
    option (logic.gateway.command) = {
      id: 50809
    };
  }

  rpc ListeningGetPlayerStatus(ga.channellisteningautoplaylogic.ListeningGetPlayerStatusReq) returns (ga.channellisteningautoplaylogic.ListeningGetPlayerStatusResp) {
    option (logic.gateway.command) = {
      id: 50810
    };
  }

  rpc ListeningSearchSongByKey(ga.channellisteningautoplaylogic.ListeningSearchSongByKeyReq) returns (ga.channellisteningautoplaylogic.ListeningSearchSongByKeyResp) {
    option (logic.gateway.command) = {
      id: 50811
    };
  }

  rpc ListeningGetSongListByType(ga.channellisteningautoplaylogic.ListeningGetSongListByTypeReq) returns (ga.channellisteningautoplaylogic.ListeningGetSongListByTypeResp) {
    option (logic.gateway.command) = {
      id: 50812
    };
  }

  rpc ListeningGetSongListList(ga.channellisteningautoplaylogic.ListeningGetSongListListReq) returns (ga.channellisteningautoplaylogic.ListeningGetSongListListResp) {
    option (logic.gateway.command) = {
      id: 50813
    };
  }

  rpc ListeningGetPlayerUserInfo(ga.channellisteningautoplaylogic.ListeningGetPlayerUserInfoReq) returns (ga.channellisteningautoplaylogic.ListeningGetPlayerUserInfoResp) {
    option (logic.gateway.command) = {
      id: 50814
    };
  }

  rpc ListeningOrderSong(ga.channellisteningautoplaylogic.ListeningOrderSongReq) returns (ga.channellisteningautoplaylogic.ListeningOrderSongResp) {
    option (logic.gateway.command) = {
      id: 50815
    };
  }

  rpc ListeningGetPlayList(ga.channellisteningautoplaylogic.ListeningGetPlayListReq) returns (ga.channellisteningautoplaylogic.ListeningGetPlayListResp) {
    option (logic.gateway.command) = {
      id: 50816
    };
  }

  rpc ListeningGetSongResource(ga.channellisteningautoplaylogic.ListeningGetSongResourceReq) returns (ga.channellisteningautoplaylogic.ListeningGetSongResourceResp) {
    option (logic.gateway.command) = {
      id: 50817
    };
  }

  
  rpc ListeningReportRecord(ga.channellisteningautoplaylogic.ListeningReportRecordReq) returns (ga.channellisteningautoplaylogic.ListeningReportRecordResp) {
    option (logic.gateway.command) = {
      id: 50818
    };
  }

  rpc ListeningLogout(ga.channellisteningautoplaylogic.ListeningLogoutReq) returns (ga.channellisteningautoplaylogic.ListeningLogoutResp) {
    option (logic.gateway.command) = {
      id: 50819
    };
  }

  rpc ListeningLikeSongWYY(ga.channellisteningautoplaylogic.ListeningLikeSongWYYReq) returns (ga.channellisteningautoplaylogic.ListeningLikeSongWYYResp) {
    option (logic.gateway.command) = {
      id: 32098
    };
  }

}