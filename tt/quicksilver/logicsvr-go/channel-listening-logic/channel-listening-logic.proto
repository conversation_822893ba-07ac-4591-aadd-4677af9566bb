syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channellisteninglogic/channel-listening-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channellisteninglogic";

service ChannelListeningLogic {
  rpc CheckListeningChannelTab (ga.channellisteninglogic.CheckListeningChannelTabReq) returns (ga.channellisteninglogic.CheckListeningChannelTabResp) {
    option (logic.gateway.command) = {
      id: 32076
    };
  }

  rpc ListeningCheckInV3 (ga.channellisteninglogic.ListeningCheckInV3Req) returns (ga.channellisteninglogic.ListeningCheckInV3Resp) {
    option (logic.gateway.command) = {
      id: 32077
    };
  }

  rpc ListeningCheckInSharedV3 (ga.channellisteninglogic.ListeningCheckInSharedReq) returns (ga.channellisteninglogic.ListeningCheckInSharedResp) {
    option (logic.gateway.command) = {
      id: 32078
    };
  }

  rpc ListeningDropCardV3 (ga.channellisteninglogic.ListeningDropCardV3Req) returns (ga.channellisteninglogic.ListeningDropCardV3Resp) {
    option (logic.gateway.command) = {
      id: 32079
    };
  }

  rpc Constellation (ga.channellisteninglogic.ConstellationReq) returns (ga.channellisteninglogic.ConstellationResp) {
    option (logic.gateway.command) = {
      id: 32080
    };
  }

  rpc GetChannelListeningSimpleInfo (ga.channellisteninglogic.GetChannelListeningSimpleInfoReq) returns (ga.channellisteninglogic.GetChannelListeningSimpleInfoResp) {
    option (logic.gateway.command) = {
      id: 32081
    };
  }

  rpc LikeSong (ga.channellisteninglogic.LikeSongReq) returns (ga.channellisteninglogic.LikeSongResp) {
    option (logic.gateway.command) = {
      id: 32082
    };
  }

  rpc GetBeLikedSongList (ga.channellisteninglogic.GetBeLikedSongListReq) returns (ga.channellisteninglogic.GetBeLikedSongListResp) {
    option (logic.gateway.command) = {
      id: 32083
    };
  }

  rpc GetLikedSongList (ga.channellisteninglogic.GetLikedSongListReq) returns (ga.channellisteninglogic.GetLikedSongListResp) {
    option (logic.gateway.command) = {
      id: 32084
    };
  }

  rpc GetUserFlowerList (ga.channellisteninglogic.GetUserFlowerListReq) returns (ga.channellisteninglogic.GetUserFlowerListResp) {
    option (logic.gateway.command) = {
      id: 32085
    };
  }

  rpc GetFlowerDetail (ga.channellisteninglogic.GetFlowerDetailReq) returns (ga.channellisteninglogic.GetFlowerDetailResp) {
    option (logic.gateway.command) = {
      id: 32086
    };
  }

  rpc SetListeningCheckInTopic (ga.channellisteninglogic.SetListeningCheckInTopicReq) returns (ga.channellisteninglogic.SetListeningCheckInTopicResp) {
    option (logic.gateway.command) = {
      id: 32087
    };
  }

  rpc ListeningCheckIn (ga.channellisteninglogic.ListeningCheckInReq) returns (ga.channellisteninglogic.ListeningCheckInResp) {
    option (logic.gateway.command) = {
      id: 32088
    };
  }

  rpc GetListeningUserCheckInInfoList (ga.channellisteninglogic.GetListeningUserCheckInInfoListReq) returns (ga.channellisteninglogic.GetListeningUserCheckInInfoListResp) {
    option (logic.gateway.command) = {
      id: 32089
    };
  }

  rpc GetRcmdSongMenu (ga.channellisteninglogic.GetRcmdSongMenuReq) returns (ga.channellisteninglogic.GetRcmdSongMenuResp) {
    option (logic.gateway.command) = {
      id: 32090
    };
  }

  rpc GetSongByMenuId (ga.channellisteninglogic.GetSongByMenuIdReq) returns (ga.channellisteninglogic.GetSongByMenuIdResp) {
    option (logic.gateway.command) = {
      id: 32091
    };
  }

  rpc GetAllMoodCfg (ga.channellisteninglogic.GetAllMoodCfgReq) returns (ga.channellisteninglogic.GetAllMoodCfgResp) {
    option (logic.gateway.command) = {
      id: 32093
    };
  }

  rpc SetListeningUserMood (ga.channellisteninglogic.SetListeningUserMoodReq) returns (ga.channellisteninglogic.SetListeningUserMoodResp) {
    option (logic.gateway.command) = {
      id: 32094
    };
  }

  rpc GetAllListeningOnMicUserMood (ga.channellisteninglogic.GetAllListeningOnMicUserMoodReq) returns (ga.channellisteninglogic.GetAllListeningOnMicUserMoodResp) {
    option (logic.gateway.command) = {
      id: 32095
    };
  }

  rpc ListeningCheckInV2 (ga.channellisteninglogic.ListeningCheckInReq) returns (ga.channellisteninglogic.ListeningCheckInResp) {
    option (logic.gateway.command) = {
      id: 32096
    };
  }

  rpc ListeningCheckInShared (ga.channellisteninglogic.ListeningCheckInSharedReq) returns (ga.channellisteninglogic.ListeningCheckInSharedResp) {
    option (logic.gateway.command) = {
      id: 32097
    };
  }



}