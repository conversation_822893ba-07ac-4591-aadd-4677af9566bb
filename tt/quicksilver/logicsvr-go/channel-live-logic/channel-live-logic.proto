syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_live_logic/channel-live-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-live-logic";

service ChannelLiveLogic {

  rpc GetLiveChannelInfo ( ga.channel_live_logic.GetLiveChannelInfoReq ) returns  ( ga.channel_live_logic.GetLiveChannelInfoResp ) {
    option (logic.gateway.command) = {
      id: 3580
    };
  }

  rpc ChannelLiveHeartbeat ( ga.channel_live_logic.ChannelLiveHeartbeatReq ) returns  ( ga.channel_live_logic.ChannelLiveHeartbeatResp ) {
    option (logic.gateway.command) = {
      id: 3581
    };
  }

  rpc SetChannelLiveStatus ( ga.channel_live_logic.SetChannelLiveStatusReq ) returns  ( ga.channel_live_logic.SetChannelLiveStatusResp ) {
    option (logic.gateway.command) = {
      id: 3582
    };
  }

  rpc GetChannelLiveStatus ( ga.channel_live_logic.GetChannelLiveStatusReq ) returns  ( ga.channel_live_logic.GetChannelLiveStatusResp ) {
    option (logic.gateway.command) = {
      id: 3583
    };
  }

  rpc ApplyPk ( ga.channel_live_logic.ApplyPkReq ) returns  ( ga.channel_live_logic.ApplyPkResp ) {
    option (logic.gateway.command) = {
      id: 3584
    };
  }

  rpc HandlerApply ( ga.channel_live_logic.HandlerApplyReq ) returns  ( ga.channel_live_logic.HandlerApplyResp ) {
    option (logic.gateway.command) = {
      id: 3585
    };
  }

  rpc BatchGetChannelLiveStatusByAccount ( ga.channel_live_logic.BatchGetChannelLiveStatusByAccountReq ) returns  ( ga.channel_live_logic.BatchGetChannelLiveStatusByAccountResp ) {
    option (logic.gateway.command) = {
      id: 3586
    };
  }

  rpc SetPkStatus ( ga.channel_live_logic.SetPkStatusReq ) returns  ( ga.channel_live_logic.SetPkStatusResp ) {
    option (logic.gateway.command) = {
      id: 3587
    };
  }

  rpc CancelPKApply ( ga.channel_live_logic.CancelPKApplyReq ) returns  ( ga.channel_live_logic.CancelPKApplyResp ) {
    option (logic.gateway.command) = {
      id: 3588
    };
  }

  rpc GetChannelLivePKRecord ( ga.channel_live_logic.GetChannelLivePKRecordReq ) returns ( ga.channel_live_logic.GetChannelLivePKRecordResp ) {
    option (logic.gateway.command) = {
      id: 3589
    };
  }

  rpc GetChannelLivePkRankUser ( ga.channel_live_logic.GetChannelLivePkRankUserReq ) returns ( ga.channel_live_logic.GetChannelLivePkRankUserResp ) {
    option (logic.gateway.command) = {
      id: 3590
    };
  }

  rpc GetChannelLiveRankUser ( ga.channel_live_logic.GetChannelLiveRankUserReq ) returns ( ga.channel_live_logic.GetChannelLiveRankUserResp ) {
    option (logic.gateway.command) = {
      id: 3591
    };
  }

  rpc GetChannelLiveWatchTimeRankUser ( ga.channel_live_logic.GetChannelLiveWatchTimeRankUserReq ) returns ( ga.channel_live_logic.GetChannelLiveWatchTimeRankUserResp ) {
    option (logic.gateway.command) = {
      id: 3592
    };
  }

  rpc GetChannelLiveData ( ga.channel_live_logic.GetChannelLiveDataReq ) returns ( ga.channel_live_logic.GetChannelLiveDataResp ) {
    option (logic.gateway.command) = {
      id: 3593
    };
  }

  rpc GetFansRankList ( ga.channel_live_logic.GetFansRankListReq ) returns ( ga.channel_live_logic.GetFansRankListResp ) {
    option (logic.gateway.command) = {
      id: 3594
    };
  }

  rpc GetFansInfo ( ga.channel_live_logic.GetFansInfoReq ) returns ( ga.channel_live_logic.GetFansInfoResp ) {
    option (logic.gateway.command) = {
      id: 3595
    };
  }

  rpc GetAnchorFansInfo ( ga.channel_live_logic.GetAnchorFansInfoReq ) returns ( ga.channel_live_logic.GetAnchorFansInfoResp ) {
    option (logic.gateway.command) = {
      id: 3596
    };
  }

  rpc SearchAnchor ( ga.channel_live_logic.SearchAnchorReq ) returns ( ga.channel_live_logic.SearchAnchorResp ) {
    option (logic.gateway.command) = {
      id: 3598
    };
  }

  rpc ReportClientIDChange (ga.channel_live_logic.ReportClientIDChangeReq) returns ( ga.channel_live_logic.ReportClientIDChangeResp ) {
    option (logic.gateway.command) = {
      id: 3599
    };
  }

  rpc GetApplyList (ga.channel_live_logic.GetApplyListReq) returns ( ga.channel_live_logic.GetApplyListResp ) {
    option (logic.gateway.command) = {
      id: 3570
    };
  }

  rpc GetPkInfo (ga.channel_live_logic.GetPkInfoReq) returns ( ga.channel_live_logic.GetPkInfoResp ) {
    option (logic.gateway.command) = {
      id: 3571
    };
  }

  rpc GetMyToolList (ga.channel_live_logic.GetMyToolListReq) returns ( ga.channel_live_logic.GetMyToolListResp ) {
    option (logic.gateway.command) = {
      id: 3572
    };
  }

  rpc GetItemConfig (ga.channel_live_logic.GetItemConfigReq) returns ( ga.channel_live_logic.GetItemConfigResp ) {
    option (logic.gateway.command) = {
      id: 3573
    };
  }

  rpc SetChannelLiveOpponentMicFlag (ga.channel_live_logic.SetChannelLiveOpponentMicFlagReq) returns ( ga.channel_live_logic.SetChannelLiveOpponentMicFlagResp ) {
    option (logic.gateway.command) = {
      id: 3574
    };
  }

  rpc StartPkMatch (ga.channel_live_logic.StartPkMatchReq) returns ( ga.channel_live_logic.StartPkMatchResp ) {
    option (logic.gateway.command) = {
      id: 3575
    };
  }

  rpc CancelPkMatch (ga.channel_live_logic.CancelPkMatchReq) returns ( ga.channel_live_logic.CancelPkMatchResp ) {
    option (logic.gateway.command) = {
      id: 3576
    };
  }

  rpc GetPKMatchInfo (ga.channel_live_logic.GetPKMatchInfoReq) returns ( ga.channel_live_logic.GetPKMatchInfoResp ) {
    option (logic.gateway.command) = {
      id: 3577
    };
  }

  rpc GetUserMissionList (ga.channel_live_logic.GetUserMissionListReq) returns (ga.channel_live_logic.GetUserMissionListResp) {
    option (logic.gateway.command) = {
      id: 3600
    };
  }

  rpc GetFansMissionList (ga.channel_live_logic.GetFansMissionListReq) returns (ga.channel_live_logic.GetFansMissionListResp) {
    option (logic.gateway.command) = {
      id: 3601
    };
  }

  rpc GetActorMissionList (ga.channel_live_logic.GetActorMissionListReq) returns (ga.channel_live_logic.GetActorMissionListResp) {
    option (logic.gateway.command) = {
      id: 3602
    };
  }

  rpc HandleUserMissionAtInterval (ga.channel_live_logic.HandleUserMissionAtIntervalReq) returns (ga.channel_live_logic.HandleUserMissionAtIntervalResp) {
    option (logic.gateway.command) = {
      id: 3603
    };
  }

  rpc HandleShareLiveChannelMission (ga.channel_live_logic.HandleShareLiveChannelMissionReq) returns (ga.channel_live_logic.HandleShareLiveChannelMissionResp) {
    option (logic.gateway.command) = {
      id: 3604
    };
  }

  rpc GetProcessActorMissionDesc (ga.channel_live_logic.GetProcessActorMissionDescReq) returns (ga.channel_live_logic.GetProcessActorMissionDescResp) {
    option (logic.gateway.command) = {
      id: 3605
    };
  }

  rpc HandleFansMissionAtInterval (ga.channel_live_logic.HandleFansMissionAtIntervalReq) returns (ga.channel_live_logic.HandleFansMissionAtIntervalResp) {
    option (logic.gateway.command) = {
      id: 3606
    };
  }

  rpc GetAnchorHonorNameplate ( ga.channel_live_logic.GetAnchorHonorNameplateReq ) returns ( ga.channel_live_logic.GetAnchorHonorNameplateResp ) {
    option (logic.gateway.command) = {
      id: 3610
    };
  }

  rpc GetRankingList (ga.channel_live_logic.GetRankingListReq) returns (ga.channel_live_logic.GetRankingListResp) {
    option (logic.gateway.command) = {
      id: 3611
    };
  }

  rpc GetFansAddedGroupList ( ga.channel_live_logic.GetFansAddedGroupListReq ) returns ( ga.channel_live_logic.GetFansAddedGroupListResp ) {
    option (logic.gateway.command) = {
      id: 3630
    };
  }

  rpc SetFansGroupName ( ga.channel_live_logic.SetFansGroupNameReq ) returns ( ga.channel_live_logic.SetFansGroupNameResp ) {
    option (logic.gateway.command) = {
      id: 3631
    };
  }

  rpc CheckSetGroupNamePermit ( ga.channel_live_logic.CheckSetGroupNamePermitReq ) returns ( ga.channel_live_logic.CheckSetGroupNamePermitResp ) {
    option (logic.gateway.command) = {
      id: 3632
    };
  }

  rpc CheckUserIsFans ( ga.channel_live_logic.CheckUserIsFansReq ) returns ( ga.channel_live_logic.CheckUserIsFansResp ) {
    option (logic.gateway.command) = {
      id: 3633
    };
  }

  rpc ChannelLiveReport ( ga.channel_live_logic.ChannelLiveReportReq ) returns ( ga.channel_live_logic.ChannelLiveReportResp ) {
    option (logic.gateway.command) = {
      id: 3634
    };
  }

  rpc AcceptAppointPk ( ga.channel_live_logic.AcceptAppointPkReq ) returns ( ga.channel_live_logic.AcceptAppointPkResp ) {
         option (logic.gateway.command) = {
                   id: 3567
         };
  }

  rpc ConfirmAppointPkPush ( ga.channel_live_logic.ConfirmAppointPkPushReq ) returns ( ga.channel_live_logic.ConfirmAppointPkPushResp ) {
         option (logic.gateway.command) = {
                   id: 3568
          };
  }

  rpc GetAppointPkInfo ( ga.channel_live_logic.GetAppointPkInfoReq ) returns ( ga.channel_live_logic.GetAppointPkInfoResp ) {
	      option (logic.gateway.command) = {
	                 id: 3569
	      };
  }

  rpc GetAnchorValidPlateList ( ga.channel_live_logic.GetAnchorValidPlateListReq ) returns ( ga.channel_live_logic.GetAnchorValidPlateListResp ) {
	      option (logic.gateway.command) = {
	                 id: 3565
	      };
  }

   rpc WearAnchorPlate ( ga.channel_live_logic.WearAnchorPlateReq ) returns ( ga.channel_live_logic.WearAnchorPlateResp ) {
	      option (logic.gateway.command) = {
	                 id: 3566
	      };
  }


  // 直播多人PK
  rpc GetChannelLiveMultiPkPermission (ga.channel_live_logic.GetChannelLiveMultiPkPermissionRequest) returns (ga.channel_live_logic.GetChannelLiveMultiPkPermissionResponse) {
        option (logic.gateway.command) = {
            id: 4001
       };
    }
    rpc SerarchPkAnchor (ga.channel_live_logic.SerarchMultiPkAnchorRequest) returns (ga.channel_live_logic.SerarchMultiPkAnchorResponse) {
        option (logic.gateway.command) = {
            id: 4002
       };
    }

    rpc ApplyChannelLiveMultiPk (ga.channel_live_logic.ApplyChannelLiveMultiPkRequest) returns (ga.channel_live_logic.ApplyChannelLiveMultiPkResponse) {
        option (logic.gateway.command) = {
            id: 4003
       };
    }

    rpc MatchMultiPk (ga.channel_live_logic.MatchMultiPkRequest) returns (ga.channel_live_logic.MatchMultiPkResponse) {
        option (logic.gateway.command) = {
            id: 4004
       };
    }
    rpc CancelMatchMultiPk (ga.channel_live_logic.CancelMatchMultiPkRequest) returns (ga.channel_live_logic.CancelMatchMultiPkResponse) {
        option (logic.gateway.command) = {
            id: 4005
       };
    }
    rpc AcceptChannelLiveMultiPk (ga.channel_live_logic.AcceptChannelLiveMultiPkRequest) returns (ga.channel_live_logic.AcceptChannelLiveMultiPkResponse) {
        option (logic.gateway.command) = {
            id: 4006
       };
    }
    rpc StartChannelLiveMultiPk (ga.channel_live_logic.StartChannelLiveMultiPkRequest) returns (ga.channel_live_logic.StartChannelLiveMultiPkResponse) {
        option (logic.gateway.command) = {
            id: 4007
       };
    }
    rpc GetChannelLiveMultiPkRank (ga.channel_live_logic.GetChannelLiveMultiPkRankRequest) returns (ga.channel_live_logic.GetChannelLiveMultiPkRankResponse) {
        option (logic.gateway.command) = {
            id: 4008
       };
    }
    rpc GetChannelLiveMultiPkKnightList (ga.channel_live_logic.GetChannelLiveMultiPkKnightListRequest) returns (ga.channel_live_logic.GetChannelLiveMultiPkKnightListResponse) {
        option (logic.gateway.command) = {
            id: 4009
       };
    }

    // 废弃接口，与api保持一致
//    rpc GetChannelLiveMultiPkInfo (ga.channel_live_logic.GetChannelLiveMultiPkInfoRequest) returns (ga.channel_live_logic.GetChannelLiveMultiPkInfoResponse) {
//        option (logic.gateway.command) = {
//            id: 4010
//            deprecated: true
//        };
//    }

    rpc GetChannelLiveMultiPkRecordList (ga.channel_live_logic.GetChannelLiveMultiPkRecordListRequest) returns (ga.channel_live_logic.GetChannelLiveMultiPkRecordListResponse) {
        option (logic.gateway.command) = {
            id: 4011
       };
    }
    rpc CancelChannelLiveMultiPkTeam (ga.channel_live_logic.CancelChannelLiveMultiPkTeamRequest) returns (ga.channel_live_logic.CancelChannelLiveMultiPkTeamResponse) {
        option (logic.gateway.command) = {
            id: 4012
       };
    }
    rpc StopChannelLiveMultiPk(ga.channel_live_logic.StopChannelLiveMultiPkRequest) returns (ga.channel_live_logic.StopChannelLiveMultiPkResponse) {
      option (logic.gateway.command) = {
           id: 4013;
      };
    }
    rpc DisinviteChannelLiveMultiPk(ga.channel_live_logic.DisinviteChannelLiveMultiPkRequest) returns (ga.channel_live_logic.DisinviteChannelLiveMultiPkResponse) {
      option (logic.gateway.command) = {
           id: 4014;
      };
    }

    rpc GetChannelLiveMultiPkTeamInfo(ga.channel_live_logic.GetChannelLiveMultiPkTeamInfoRequest) returns (ga.channel_live_logic.GetChannelLiveMultiPkTeamInfoResponse) {
      option (logic.gateway.command) = {
           id: 4015;
      };
    }
    rpc InitChannelLiveMultiPkTeam(ga.channel_live_logic.InitChannelLiveMultiPkTeamRequest) returns (ga.channel_live_logic.InitChannelLiveMultiPkTeamResponse) {
      option (logic.gateway.command) = {
           id: 4016;
      };
    }

    rpc LeaveFansGroup(ga.channel_live_logic.LeaveFansGroupRequest) returns (ga.channel_live_logic.LeaveFansGroupResponse) {
       option (logic.gateway.command) = {
           id: 3635;
      };
    }
  
    rpc GetVirtualLiveChannelSecret(ga.channel_live_logic.GetVirtualLiveChannelSecretRequest) returns (ga.channel_live_logic.GetVirtualLiveChannelSecretResponse) {
       option (logic.gateway.command) = {
           id: 3636;
      };
    }

    rpc GetUserFansGiftPri(ga.channel_live_logic.GetUserFansGiftPriRequest) returns (ga.channel_live_logic.GetUserFansGiftPriResponse) {
       option (logic.gateway.command) = {
           id: 3637;
      };
    }

}