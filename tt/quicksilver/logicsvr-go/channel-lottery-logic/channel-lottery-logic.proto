syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_lottery/channel-lottery_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-lottery-logic";

service ChannelLotteryLogic {
    option (logic.gateway.service_ext) = {
        service_name: "channel-lottery-logic"
    };
    // -------------------- 官频-抽奖 --------------------
    // 获取是否有抽奖功能
    rpc ShowChannelLotterySetting (ga.channel_lottery.ShowChannelLotterySettingReq) returns (ga.channel_lottery.ShowChannelLotterySettingResp) {
        option (logic.gateway.command) = {
            id: 30431
      };
    }
    // 获取抽奖设置信息
    rpc GetChannelLotterySetting (ga.channel_lottery.GetChannelLotterySettingReq) returns (ga.channel_lottery.GetChannelLotterySettingResp) {
        option (logic.gateway.command) = {
            id: 30432
      };
    }
    // 设置抽奖信息
    rpc SetChannelLotteryInfo (ga.channel_lottery.SetChannelLotteryInfoReq) returns (ga.channel_lottery.SetChannelLotteryInfoResp) {
        option (logic.gateway.command) = {
            id: 30433
      };
    }
    // 点击参与抽奖
    rpc JoinChannelLottery (ga.channel_lottery.JoinChannelLotteryReq) returns (ga.channel_lottery.JoinChannelLotteryResp) {
        option (logic.gateway.command) = {
            id: 30434
      };
    }
    // 获取本次排班所有抽奖配置和抽奖结果
    rpc GetChannelLotteryInfoList (ga.channel_lottery.GetChannelLotteryInfoListReq) returns (ga.channel_lottery.GetChannelLotteryInfoListResp) {
        option (logic.gateway.command) = {
            id: 30435
      };
    }
    // 确定并开始抽奖
    rpc BeginChannelLottery (ga.channel_lottery.BeginChannelLotteryReq) returns (ga.channel_lottery.BeginChannelLotteryResp) {
        option (logic.gateway.command) = {
            id: 30436
      };
    }
    // 确认送礼
    rpc SendChannelLotteryPresent (ga.channel_lottery.SendChannelLotteryPresentReq) returns (ga.channel_lottery.SendChannelLotteryPresentResp) {
        option (logic.gateway.command) = {
            id: 30437
      };
    }
    // 获取开始抽奖信息
    rpc GetChannelLotteryInfo (ga.channel_lottery.GetChannelLotteryInfoReq) returns (ga.channel_lottery.GetChannelLotteryInfoResp) {
        option (logic.gateway.command) = {
            id: 30438
      };
    }
    // 获取自定义礼物列表
    rpc SearchCustomGifts (ga.channel_lottery.SearchCustomGiftsReq) returns (ga.channel_lottery.SearchCustomGiftsResp) {
        option (logic.gateway.command) = {
            id: 30439
      };
    }

    // 上报通过分享链接进抽奖房间
    rpc ReportEnterShareChannel (ga.channel_lottery.ReportEnterShareChannelReq) returns (ga.channel_lottery.ReportEnterShareChannelResp) {
        option (logic.gateway.command) = {
            id: 36521
        };
    }
    // 获取抽奖期间进房人数
    rpc GetEnterChannelUserCnt (ga.channel_lottery.GetEnterChannelUserCntReq) returns (ga.channel_lottery.GetEnterChannelUserCntResp) {
        option (logic.gateway.command) = {
            id: 36522
        };
    }
    // 获取用户任务进度
    rpc GetConditionMissionProgress (ga.channel_lottery.GetConditionMissionProgressReq) returns (ga.channel_lottery.GetConditionMissionProgressResp) {
        option (logic.gateway.command) = {
            id: 36523
        };
    }
}
