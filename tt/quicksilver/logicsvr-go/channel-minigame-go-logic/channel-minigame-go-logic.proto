syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel/channel_.proto";
import "channel_mini_game/channel-minigame-go-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-minigame-go-logic";

service ChannelMiniGameGoLogic {
  option (logic.gateway.service_ext) = {
    service_name: "channel-minigame-go-logic"
  };

  
  rpc ChannelGame (ga.channel.ChannelGameReq) returns (ga.channel.ChannelGameResp) {
    option (logic.gateway.command) = {
      id: 2063
      deprecated: true
      rewrite_full_path:"/ga.api.channelext_logic_go.ChannelExtLogicGo/ChannelGame"
    };
  }

  rpc SendMagicExpression (ga.channel.SendMagicExpressionReq) returns (ga.channel.SendMagicExpressionResp) {
    option (logic.gateway.command) = {
      id: 2067
      deprecated: true
      rewrite_full_path:"/ga.api.channelext_logic_go.ChannelExtLogicGo/SendMagicExpression"
    };
  }

  rpc ChannelVotePKVote (ga.channel.ChannelVotePkVoteReq) returns (ga.channel.ChannelVotePkVoteResp) {
    option (logic.gateway.command) = {
      id: 5002
    };
  }

  rpc ChannelVotePKStart (ga.channel.ChannelVotePkStartReq) returns (ga.channel.ChannelVotePkStartResp) {
    option (logic.gateway.command) = {
      id: 5003
    };
  }

  rpc ChannelVotePKCancel (ga.channel.ChannelPkCancelReq) returns (ga.channel.ChannelPKCancelResp) {
    option (logic.gateway.command) = {
      id: 5004
    };
  }

  rpc ChannelVotePKGetInfo (ga.channel.GetChannelVotePKInfoReq) returns (ga.channel.GetChannelVotePKInfoResp) {
    option (logic.gateway.command) = {
      id: 5005
    };
  }

  rpc SearchPkCandidate (ga.channel_mini_game.SearchPkCandidateReq) returns (ga.channel_mini_game.SearchPkCandidateResp) {
    option (logic.gateway.command) = {
      id: 32100
    };
  }

  rpc ChannelLiveVotePKVote (ga.channel.ChannelLiveVotePkVoteReq) returns (ga.channel.ChannelLiveVotePkVoteResp) {
    option (logic.gateway.command) = {
      id: 32101
    };
  }

  rpc ChannelLiveVotePKStart (ga.channel.ChannelLiveVotePkStartReq) returns (ga.channel.ChannelLiveVotePkStartResp) {
    option (logic.gateway.command) = {
      id: 32102
    };
  }

  rpc ChannelLiveVotePKCancel (ga.channel.ChannelLivePkCancelReq) returns (ga.channel.ChannelLivePKCancelResp) {
    option (logic.gateway.command) = {
      id: 32103
    };
  }

  rpc ChannelLiveVotePKGetInfo (ga.channel.GetChannelLiveVotePKInfoReq) returns (ga.channel.GetChannelLiveVotePKInfoResp) {
    option (logic.gateway.command) = {
      id: 32104
    };
  }

  rpc GetVotePkStatus (ga.channel_mini_game.GetVotePkStatusReq) returns (ga.channel_mini_game.GetVotePkStatusResp) {
    option (logic.gateway.command) = {
      id: 32105
    };
  }

  rpc ChannelPresentCountSwitch (ga.channel.ChannelPresentCountReq) returns (ga.channel.ChannelPresentCountResp) {
    option (logic.gateway.command) = {
      id: 30000
    };
  }

  rpc ChannelPresentCountInit (ga.channel.GetChannelPresentCountStateReq) returns (ga.channel.GetChannelPresentCountStateResp) {
    option (logic.gateway.command) = {
      id: 30001
    };
  }

  rpc GetChannelPresentRunwayList (ga.channel.GetChannelPresentRunwayListReq) returns (ga.channel.GetChannelPresentRunwayListResp) {
    option (logic.gateway.command) = {
      id: 2510
    };
  }

  rpc GetPresentCountRank (ga.channel.GetPresentCountRankReq) returns (ga.channel.GetPresentCountRankResp) {
    option (logic.gateway.command) = {
      id: 30002
    };
  }


  rpc GetInteractionEmojiConfList (ga.channel_mini_game.GetInteractionEmojiConfListReq) returns (ga.channel_mini_game.GetInteractionEmojiConfListResp) {
    option (logic.gateway.command) = {
      id: 50606
    };
  }


  rpc GetInteractionEmojiPrivilege (ga.channel_mini_game.GetInteractionEmojiPrivilegeReq) returns (ga.channel_mini_game.GetInteractionEmojiPrivilegeResp) {
    option (logic.gateway.command) = {
      id: 50607
    };
  }

    rpc SendInteractionEmoji (ga.channel.SendInteractionEmojiReq) returns (ga.channel.SendInteractionEmojiResp) {
    option (logic.gateway.command) = {
      id: 50608
    };
  }

}