syntax = "proto3";

package logic;

import "logicsvr-go/gateway/options/options.proto";
import "app/channel-movie_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-movie-logic";

service ChannelMovieLogic {
    option (logic.gateway.service_ext) = {
        service_name: "channel-movie-logic"
    };

    // // 获取房间播放状态
    // rpc GetChannelMovieStatus (ga.GetChannelMovieStatusReq) returns (ga.GetChannelMovieStatusResp) {
    //     option (logic.gateway.command) = {
    //         id: 30601
    //     };
    // }
    // // 播放视频
    // rpc PlayChannelMovie (ga.PlayChannelMovieReq) returns (ga.PlayChannelMovieResp) {
    //     option (logic.gateway.command) = {
    //         id: 30602
    //     };
    // }
    // // 暂停/开启
    // rpc ChangeChannelMovieStatus (ga.ChangeChannelMovieStatusReq) returns (ga.ChangeChannelMovieStatusResp) {
    //     option (logic.gateway.command) = {
    //         id: 30603
    //     };
    // }
    // // 播放条拖动
    // rpc DragChannelMovieBar (ga.DragChannelMovieBarReq) returns (ga.DragChannelMovieBarResp) {
    //     option (logic.gateway.command) = {
    //         id: 30604
    //     };
    // }

    // //获取分类列表
    // rpc GetMovieCategory (ga.GetMovieCategoryReq) returns (ga.GetMovieCategoryResp) {
    //     option (logic.gateway.command) = {
    //         id:30605
    //     };
    // }
    // //排行榜
    // rpc GetMovieRankList (ga.GetMovieRankListReq) returns (ga.GetMovieRankListResp) {
    //     option (logic.gateway.command) = {
    //         id:30606
    //     };
    // }
    // //搜索
    // rpc SearchMovie (ga.SearchMovieReq) returns (ga.SearchMovieResp) {
    //     option (logic.gateway.command) = {
    //         id:30607
    //     };
    // }
    // //电影详情
    // rpc GetMovieDetail (ga.GetMovieDetailReq) returns (ga.GetMovieDetailResp) {
    //     option (logic.gateway.command) = {
    //         id:30608
    //     };
    // }
}
