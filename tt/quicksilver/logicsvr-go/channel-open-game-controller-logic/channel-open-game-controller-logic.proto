syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-open-game-controller-logic";
import "channel_open_game_controller/channel-open-game-controller-logic_.proto";
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";

service ChannelOpenGameControllerLogic {
    rpc GetChannelGamePlayerOpenid (ga.channel_open_game_controller.GetChannelGamePlayerOpenidReq) returns (ga.channel_open_game_controller.GetChannelGamePlayerOpenidResp) {
        option (logic.gateway.command) = {
            id: 30111
        };
    }
    rpc JoinChannelGame (ga.channel_open_game_controller.JoinChannelGameReq) returns (ga.channel_open_game_controller.JoinChannelGameResp) {
        option (logic.gateway.command) = {
            id: 30112
        };
    }
    rpc QuitChannelGame (ga.channel_open_game_controller.QuitChannelGameReq) returns (ga.channel_open_game_controller.QuitChannelGameResp) {
        option (logic.gateway.command) = {
            id: 30113
        };
    }
    rpc ReadyChannelGame (ga.channel_open_game_controller.ReadyChannelGameReq) returns (ga.channel_open_game_controller.ReadyChannelGameResp) {
        option (logic.gateway.command) = {
            id: 30114
        };
    }
    rpc UnReadyChannelGame (ga.channel_open_game_controller.UnReadyChannelGameReq) returns (ga.channel_open_game_controller.UnReadyChannelGameResp) {
        option (logic.gateway.command) = {
            id: 30115
        };
    }
    rpc GetChannelGameStatusInfo (ga.channel_open_game_controller.GetChannelGameStatusInfoReq) returns (ga.channel_open_game_controller.GetChannelGameStatusInfoResp) {
        option (logic.gateway.command) = {
            id: 30116
        };
    }
    rpc ExitChannelGame (ga.channel_open_game_controller.ExitChannelGameReq) returns (ga.channel_open_game_controller.ExitChannelGameResp) {
        option (logic.gateway.command) = {
            id: 30117
        };
    }
    rpc StartChannelGame (ga.channel_open_game_controller.StartChannelGameReq) returns (ga.channel_open_game_controller.StartChannelGameResp) {
        option (logic.gateway.command) = {
            id: 30118
        };
    }
    rpc SetChannelGameModeInfo (ga.channel_open_game_controller.SetChannelGameModeInfoReq) returns (ga.channel_open_game_controller.SetChannelGameModeInfoResp) {
        option (logic.gateway.command) = {
            id: 30119
        };
    }
    rpc SetChannelGamePlayerLoading (ga.channel_open_game_controller.SetChannelGamePlayerLoadingReq) returns (ga.channel_open_game_controller.SetChannelGamePlayerLoadingResp) {
        option (logic.gateway.command) = {
            id: 30120
        };
    }
    rpc BatchGetUidByOpenid (ga.channel_open_game_controller.BatchGetUidByOpenidReq) returns (ga.channel_open_game_controller.BatchGetUidByOpenidResp) {
        option (logic.gateway.command) = {
            id: 30121
        };
    }
    rpc CheckUserStartGamePermission (ga.channel_open_game_controller.CheckUserStartGamePermissionRequest) returns (ga.channel_open_game_controller.CheckUserStartGamePermissionResponse) {
        option (logic.gateway.command) = {
            id: 50861
        };
    }
}

