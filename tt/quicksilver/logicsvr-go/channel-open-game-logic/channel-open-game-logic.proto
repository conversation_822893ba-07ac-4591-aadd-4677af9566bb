syntax = "proto3";

package channel_open_game_logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_open_game/channel-open-game-logic_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-open-game-logic";

service ChannelOpenGameLogic {

    option (logic.gateway.service_ext) = {
        service_name: "channel-open-game-logic"
    };

    // 获取房间支持游戏列表
    rpc GetChannelSupportGameList (ga.channel_open_game.GetChannelSupportGameListReq) returns (ga.channel_open_game.GetChannelSupportGameListResp) {
        option (logic.gateway.command) = {
			id: 30070
		};
    }

    // 加载房间玩法
    rpc SetChannelLoadingGame (ga.channel_open_game.SetChannelLoadingGameReq) returns (ga.channel_open_game.SetChannelLoadingGameResp) {
        option (logic.gateway.command) = {
			id: 30071
		 };
    }

    // 获取房间玩法
    rpc GetChannelLoadingGame (ga.channel_open_game.GetChannelLoadingGameReq) returns (ga.channel_open_game.GetChannelLoadingGameResp) {
        option (logic.gateway.command) = {
			id: 30072
		 };
    }

    // 设置游戏玩家角色
    rpc SubmitGameCenterCmd (ga.channel_open_game.SubmitGameCenterCmdReq) returns (ga.channel_open_game.SubmitGameCenterCmdResp) {
        option (logic.gateway.command) = {
			id: 30074
		 };
    }

    // 获取活动奖励
    rpc GetGameActivityReward (ga.channel_open_game.GetGameActivityRewardReq) returns (ga.channel_open_game.GetGameActivityRewardResp) {
        option (logic.gateway.command) = {
			id: 30075
		 };
    }

    // 活动分享上报
    rpc ReportGameActivityShared (ga.channel_open_game.ReportGameActivitySharedReq) returns (ga.channel_open_game.ReportGameActivitySharedResp) {
        option (logic.gateway.command) = {
			id: 30076
		 };
    }

    // 上报加入游戏
    rpc JoinChannelGameBegin (ga.channel_open_game.JoinChannelGameBeginReq) returns (ga.channel_open_game.JoinChannelGameBeginResp) {
        option (logic.gateway.command) = {
			id: 30077
		 };
    }

    // 上报可以开始游戏
    rpc JoinChannelGameEnd (ga.channel_open_game.JoinChannelGameEndReq) returns (ga.channel_open_game.JoinChannelGameEndResp) {
        option (logic.gateway.command) = {
			id: 30078
		 };
    }

    rpc QueryChannelGameTick (ga.channel_open_game.QueryChannelGameTickReq) returns (ga.channel_open_game.QueryChannelGameTickResp) {
        option (logic.gateway.command) = {
			id: 30079
		 };
    }

    rpc GetChannelGameBaseInfo(ga.channel_open_game.GetChannelGameBaseInfoRequest) returns (ga.channel_open_game.GetChannelGameBaseInfoResponse) {
      option (logic.gateway.command) = {
        id: 50860
      };
    }
}
