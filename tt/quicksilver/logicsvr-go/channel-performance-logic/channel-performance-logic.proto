syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_performance/channel-performance_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-performance-logic";

service ChannelPerformanceLogic {
    option (logic.gateway.service_ext) = {
        service_name: "channel-performance-logic"
    };

    rpc GetChannelPerformance (ga.channel_performance.GetChannelPerformanceReq) returns (ga.channel_performance.GetChannelPerformanceResp) {
        option (logic.gateway.command) = {
			id: 30350
		};
    }

    rpc SetCurrentChannelPerformanceStage (ga.channel_performance.SetCurrentChannelPerformanceStageReq) returns (ga.channel_performance.SetCurrentChannelPerformanceStageResp) {
        option (logic.gateway.command) = {
			id: 30351
		};
    }
}
