syntax = "proto3";

package logic.channel_play;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_play/channel-play_.proto";
import "hobby_channel/hobby-channel_.proto";
import "topic_channel/topic_channel_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-play-logic";

service ChannelPlayLogic{
  option (logic.gateway.service_ext) = {
    service_name: "channel-play-logic"
  };

  rpc ListTopicChannel(ga.channel_play.ListTopicChannelReq) returns(ga.channel_play.ListTopicChannelResp) {
    option (logic.gateway.command) = {
      id: 3080
    };
  };

  rpc GetSecondaryFilter(ga.channel_play.GetSecondaryFilterReq) returns(ga.channel_play.GetSecondaryFilterResp) {
    option (logic.gateway.command) = {
      id: 3081
    };
  };

  rpc GetSecondaryFilterByCategory(ga.channel_play.GetSecondaryFilterByCategoryReq) returns(ga.channel_play.GetSecondaryFilterByCategoryResp) {
    option (logic.gateway.command) = {
      id: 3082
    };
  }

  rpc GetDefaultRoomNameList(ga.channel_play.GetDefaultRoomNameListReq) returns(ga.channel_play.GetDefaultRoomNameListResp) {
    option (logic.gateway.command) = {
      id: 3083
    };
  }

  rpc GetGameHomePageDIYFilter(ga.hobby_channel.GetGameHomePageDIYFilterReq) returns (ga.hobby_channel.GetGameHomePageDIYFilterResp) {
    option (logic.gateway.command) = {
      id: 31510
    };
  }

  rpc SetGameHomePageDIYFilter(ga.hobby_channel.SetGameHomePageDIYFilterReq) returns (ga.hobby_channel.SetGameHomePageDIYFilterResp) {
    option (logic.gateway.command) = {
      id: 31511
    };
  }

  rpc GetGameHomePageFilter(ga.hobby_channel.GetGameHomePageFilterReq) returns (ga.hobby_channel.GetGameHomePageFilterResp) {
    option (logic.gateway.command) = {
      id: 31512
    };
  }

  //从hobby-channel-logic迁移过滤，创建房间
  rpc CreateHobbyChannel (ga.hobby_channel.CreateHobbyChannelReq) returns (ga.hobby_channel.CreateHobbyChannelResp) {
    option (logic.gateway.command) = {
      id: 31500
    };
  }

  rpc PublishGangupChannel (ga.channel_play.PublishGangupChannelReq) returns (ga.channel_play.PublishGangupChannelResp) {
    option (logic.gateway.command) = {
      id: 3084
    };
  }

  rpc CancelGangupChannelPublish (ga.channel_play.CancelGangupChannelPublishReq) returns (ga.channel_play.CancelGangupChannelPublishResp) {
    option (logic.gateway.command) = {
      id: 3085
    };
  }

  rpc GetHomePageHeadConfig (ga.channel_play.HomePageHeadConfigReq) returns (ga.channel_play.HomePageHeadConfigResp) {
    option (logic.gateway.command) = {
      id: 3086
    };
  }

  rpc GetHotMiniGames (ga.channel_play.GetHotMiniGamesReq) returns (ga.channel_play.GetHotMiniGamesResp) {
    option (logic.gateway.command) = {
      id: 3087
    };
  }

  rpc GetQuickMiniGames (ga.channel_play.GetQuickMiniGamesReq) returns (ga.channel_play.GetQuickMiniGamesResp) {
    option (logic.gateway.command) = {
      id: 3088
    };
  }

  rpc GetPlayQuestions (ga.channel_play.GetPlayQuestionsReq) returns (ga.channel_play.GetPlayQuestionsResp) {
    option (logic.gateway.command) = {
      id: 3089
    };
  }

  rpc GameInsertFlowConfig (ga.channel_play.GameInsertFlowConfigReq) returns (ga.channel_play.GameInsertFlowConfigResp) {
    option (logic.gateway.command) = {
      id: 3090
    };
  }

  rpc GetHomePageGuide (ga.channel_play.GetHomePageGuideReq) returns (ga.channel_play.GetHomePageGuideResp) {
    option (logic.gateway.command) = {
      id: 3091
    };
  }

  rpc GetMoreTabConfig (ga.channel_play.GetMoreTabConfigReq) returns (ga.channel_play.GetMoreTabConfigResp) {
    option (logic.gateway.command) = {
      id: 3092
    };
  }

  rpc GetFilterItemByEntrance (ga.channel_play.GetFilterItemByEntranceReq) returns (ga.channel_play.GetFilterItemByEntranceResp) {
    option (logic.gateway.command) = {
      id: 3093
    };
  }

  rpc SetDIYFilterByEntrance (ga.channel_play.SetDIYFilterByEntranceReq) returns (ga.channel_play.SetDIYFilterByEntranceResp) {
    option (logic.gateway.command) = {
      id: 3094
    };
  }

  rpc GetDIYFilterByEntrance (ga.channel_play.GetDIYFilterByEntranceReq) returns (ga.channel_play.GetDIYFilterByEntranceResp) {
    option (logic.gateway.command) = {
      id: 3095
    };
  }
  rpc GetNegativeFeedBackInRoom (ga.channel_play.GetNegativeFeedBackInRoomReq) returns (ga.channel_play.GetNegativeFeedBackInRoomResp) {
    option (logic.gateway.command) = {
      id: 3096
    };
  }
  rpc ReportNegativeFeedBackInRoom (ga.channel_play.ReportNegativeFeedBackInRoomReq) returns (ga.channel_play.ReportNegativeFeedBackInRoomResp) {
    option (logic.gateway.command) = {
      id: 3097
    };
  };
  rpc GetPublishOptionGuide (ga.channel_play.GetPublishOptionGuideReq) returns (ga.channel_play.GetPublishOptionGuideResp) {
    option (logic.gateway.command) = {
      id: 3098
    };
  }

  rpc GetNewQuickMatchConfig(ga.channel_play.GetNewQuickMatchConfigReq) returns (ga.channel_play.GetNewQuickMatchConfigResp) {
    option (logic.gateway.command) = {
      id: 3099
    };
  }

  rpc GetTopicChannelCfgInfo (ga.channel_play.GetTopicChannelCfgInfoReq) returns (ga.channel_play.GetTopicChannelCfgInfoResp) {
    option (logic.gateway.command) = {
      id: 3100
    };
  }

  rpc SetUgcChannelPlayMode (ga.channel_play.SetUgcChannelPlayModeReq) returns (ga.channel_play.SetUgcChannelPlayModeResp) {
    option (logic.gateway.command) = {
      id: 3101
    };
  }

  rpc TypingStatusBroadcast (ga.channel_play.TypingStatusBroadcastReq) returns (ga.channel_play.TypingStatusBroadcastResp) {
    option (logic.gateway.command) = {
      id: 3102
    };
  }

  rpc GetChannelPlayModeGuide (ga.channel_play.GetChannelPlayModeGuideReq) returns (ga.channel_play.GetChannelPlayModeGuideResp) {
    option (logic.gateway.command) = {
      id: 3103
    };
  }
  
  rpc ReportDailyTask (ga.channel_play.ReportDailyTaskReq) returns (ga.channel_play.ReportDailyTaskResp) {
    option (logic.gateway.command) = {
      id: 3104
    };
  }

  rpc GetCache(ga.channel_play.GetCacheReq) returns (ga.channel_play.GetCacheResp) {}

  //从topic-channel-logic-v2迁移过来，创房/切换玩法面板，PC首页全部分类、PC房间切换玩法面板
  rpc ShowTopicChannelTabList ( ga.topic_channel.ShowTopicChannelTabListReq ) returns (ga.topic_channel.ShowTopicChannelTabListResp ) {
    option (logic.gateway.command) = {
      id: 3056
    };
  }

  // 3105首页专区是否能进入检查判断
  rpc HomePageHeadConfigEnterCheck (ga.channel_play.HomePageHeadConfigEnterCheckReq) returns (ga.channel_play.HomePageHeadConfigEnterCheckResp) {
    option (logic.gateway.command) = {
      id: 3105
    };
  }

  // 首页专区房间列表引导配置
  rpc GetChannelListGuideConfigs (ga.channel_play.GetChannelListGuideConfigsReq) returns (ga.channel_play.GetChannelListGuideConfigsResp) {
    option (logic.gateway.command) = {
      id: 3106
    };
  }

  // 获取玩法信息
  rpc GetTabInfos (ga.channel_play.GetTabInfosReq) returns (ga.channel_play.GetTabInfosResp) {
    option (logic.gateway.command) = {
      id: 3107
    };
  }

  // 获取玩法信息
  rpc GetRecommendGames (ga.channel_play.GetRecommendGamesReq) returns (ga.channel_play.GetRecommendGamesResp) {
    option (logic.gateway.command) = {
      id: 3112
    };
  }

  // 获取玩法信息
  rpc RefreshGameLabel (ga.channel_play.RefreshGameLabelReq) returns (ga.channel_play.RefreshGameLabelResp) {
    option (logic.gateway.command) = {
      id: 3113
    };
  }
}