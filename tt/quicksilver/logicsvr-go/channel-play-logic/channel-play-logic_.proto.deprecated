syntax = "proto3";

package logic.channel_play;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "tt/quicksilver/hobby-channel_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-play-logic";

service ChannelPlayLogic_{
  option (logic.gateway.service_ext) = {
    service_name: "channel-play-logic"
  };

  rpc GetGameHomePageDIYFilter(.ga.GetGameHomePageDIYFilterReq) returns (.ga.GetGameHomePageDIYFilterResp) {
    option (logic.gateway.command) = {
      id: 31510
    };
  }

  rpc SetGameHomePageDIYFilter(.ga.SetGameHomePageDIYFilterReq) returns (.ga.SetGameHomePageDIYFilterResp) {
    option (logic.gateway.command) = {
      id: 31511
    };
  }

  rpc GetGameHomePageFilter(.ga.GetGameHomePageFilterReq) returns (.ga.GetGameHomePageFilterResp) {
    option (logic.gateway.command) = {
      id: 31512
    };
  }
}