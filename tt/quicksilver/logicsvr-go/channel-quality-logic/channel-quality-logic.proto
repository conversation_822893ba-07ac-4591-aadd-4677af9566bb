syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_quality/channel-quality_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-quality-logic";

service ChannelQualityLogic {
    rpc QualityEventReport (ga.channel_quality.QualityEventReportReq) returns (ga.channel_quality.QualityEventReportResp) {
        option (logic.gateway.command) = {
            id: 30160
      };
    }
}