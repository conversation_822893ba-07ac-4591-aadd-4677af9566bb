syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_roleplay_logic/channel-roleplay-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-roleplay-logic";

service ChannelRoleplayLogic {
	option (logic.gateway.service_ext) = {
		service_name: "channel-roleplay-logic"
	};

	// 获取房间内麦上用户角色信息
	rpc GetChannelHoldMicUserRoleList (ga.channel_roleplay_logic.GetChannelHoldMicUserRoleListReq) returns (ga.channel_roleplay_logic.GetChannelHoldMicUserRoleListResp) {
		option (logic.gateway.command) = {
			id: 104001
		};
	}

	// 修改自己的房间内角色信息
	rpc SetMyChannelRole (ga.channel_roleplay_logic.SetMyChannelRoleReq) returns (ga.channel_roleplay_logic.SetMyChannelRoleResp) {
		option (logic.gateway.command) = {
			id: 104002
		};
	}

	rpc EnterBox (ga.channel_roleplay_logic.EnterBoxReq) returns (ga.channel_roleplay_logic.EnterBoxResp) {
        option (logic.gateway.command) = {
            id: 104003
        };
    }

    rpc ExitBox (ga.channel_roleplay_logic.ExitBoxReq) returns (ga.channel_roleplay_logic.ExitBoxResp) {
        option (logic.gateway.command) = {
            id: 104004
        };
    }

    rpc HandleApplyBox (ga.channel_roleplay_logic.HandleApplyBoxReq) returns (ga.channel_roleplay_logic.HandleApplyBoxResp) {
        option (logic.gateway.command) = {
            id: 104005
        };
    }

    rpc GetBoxInfo (ga.channel_roleplay_logic.GetBoxInfoReq) returns (ga.channel_roleplay_logic.GetBoxInfoResp) {
        option (logic.gateway.command) = {
            id: 104006
        };
    }

    rpc GetBoxInfosByLimit (ga.channel_roleplay_logic.GetBoxInfosByLimitReq) returns (ga.channel_roleplay_logic.GetBoxInfosByLimitResp) {
        option (logic.gateway.command) = {
            id: 104007
        };
    }

    rpc GetChannelUserRoleList (ga.channel_roleplay_logic.GetChannelUserRoleListReq) returns (ga.channel_roleplay_logic.GetChannelUserRoleListResp) {
	    option (logic.gateway.command) = {
		    id: 104008
	    };
    }

	// 创建/修改子频道
	rpc UpsertBoxInfo(ga.channel_roleplay_logic.UpsertBoxInfoReq) returns (ga.channel_roleplay_logic.UpsertBoxInfoResp) {
		option (logic.gateway.command) = {
			id: 50053
		};
	}

	// 删除子频道
	rpc DelBoxInfo(ga.channel_roleplay_logic.DelBoxInfoReq) returns (ga.channel_roleplay_logic.DelBoxInfoResp) {
		option (logic.gateway.command) = {
			id: 50054
		};
	}

	// 主房间开启公共频道
    rpc OpenCommonMic(ga.channel_roleplay_logic.OpenCommonMicReq) returns (ga.channel_roleplay_logic.OpenCommonMicResp) {
        option (logic.gateway.command) = {
            id: 50052
        };
    }
}