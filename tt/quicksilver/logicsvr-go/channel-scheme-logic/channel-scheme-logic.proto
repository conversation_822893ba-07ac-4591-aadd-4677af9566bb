syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_scheme/channel-scheme_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-scheme-logic";

service ChannelSchemeLogic {
    option (logic.gateway.service_ext) = {
        service_name: "channel-scheme-logic"
    };

    rpc GetChannelSchemeInfo (ga.channel_scheme.GetChannelSchemeInfoReq) returns (ga.channel_scheme.GetChannelSchemeInfoResp) {
        option (logic.gateway.command) = {
          id: 33100
        };
    }

    rpc CheckExitAfterSwitchScheme (ga.channel_scheme.CheckExitAfterSwitchSchemeReq) returns (ga.channel_scheme.CheckExitAfterSwitchSchemeResp) {
        option (logic.gateway.command) = {
          id: 33101
        };
    }

    rpc GetPgcChannelSchemeList (ga.channel_scheme.GetPgcChannelSchemeListReq) returns (ga.channel_scheme.GetPgcChannelSchemeListResp) {
        option (logic.gateway.command) = {
            id: 33102
        };
    }
    rpc SwitchChannelScheme (ga.channel_scheme.SwitchChannelSchemeReq) returns (ga.channel_scheme.SwitchChannelSchemeResp) {
        option (logic.gateway.command) = {
            id: 33103
        };
    }
}