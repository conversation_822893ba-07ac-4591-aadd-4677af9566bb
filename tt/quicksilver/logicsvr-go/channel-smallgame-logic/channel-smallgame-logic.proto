syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_mini_game/channel-minigame-go-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-smallgame-logic";

service ChannelSmallGameLogic {
  option (logic.gateway.service_ext) = {
    service_name: "channel-smallgame-logic"
  };

  rpc GetNumBombStatus (ga.channel_mini_game.GetNumBombStatusReq) returns (ga.channel_mini_game.GetNumBombStatusResp) {
    option (logic.gateway.command) = {
      id: 50600
    };
  }

  rpc OpenNumBomb (ga.channel_mini_game.OpenNumBombReq) returns (ga.channel_mini_game.OpenNumBombResp) {
    option (logic.gateway.command) = {
      id: 50601
    };
  }
  rpc StartNumBomb (ga.channel_mini_game.StartNumBombReq) returns (ga.channel_mini_game.StartNumBombResp) {
    option (logic.gateway.command) = {
      id: 50602
    };
  }

  rpc PlayNumBomb (ga.channel_mini_game.PlayNumBombReq) returns (ga.channel_mini_game.PlayNumBombResp) {
    option (logic.gateway.command) = {
      id: 50603
    };
  }

  rpc CloseNumBomb (ga.channel_mini_game.CloseNumBombReq) returns (ga.channel_mini_game.CloseNumBombResp) {
    option (logic.gateway.command) = {
      id: 50604
    };
  }

  rpc GetPropConfig (ga.channel_mini_game.GetPropConfigReq) returns (ga.channel_mini_game.GetPropConfigResp) {
    option (logic.gateway.command) = {
      id: 50605
    };
  }
}
