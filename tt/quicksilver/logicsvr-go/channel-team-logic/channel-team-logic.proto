syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-team-logic";
import "channel_team/channel-team-logic_.proto";
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";

service ChannelTeamLogic {
    option (logic.gateway.service_ext) = {
        service_name: "channel-team-logic"
    };

    // 用户进入小队
    rpc JoinChannelTeam (ga.channel_team.JoinChannelTeamReq) returns (ga.channel_team.JoinChannelTeamResp) {
        option (logic.gateway.command) = {
            id: 30356
        };
    }
    // 房主获取申请列表
    rpc GetChannelTeamApplyList (ga.channel_team.GetChannelTeamApplyListReq) returns (ga.channel_team.GetChannelTeamApplyListResp) {
        option (logic.gateway.command) = {
            id: 30357
        };
    }
    // 房主同意用户申请入队
    rpc AgreeChannelTeamApply (ga.channel_team.AgreeChannelTeamApplyReq) returns (ga.channel_team.AgreeChannelTeamApplyResp) {
        option (logic.gateway.command) = {
            id: 30358
        };
    }
    // 用户退出小队或者房主踢用户退出小队
    rpc TickChannelTeamMember (ga.channel_team.TickChannelTeamMemberReq) returns (ga.channel_team.TickChannelTeamMemberResp) {
        option (logic.gateway.command) = {
            id: 30359
        };
    }
    // 获取小队成员信息
    rpc GetChannelTeamMemberList (ga.channel_team.GetChannelTeamMemberListReq) returns (ga.channel_team.GetChannelTeamMemberListResp) {
        option (logic.gateway.command) = {
            id: 30360
        };
    }
    // 获取开黑过的用户列表
    rpc GetGangUpHistory (ga.channel_team.GetGangUpHistoryReq) returns (ga.channel_team.GetGangUpHistoryResp) {
        option (logic.gateway.command) = {
            id: 30361
        };
    }
    // 获取所有的房间用户
    rpc GetAllChannelMember (ga.channel_team.GetAllChannelMemberReq) returns (ga.channel_team.GetAllChannelMemberResp) {
        option (logic.gateway.command) = {
            id: 30362
        };
    }
    // 设置房间小队昵称
    rpc SetGameNickname (ga.channel_team.SetGameNicknameReq) returns (ga.channel_team.SetGameNicknameResp) {
        option (logic.gateway.command) = {
            id: 30363
        };
    }
    // 发送游戏昵称到公屏
//    rpc PushGameNickname (ga.channel_team.PushGameNicknameReq) returns (ga.channel_team.PushGameNicknameResp) {
//        option (logic.gateway.command) = {
//            id: 30364
//        };
//    }
}

