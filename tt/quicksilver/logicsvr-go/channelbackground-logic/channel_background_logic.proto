syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channelbackgroundlogic/channel_background_logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channelbackgroundlogic";

service ChannelBackgroundLogic {
    option (logic.gateway.service_ext) = {
        service_name: "channelbackground-logic"
    };

    rpc GetCurChannelBackgroundInfo (ga.channelbackgroundlogic.GetCurChannelBackgroundInfoReq) returns (ga.channelbackgroundlogic.GetCurChannelBackgroundInfoResp) {
            option (logic.gateway.command) = {
                id: 2020   //改成正确的id
            };
    }

    rpc ChangeCurChannelBackground (ga.channelbackgroundlogic.ChangeCurChannelBackgroundReq) returns (ga.channelbackgroundlogic.ChangeCurChannelBackgroundResp) {
        option (logic.gateway.command) = {
                id: 2021   //改成正确的id
            };
    }

    rpc GetChannelBackgroundInfoList (ga.channelbackgroundlogic.GetChannelBackgroundInfoListReq) returns (ga.channelbackgroundlogic.GetChannelBackgroundInfoListResp) {
        option (logic.gateway.command) = {
                id: 2022   //改成正确的id
            };
    }

    rpc CheckChannelBackgroundUpdate (ga.channelbackgroundlogic.CheckChannelBackgroundUpdateReq) returns (ga.channelbackgroundlogic.CheckChannelBackgroundUpdateResp) {
        option (logic.gateway.command) = {
                id: 2023   //改成正确的id
            };
    }

  rpc GetRecommendBackground (ga.channelbackgroundlogic.GetRecommendBackgroundReq) returns (ga.channelbackgroundlogic.GetRecommendBackgroundResp) {
    option (logic.gateway.command) = {
      id: 2024   //改成正确的id
    };
  }

  rpc GetKHBackgroundInfoList (ga.channelbackgroundlogic.GetKHBackgroundInfoListReq) returns (ga.channelbackgroundlogic.GetKHBackgroundInfoListResp) {
    option (logic.gateway.command) = {
      id: 2025   //改成正确的id
    };
  }

  rpc GetPclfgCurChannelBackground (ga.channelbackgroundlogic.GetPclfgCurChannelBackgroundReq) returns (ga.channelbackgroundlogic.GetPclfgCurChannelBackgroundResp) {
    option (logic.gateway.command) = {
      id: 2026   //改成正确的id
    };
  }
  rpc SetPclfgCurChannelBackground (ga.channelbackgroundlogic.SetPclfgCurChannelBackgroundReq) returns (ga.channelbackgroundlogic.SetPclfgCurChannelBackgroundResp) {
    option (logic.gateway.command) = {
      id: 2027   //改成正确的id
    };
  }
  rpc GetPclfgChannelBackgroundList (ga.channelbackgroundlogic.GetPclfgChannelBackgroundListReq) returns (ga.channelbackgroundlogic.GetPclfgChannelBackgroundListResp) {
    option (logic.gateway.command) = {
      id: 2028   //改成正确的id
    };
  }
}