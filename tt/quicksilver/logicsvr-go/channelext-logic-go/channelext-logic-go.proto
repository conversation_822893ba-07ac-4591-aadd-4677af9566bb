syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel/channel_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channelext-logic-go";

service ChannelExtLogicGo {
    option (logic.gateway.service_ext) = {
        service_name: "channelext-logic-go"
    };

    rpc ChannelGetMicExternInfo (ga.channel.ChannelGetMicExternInfoReq) returns (ga.channel.ChannelGetMicExternInfoResp) {
        option (logic.gateway.command) = {
            id: 30461
      };
    }
    rpc SendChannelGameNick (ga.channel.SendChannelGameNickReq) returns (ga.channel.SendChannelGameNickResp) {
        option (logic.gateway.command) = {
            id: 30462
        };
    }

    rpc ChannelSetEnterControlType (ga.channel.ChannelSetEnterControlTypeReq) returns (ga.channel.ChannelSetEnterControlTypeResp) {
        option (logic.gateway.command) = {
            id: 36401
        };
    }
    rpc ChannelGetEnterControlType (ga.channel.ChannelGetEnterControlTypeReq) returns (ga.channel.ChannelGetEnterControlTypeResp) {
        option (logic.gateway.command) = {
            id: 36402
        };
    }
    rpc ChannelGame (ga.channel.ChannelGameReq) returns (ga.channel.ChannelGameResp) {
        option (logic.gateway.command) = {
            id: 2063
        };
    }

    rpc SendMagicExpression (ga.channel.SendMagicExpressionReq) returns (ga.channel.SendMagicExpressionResp) {
        option (logic.gateway.command) = {
            id: 2067
        };
    }
}
