syntax = "proto3";

package logic.character_channel_logic;


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "character_channel_logic/character_channel_logic.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/character-channel-logic";

service CharacterChannelLogic {
    option (logic.gateway.service_ext) = {
        service_name: "character-channel-logic"
    };

    rpc CharacterApplyOnMic(ga.character_channel_logic.CharacterApplyOnMicReq) returns (ga.character_channel_logic.CharacterApplyOnMicResp) {
        option (logic.gateway.command) = {
          id: 5700
        };
    }
    rpc CharacterAgreeApplyMic(ga.character_channel_logic.CharacterAgreeApplyMicReq) returns (ga.character_channel_logic.CharacterAgreeApplyMicResp) {
        option (logic.gateway.command) = {
            id: 5701
        };
    }

}