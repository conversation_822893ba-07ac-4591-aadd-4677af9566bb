syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "chat_bot_logic/chat_bot_logic.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/chat-bot-logic";

service ChatBotLogic {
    option (logic.gateway.service_ext) = {
    	service_name: "chat-bot-logic"
    };

    // 获取AI伴侣入口
    rpc GetAIPartnerEntrance(ga.chat_bot_logic.GetAIPartnerEntranceRequest) returns(ga.chat_bot_logic.GetAIPartnerEntranceResponse) {
        option (logic.gateway.command) = {
            id: 1300
        };
    }

    // 获取新版本AI伴侣入口
    rpc GetAIPartnerEntranceV2(ga.chat_bot_logic.GetAIPartnerEntranceV2Request) returns(ga.chat_bot_logic.GetAIPartnerEntranceV2Response) {
        option (logic.gateway.command) = {
            id: 1301
        };
    }

    // 获取AI伴侣入口列表 
    rpc GetAIPartnerEntranceList(ga.chat_bot_logic.GetAIPartnerEntranceListRequest) returns(ga.chat_bot_logic.GetAIPartnerEntranceListResponse) {
        option (logic.gateway.command) = {
            id: 1302
        };
    }

    // 获取AI伴侣入口列表 
    rpc GetAIRoleInteractiveConfig(ga.chat_bot_logic.GetAIRoleInteractiveConfigRequest) returns(ga.chat_bot_logic.GetAIRoleInteractiveConfigResponse) {
        option (logic.gateway.command) = {
            id: 1303
        };
    }
    
    // 上报桌宠行为
    rpc ReportAIPetBehavior(ga.chat_bot_logic.ReportAIPetBehaviorRequest) returns(ga.chat_bot_logic.ReportAIPetBehaviorResponse) {
        option (logic.gateway.command) = {
            id: 1304
        };
    }

}
