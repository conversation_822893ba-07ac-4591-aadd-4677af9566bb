syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "chatcardlogic/chat-card-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/chatcardlogic";

service ChatCardLogic {
    // 获取卡片配置
    rpc GetChatCardConfig (ga.chatcardlogic.GetChatCardConfigReq) returns (ga.chatcardlogic.GetChatCardConfigResp) {
        option (logic.gateway.command) = {
            id: 30500
        };
    }

    // 获取卡片状态
    rpc GetChatCardStatus (ga.chatcardlogic.GetChatCardStatusReq) returns (ga.chatcardlogic.GetChatCardStatusResp) {
        option (logic.gateway.command) = {
            id: 30501
        };
    }

    // 开启卡片
    rpc OpenChatCard (ga.chatcardlogic.OpenChatCardReq) returns (ga.chatcardlogic.OpenChatCardResp) {
        option (logic.gateway.command) = {
            id: 30502
        };
    }

    // 关闭卡片
    rpc CloseChatCard (ga.chatcardlogic.CloseChatCardReq) returns (ga.chatcardlogic.CloseChatCardResp) {
        option (logic.gateway.command) = {
            id: 30503
        };
    }

    // 获取卡片列表
    rpc GetChatCardList (ga.chatcardlogic.GetChatCardListReq) returns (ga.chatcardlogic.GetChatCardListResp) {
        option (logic.gateway.command) = {
            id: 30504
        };
    }

    // 打招呼
    rpc SayHi (ga.chatcardlogic.SayHiReq) returns (ga.chatcardlogic.SayHiResp) {
        option (logic.gateway.command) = {
            id: 30505
        };
    }

    // 回复
    rpc Reply (ga.chatcardlogic.ReplyReq) returns (ga.chatcardlogic.ReplyResp) {
        option (logic.gateway.command) = {
            id: 30506
        };
    }

    // 点赞
    rpc LikeChatCard (ga.chatcardlogic.LikeChatCardReq) returns (ga.chatcardlogic.LikeChatCardResp) {
        option (logic.gateway.command) = {
            id: 30507
        };
    }

    // 标记消息已读
    rpc MarkChatCardMsgRead (ga.chatcardlogic.MarkChatCardMsgReadReq) returns (ga.chatcardlogic.MarkChatCardMsgReadResp) {
        option (logic.gateway.command) = {
            id: 30508
        };
    }

    // 获取未读数
    rpc GetChatCardMsgUnreadCount (ga.chatcardlogic.GetChatCardMsgUnreadCountReq) returns (ga.chatcardlogic.GetChatCardMsgUnreadCountResp) {
        option (logic.gateway.command) = {
            id: 30509
        };
    }

    // 获取消息列表
    rpc GetChatCardMsgList (ga.chatcardlogic.GetChatCardMsgListReq) returns (ga.chatcardlogic.GetChatCardMsgListResp) {
        option (logic.gateway.command) = {
            id: 30510
        };
    }

    // 上报感兴趣的标签
    rpc ReportInterestedChatCardTags (ga.chatcardlogic.ReportInterestedChatCardTagsReq) returns (ga.chatcardlogic.ReportInterestedChatCardTagsResp) {
        option (logic.gateway.command) = {
            id: 30511
        };
    }

    // 打招呼（不需要发布卡片）
    rpc SayHiV2 (ga.chatcardlogic.SayHiV2Req) returns (ga.chatcardlogic.SayHiV2Resp) {
        option (logic.gateway.command) = {
            id: 30512
        };
    }
}