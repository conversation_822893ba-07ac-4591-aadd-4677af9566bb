syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "client_conf_mgr/client-conf-mgr_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/client-conf-mgr-logic";

service ClientConfMgrLogic {
    option (logic.gateway.service_ext) = {
      service_name: "client-conf-mgr-logic"
    };

    rpc GetConfFile (ga.client_conf_mgr.GetConfFileReq) returns (ga.client_conf_mgr.GetConfFileResp) {
        option (logic.gateway.command) = {
            id: 30471
      };
    }

    rpc GetConfList (ga.client_conf_mgr.GetConfListReq) returns (ga.client_conf_mgr.GetConfListResp) {
        option (logic.gateway.command) = {
            id: 30472
      };
    }
    rpc CheckAnnouncementUpdate (ga.client_conf_mgr.CheckAnnouncementUpdateReq) returns (ga.client_conf_mgr.CheckAnnouncementUpdateResp) {
        option (logic.gateway.command) = {
            id: 30473
      };
    }
    
}