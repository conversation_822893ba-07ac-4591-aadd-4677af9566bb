syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/client-gen-push-logic";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "clientgenpushlogic/client-gen-push_.proto";

service ClientGenPushLogic {
  option (logic.gateway.service_ext) = {
    service_name: "client-gen-push-logic"
  };

  // buf:lint:ignore RPC_PASCAL_CASE
  rpc GA_GetGenPushSwitch(ga.clientgenpushlogic.GA_GetGenPushSwitchReq) returns (ga.clientgenpushlogic.GA_GetGenPushSwitchResp) {
    option (logic.gateway.command) = {
      id: 30441
    };
  }

  // buf:lint:ignore RPC_PASCAL_CASE
  rpc GA_GetGenPushDoc(ga.clientgenpushlogic.GA_GetGenPushDocReq) returns (ga.clientgenpushlogic.GA_GetGenPushDocResp) {
    option (logic.gateway.command) = {
      id: 30442
    };
  }

  // buf:lint:ignore RPC_PASCAL_CASE
  rpc GA_GetPushFactoryPrivateTemplate(ga.clientgenpushlogic.GA_GetPushFactoryPrivateTemplateReq) returns (ga.clientgenpushlogic.GA_GetPushFactoryPrivateTemplateResp) {
    option (logic.gateway.command) = {
       id: 30443
    };
  }

  rpc GetNewUserPushCntLimit(ga.clientgenpushlogic.GetNewUserPushCntLimitReq) returns (ga.clientgenpushlogic.GetNewUserPushCntLimitResp) {
    option (logic.gateway.command) = {
      id: 30444
    };
  }
}