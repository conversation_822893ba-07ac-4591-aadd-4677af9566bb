syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "competition_entrance_logic/competition-entrance-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/competition-entrance-logic";

service CompetitionEntranceLogic {
    option (logic.gateway.service_ext) = {
        service_name: "competition-entrance-logic"
    };

    rpc GetCompetitionEntrance (ga.competition_entrance_logic.GetCompetitionEntranceReq) returns (ga.competition_entrance_logic.GetCompetitionEntranceResp) {
        option (logic.gateway.command) = {
            id: 33081
        };
    }

    // 获取赛事临时房配置
    rpc GetGameTmpChannelCfg (ga.competition_entrance_logic.GetGameTmpChannelCfgReq) returns (ga.competition_entrance_logic.GetGameTmpChannelCfgResp) {
        option (logic.gateway.command) = {
            id: 33082
        };
    }

    // 获取房间内生效中的赛事入口列表
    rpc GetChannelCompetitionEntranceList (ga.competition_entrance_logic.GetChannelCompetitionEntranceListReq) returns (ga.competition_entrance_logic.GetChannelCompetitionEntranceListResp) {
        option (logic.gateway.command) = {
            id: 33083
        };
    }

    // 查询是否展示悬浮组件/弹窗
    rpc CheckShowFloatingComponent (ga.competition_entrance_logic.CheckShowFloatingComponentReq) returns (ga.competition_entrance_logic.CheckShowFloatingComponentResp) {
        option (logic.gateway.command) = {
            id: 33084
        };
    }
}