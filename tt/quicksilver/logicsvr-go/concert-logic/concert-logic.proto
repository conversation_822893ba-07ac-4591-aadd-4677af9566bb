syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "concert_logic/concert-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/concertlogic";

service ConcertLogic {
  option (logic.gateway.service_ext) = {
    service_name: "concert-logic"
  };

  // 获取歌曲选项
  rpc GetConcertSongOpts (ga.concert_logic.GetConcertSongOptsReq) returns (ga.concert_logic.GetConcertSongOptsResp) {
    option (logic.gateway.command) = {
      id: 33300
    };
  }

  // 搜索歌曲
  rpc SearchConcertSong (ga.concert_logic.SearchConcertSongReq) returns (ga.concert_logic.SearchConcertSongResp) {
    option (logic.gateway.command) = {
      id: 33301
    };
  }

  // 获取歌曲
  rpc GetConcertSongList (ga.concert_logic.GetConcertSongListReq) returns (ga.concert_logic.GetConcertSongListResp) {
    option (logic.gateway.command) = {
      id: 33302
    };
  }

  // 获取演唱资源
  rpc GetAllConcertResource (ga.concert_logic.GetAllConcertResourceReq) returns (ga.concert_logic.GetAllConcertResourceResp) {
    option (logic.gateway.command) = {
      id: 33303
    };
  }

  // 点歌
  rpc StartConcertSinging (ga.concert_logic.StartConcertSingingReq) returns (ga.concert_logic.StartConcertSingingResp) {
    option (logic.gateway.command) = {
      id: 33304
    };
  }

  // 获取乐谱
  rpc GetMusicBook (ga.concert_logic.GetMusicBookReq) returns (ga.concert_logic.GetMusicBookResp) {
    option (logic.gateway.command) = {
      id: 33305
    };
  }

  // 完成乐谱下载
  rpc CompleteDownloadingMusicBook (ga.concert_logic.CompleteDownloadingMusicBookReq) returns (ga.concert_logic.CompleteDownloadingMusicBookResp) {
    option (logic.gateway.command) = {
      id: 33306
    };
  }

  // 停止演唱
  rpc StopConcertSinging (ga.concert_logic.StopConcertSingingReq) returns (ga.concert_logic.StopConcertSingingResp) {
    option (logic.gateway.command) = {
      id: 33307
    };
  }

  // 获取演唱信息
  rpc GetConcertInfo (ga.concert_logic.GetConcertInfoReq) returns (ga.concert_logic.GetConcertInfoResp) {
    option (logic.gateway.command) = {
      id: 33308
    };
  }

  // 获取演唱信息
  rpc UpdateBackingTrackStatus (ga.concert_logic.UpdateBackingTrackStatusReq) returns (ga.concert_logic.UpdateBackingTrackStatusResp) {
    option (logic.gateway.command) = {
      id: 33309
    };
  }

  // 上报成功数量
  rpc ReportConcertSuccCount (ga.concert_logic.ReportConcertSuccCountReq) returns (ga.concert_logic.ReportConcertSuccCountResp) {
    option (logic.gateway.command) = {
      id: 33310
    };
  }

  // 中途加入
  rpc JoinConcert (ga.concert_logic.JoinConcertReq) returns (ga.concert_logic.JoinConcertResp) {
    option (logic.gateway.command) = {
      id: 33311
    };
  }

  // 获取形象配置
  rpc GetAllConcertImage (ga.concert_logic.GetAllConcertImageReq) returns (ga.concert_logic.GetAllConcertImageResp) {
    option (logic.gateway.command) = {
      id: 33312
    };
  }

  // 设置形象
  rpc SetConcertUserImage (ga.concert_logic.SetConcertUserImageReq) returns (ga.concert_logic.SetConcertUserImageResp) {
    option (logic.gateway.command) = {
      id: 33313
    };
  }

  // 获取麦上用户形象
  rpc GetAllConcertOnMicUserImage (ga.concert_logic.GetAllConcertOnMicUserImageReq) returns (ga.concert_logic.GetAllConcertOnMicUserImageResp) {
    option (logic.gateway.command) = {
      id: 33314
    };
  }

  // 通过歌曲id获取歌曲
  rpc GetConcertSongById (ga.concert_logic.GetConcertSongByIdReq) returns (ga.concert_logic.GetConcertSongByIdResp) {
    option (logic.gateway.command) = {
      id: 33317
    };
  }

  // 获取最新上架的歌曲
  rpc GetRecentUploadedSong (ga.concert_logic.GetRecentUploadedSongReq) returns (ga.concert_logic.GetRecentUploadedSongResp) {
    option (logic.gateway.command) = {
      id: 33318
    };
  }
}