syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "dark_gift_bonus_logic/dark-gift-bonus-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/dark-gift-bonus-logic";


service DarkGiftBonusLogic {
  option (logic.gateway.service_ext) = {
    service_name: "dark-gift-bonus-logic"
  };

  // 检查合成入口是否开启
  rpc CheckGiftComposeEntry (ga.dark_gift_bonus_logic.CheckGiftComposeEntryReq) returns (ga.dark_gift_bonus_logic.CheckGiftComposeEntryResp) {
    option (logic.gateway.command) = {
      id: 2197
    };
  }

  // 获取中奖记录
  rpc GetDarkGiftBonusRecord (ga.dark_gift_bonus_logic.GetDarkGiftBonusRecordReq) returns (ga.dark_gift_bonus_logic.GetDarkGiftBonusRecordResp) {
    option (logic.gateway.command) = {
      id: 32050
    };
  }

  // 检查近两个月有无获奖记录（用于判断是否展示“奖励记录”tab）
  rpc CheckIfExistBonusRecord (ga.dark_gift_bonus_logic.CheckIfExistBonusRecordReq) returns (ga.dark_gift_bonus_logic.CheckIfExistBonusRecordResp) {
    option (logic.gateway.command) = {
      id: 32051
    };
  }

}