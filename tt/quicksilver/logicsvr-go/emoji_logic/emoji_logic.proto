syntax="proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/emoji_logic";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "emoji/emoji_.proto";
import "emoji/third_party_emoji.proto";

service EmojiLogic {
    option (logic.gateway.service_ext) = {
        service_name: "emoji-logic"
    };

    rpc SaveEmoji( ga.emoji.SaveEmojiReq ) returns( ga.emoji.SaveEmojiResp ) {
        option (logic.gateway.command) = {
            id: 2500
        };
    }

    rpc DeleteEmoji( ga.emoji.DeleteEmojiReq ) returns( ga.emoji.DeleteEmojiResp ) {
        option (logic.gateway.command) = {
            id: 2501
        };
    }

    rpc GetEmojiPkgList( ga.emoji.GetEmojiPkgListReq ) returns( ga.emoji.GetEmojiPkgListResp ) {
        option (logic.gateway.command) = {
            id: 2502
        };
    }

    rpc GetEmojiListByPkg( ga.emoji.GetEmojiListByPkgReq ) returns( ga.emoji.GetEmojiListByPkgResp ) {
        option (logic.gateway.command) = {
            id: 2503
        };
    }

    rpc TestEmojiA( ga.emoji.GetEmojiPkgListReq ) returns( ga.emoji.GetEmojiPkgListResp ) {
        option (logic.gateway.command) = {
            id: 3038
        };
    }

    rpc GetHotEmoji( ga.emoji.GetHotEmojiReq ) returns( ga.emoji.GetHotEmojiResp ) {
        option (logic.gateway.command) = {
          id: 2511
        };
    }

    rpc GetSearchEmoji( ga.emoji.GetSearchEmojiReq ) returns( ga.emoji.GetSearchEmojiResp ) {
        option (logic.gateway.command) = {
          id: 2512
        };
    }

    rpc CheckHotEmojiEntrance(ga.emoji.CheckHotEmojiEntranceReq) returns (ga.emoji.CheckHotEmojiEntranceResp) {
        option (logic.gateway.command) = {
          id: 2513,
        };
    }
}