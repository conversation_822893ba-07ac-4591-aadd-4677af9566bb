syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "eventreportlogic/event-report-logic_.proto";
// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/eventreportlogic";

service EventReportLogic {
    option (logic.gateway.service_ext) = {
      service_name: "event-report-logic"
   };

    // 上报信息 在房间的信息
    rpc ReportPlayroomEvent (ga.eventreportlogic.ReportPlayroomEventReq) returns (ga.eventreportlogic.ReportPlayroomEventResp) {
        option (logic.gateway.command) = {
            id: 90000
        };
    }

    //上报新用户统计的时间
    rpc RecordNewUserTimeCount (ga.eventreportlogic.RecordNewUserTimeCountReq) returns (ga.eventreportlogic.RecordNewUserTimeCountResp) {
        option (logic.gateway.command) = {
            id: 90001
        };
    }


    //上报开闭麦事件
    rpc ReportUserMicStatus (ga.eventreportlogic.ReportMicStatusReq) returns (ga.eventreportlogic.ReportMicStatusResp) {
        option (logic.gateway.command) = {
            id: 90002
        };
    }
}