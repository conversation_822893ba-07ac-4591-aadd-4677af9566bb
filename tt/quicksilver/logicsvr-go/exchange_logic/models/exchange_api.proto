syntax="proto3";

import "google/protobuf/any.proto";

package exchange_logic.models;

message Response {
    int32 code = 1;
    string msg = 2;
    uint32 server_time = 3;
    google.protobuf.Any data = 4;
}

enum CurrencyType {
    Unspecified = 0;    // 未指定
    TBean = 1;          // T豆
    Points = 2;         // 积分
    RedDiamond = 3;     // 红钻
    Commission = 4;     // 佣金
}

message RedDiamondExchangeItem {
    uint32 item_id = 1;         // 商品id
    string name = 2;            // 名称
    string desc = 3;            // 描述
    uint32 amount = 4;          // 按基础比例获得的红钻数量
    uint32 bonus_amount = 5;    // 额外赠送的红钻数量
    CurrencyType currency_type = 6;   // 货币类型, see CurrencyType
    uint32 price = 7;           // 价格
}

message GetRedDiamondExchangeItemListResp {
    CurrencyType currency_type = 1;                 // 货币类型, see CurrencyType
    repeated RedDiamondExchangeItem item_list = 2;  // 商品价格列表
}

message ExchangeResult {
    //    CurrencyType currency_type = 1;
    uint32 amount = 2;
    uint32 bonus_amount = 3;
    uint32 price = 4;
}

message ExchangeReq {
    CurrencyType currency_type = 1;
    ExchangeResult expected_result = 2;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ExchangeResp {
    Transaction record = 1;
    bool Done = 2;
}

message ProfileResp {
    int32 red_diamonds = 1;
    int32 tbeans = 2;
    int32 points = 3;
    ExchangeResult max_tbean_to_red_diamond = 4;
    ExchangeResult max_points_to_red_diamond = 5;
}

message Transaction {
    uint32          id = 1;
    uint32          uid = 2;
    CurrencyType    target_type = 3;
    uint32          amount = 4;
    CurrencyType    currency_type = 5;
    uint32          price = 6;
    uint32          create_at = 7;
    string          desc = 8;
}

message GetTransactionsResp {
    repeated Transaction list = 1;
}

message EstimateResp {
    ExchangeResult expected_result = 1;
}

message PointsToTBeanReq {
    string order_id = 1;
    uint32 points = 2;
    string market_id = 3;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message PointsToTBeanResp {
    Transaction record = 1;
    bool Done = 2;
}

message VerifyCodeResp{
    uint32 cooldown = 1;
    int32 code = 2;
    string msg = 3;
}

message PointsToCashRecord{
    string  id = 1;
    string  bank_name = 2;
    string  card_no = 3;
    float   income = 4;
    uint32  status = 5;
    uint32  time = 6;
    string  remark = 7;
}

message PointsToCashRecordResult{
    repeated PointsToCashRecord list = 1;
    uint32 total = 2;
}

message PointsToCashInfo{
    uint32 income = 1;
    uint32 points = 2;
    uint32 withdraw_status =3;
}

message PointsToCashResp {
    PointsToCashInfo  points_info = 1;
    PointsToCashRecordResult drawing_record = 2;
}