syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "fellow_logic/fellow-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/fellow-logic";


service FellowLogic {
  option (logic.gateway.service_ext) = {
    service_name: "fellow-logic"
  };

  // 获取挚友名单
  rpc GetFellowList (ga.fellow_logic.GetFellowListReq) returns (ga.fellow_logic.GetFellowListResp) {
    option (logic.gateway.command) = {
      id: 30661   //改成正确的id
    };
  }

  // 获取可以申请挚友关系的用户名单
  rpc GetFellowCandidateList (ga.fellow_logic.GetFellowCandidateListReq) returns (ga.fellow_logic.GetFellowCandidateListResp) {
    option (logic.gateway.command) = {
      id: 30662   //改成正确的id
    };
  }

  // 获取对应用户的挚友申请相关信息
  rpc GetFellowCandidateInfo (ga.fellow_logic.GetFellowCandidateInfoReq) returns (ga.fellow_logic.GetFellowCandidateInfoResp) {
    option (logic.gateway.command) = {
      id: 30663   //改成正确的id
    };
  }

  // 发送挚友申请（邀请函）
  rpc SendFellowInvite (ga.fellow_logic.SendFellowInviteReq) returns (ga.fellow_logic.SendFellowInviteResp) {
    option (logic.gateway.command) = {
      id: 30664   //改成正确的id
    };
  }

  // 获取对应id的邀请函信息
  rpc GetFellowInviteInfoById (ga.fellow_logic.GetFellowInviteInfoByIdReq) returns (ga.fellow_logic.GetFellowInviteInfoByIdResp) {
    option (logic.gateway.command) = {
      id: 30665   //改成正确的id
    };
  }

  // 获取待处理的邀请函列表
  rpc GetFellowInviteList (ga.fellow_logic.GetFellowInviteListReq) returns (ga.fellow_logic.GetFellowInviteListResp) {
    option (logic.gateway.command) = {
      id: 30666   //改成正确的id
    };
  }

  // 处理挚友申请
  rpc HandleFellowInvite (ga.fellow_logic.HandleFellowInviteReq) returns (ga.fellow_logic.HandleFellowInviteResp) {
    option (logic.gateway.command) = {
      id: 30667   //改成正确的id
    };
  }

  // 检查邀请坑位
  rpc CheckFellowInvite (ga.fellow_logic.CheckFellowInviteReq) returns (ga.fellow_logic.CheckFellowInviteResp) {
    option (logic.gateway.command) = {
      id: 30668   //改成正确的id
    };
  }

  // 解锁挚友位
  rpc UnlockFellowPosition (ga.fellow_logic.UnlockFellowPositionReq) returns (ga.fellow_logic.UnlockFellowPositionResp) {
    option (logic.gateway.command) = {
      id: 30669   //改成正确的id
    };
  }

  // 取消邀请
  rpc CancelFellowInvite (ga.fellow_logic.CancelFellowInviteReq) returns (ga.fellow_logic.CancelFellowInviteResp) {
    option (logic.gateway.command) = {
      id: 30672   //改成正确的id
    };
  }

    // 获取挚友分数
  rpc GetFellowPoint (ga.fellow_logic.GetFellowPointReq) returns (ga.fellow_logic.GetFellowPointResp) {
    option (logic.gateway.command) = {
      id: 30673   //改成正确的id
    };
  }

  // 获取挚友分数
  rpc GetFellowPresentDetail (ga.fellow_logic.GetFellowPresentDetailReq) returns (ga.fellow_logic.GetFellowPresentDetailResp) {
    option (logic.gateway.command) = {
      id: 30674   //改成正确的id
    };
  }

  // 获取挚友分数
  rpc SendFellowPresent (ga.fellow_logic.SendFellowPresentReq) returns (ga.fellow_logic.SendFellowPresentResp) {
    option (logic.gateway.command) = {
      id: 30675   //改成正确的id
    };
  }

  // 获取挚友关系
  rpc GetFellowInfoByUid (ga.fellow_logic.GetFellowInfoByUidReq) returns (ga.fellow_logic.GetFellowInfoByUidResp) {
    option (logic.gateway.command) = {
      id: 30676   //改成正确的id
    };
  }

  // 送房间邀请函
  rpc SendChannelFellowInvite (ga.fellow_logic.SendChannelFellowInviteReq) returns (ga.fellow_logic.SendChannelFellowInviteResp) {
    option (logic.gateway.command) = {
      id: 30677   //改成正确的id
    };
  }

  // 处理房间邀请函
  rpc HandleChannelFellowInvite (ga.fellow_logic.HandleChannelFellowInviteReq) returns (ga.fellow_logic.HandleChannelFellowInviteResp) {
    option (logic.gateway.command) = {
      id: 30678   //改成正确的id
    };
  }

  // 房间赠送挚友信物
  rpc ChannelSendFellowPresent (ga.fellow_logic.ChannelSendFellowPresentReq) returns (ga.fellow_logic.ChannelSendFellowPresentResp) {
    option (logic.gateway.command) = {
      id: 30679   //改成正确的id
    };
  }

  // 获取房间资料卡挚友名单
  rpc GetRoomFellowList (ga.fellow_logic.GetRoomFellowListReq) returns (ga.fellow_logic.GetRoomFellowListResp) {
    option (logic.gateway.command) = {
      id: 30680   //改成正确的id
    };
  }


  // 获取所有房间内邀请
  rpc GetAllChannelFellowInvite (ga.fellow_logic.GetAllChannelFellowInviteReq) returns (ga.fellow_logic.GetAllChannelFellowInviteResp) {
    option (logic.gateway.command) = {
      id: 30681   //改成正确的id
    };
  }

  // 获取房间挚友信息
  rpc GetChannelFellowCandidateInfo (ga.fellow_logic.GetChannelFellowCandidateInfoReq) returns (ga.fellow_logic.GetChannelFellowCandidateInfoResp) {
    option (logic.gateway.command) = {
      id: 30682   //改成正确的id
    };
  }

  // 获取麦上挚友信息
  rpc GetOnMicFellowList (ga.fellow_logic.GetOnMicFellowListReq) returns (ga.fellow_logic.GetOnMicFellowListResp) {
    option (logic.gateway.command) = {
      id: 30683   //改成正确的id
    };
  }
  
  // 获取我发出的邀请函列表
  rpc GetSendInviteList (ga.fellow_logic.GetSendInviteListReq) returns (ga.fellow_logic.GetSendInviteListResp) {
    option (logic.gateway.command) = {
      id: 30684   //改成正确的id
    };
  }

  // 获取稀缺关系配置
  rpc GetRareConfig (ga.fellow_logic.GetRareConfigReq) returns (ga.fellow_logic.GetRareConfigResp) {
    option (logic.gateway.command) = {
      id: 30685   //改成正确的id
    };
  }

   // 切换绑定的稀缺关系
  rpc SetBindRelation (ga.fellow_logic.SetBindRelationReq) returns (ga.fellow_logic.SetBindRelationResp) {
    option (logic.gateway.command) = {
      id: 30686   //改成正确的id
    };
  }


   // 获取房间稀缺关系配置
  rpc GetChannelRareConfig (ga.fellow_logic.GetChannelRareConfigReq) returns (ga.fellow_logic.GetChannelRareConfigResp) {
    option (logic.gateway.command) = {
      id: 30687   //改成正确的id
    };
  }
  
   // 获取两个UID的所有稀缺关系列表
  rpc GetRareList (ga.fellow_logic.GetRareListReq) returns (ga.fellow_logic.GetRareListResp) {
    option (logic.gateway.command) = {
      id: 30688   //改成正确的id
    };
  }
   // 删除稀缺关系
  rpc DelRare (ga.fellow_logic.DelRareReq) returns (ga.fellow_logic.DelRareResp) {
    option (logic.gateway.command) = {
      id: 30689
    };
  }
   // 换绑关系
  rpc ChangeFellowBindType (ga.fellow_logic.ChangeFellowBindTypeReq) returns (ga.fellow_logic.ChangeFellowBindTypeResp) {
    option (logic.gateway.command) = {
      id: 30690
    };
  }


  // 获取用户正在使用挚友小屋列表
  rpc GetFellowHouseInuseList (ga.fellow_logic.GetFellowHouseInuseListRequest) returns (ga.fellow_logic.GetFellowHouseInuseListResponse) {
    option (logic.gateway.command) = {
      id: 30691
    };
  }
  // 获取用户挚友小屋入口信息
  rpc GetFellowHouseEntryInfo (ga.fellow_logic.GetFellowHouseEntryInfoRequest) returns (ga.fellow_logic.GetFellowHouseEntryInfoResponse) {
    option (logic.gateway.command) = {
      id: 30692
    };
  }

}