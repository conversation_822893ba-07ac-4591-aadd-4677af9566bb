syntax="proto2";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/find_friends_logic";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "find_friends/find_friends_.proto";

service FindFriendsLogic {
    option (logic.gateway.service_ext) = {
        service_name: "find-friends-logic"
    };

    /** 获取扩圈信息(包含设置) **/
    rpc FindFriendsGetMyInfo( ga.find_friends.FindFriendsGetMyInfoReq ) returns( ga.find_friends.FindFriendsGetMyInfoResp ) {
        option (logic.gateway.command) = {
            id: 2101
        };
    }

    /** 更新扩圈信息 **/
	rpc FindFriendsUpdateMyInfo( ga.find_friends.FindFriendsUpdateMyInfoReq ) returns( ga.find_friends.FindFriendsUpdateMyInfoResp ) {
        option (logic.gateway.command) = {
            id: 2102
        };
    }

    /** 更新照片 **/
	rpc FindFriendsUpdatePhotos( ga.find_friends.FindFriendsUpdatePhotosReq ) returns( ga.find_friends.FindFriendsUpdatePhotosResp ) {
        option (logic.gateway.command) = {
            id: 2103
        };
    }
    
    /** 获取扩圈卡片列表 **/
	rpc FindFriendsGetUserCards( ga.find_friends.FindFriendsGetUserCardsReq ) returns( ga.find_friends.FindFriendsGetUserCardsResp ) {
        option (logic.gateway.command) = {
            id: 2104
        };
    }

    /** 扩圈卡片操作 **/
	rpc FindFriendsOperateOnCards( ga.find_friends.FindFriendsOperateOnCardsReq ) returns( ga.find_friends.FindFriendsOperateOnCardsResp ) {
        option (logic.gateway.command) = {
            id: 2105
        };
    }

    /** 获取游戏列表 **/
	rpc FindFriendsGetGameList( ga.find_friends.FindFriendsGetGameListReq ) returns( ga.find_friends.FindFriendsGetGameListResp ) {
        option (logic.gateway.command) = {
            id: 2106
        };
    }
    
    rpc GetQuickMatchConfigurations( ga.find_friends.GetQuickMatchConfigurationsReq ) returns( ga.find_friends.GetQuickMatchConfigurationsResp ) {
        option (logic.gateway.command) = {
            id: 2120
        };
    }

    rpc StartQuickMatch( ga.find_friends.StartQuickMatchReq ) returns( ga.find_friends.StartQuickMatchResp ) { 
        option (logic.gateway.command) = {
            id: 2121
        };
    }

    rpc CancelQuickMatch( ga.find_friends.CancelQuickMatchReq ) returns( ga.find_friends.CancelQuickMatchResp ) { 
        option (logic.gateway.command) = {
            id: 2122
        };
    }

    rpc GetQuickMatchOnlineUsers( ga.find_friends.GetQuickMatchOnlineUsersReq ) returns( ga.find_friends.GetQuickMatchOnlineUsersResp ) {
        option (logic.gateway.command) = {
            id: 2123
        };
    }

    rpc QuickMatchKeepAlive( ga.find_friends.QuickMatchKeepAliveReq ) returns( ga.find_friends.QuickMatchKeepAliveResp ) {
        option (logic.gateway.command) = {
            id: 2124
        };
    }
}
