syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "game_card/game_card_.proto";
import "channel/channel_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/game-card-logic";

service GameCardLogic {
    option (logic.gateway.service_ext) = {
      service_name: "game-card-logic"
    };

    rpc BatchCreateGameCard (ga.game_card.BatchCreateGameCardReq) returns (ga.game_card.BatchCreateGameCardResp) {
      option (logic.gateway.command) =  {
        id:31250
      };
    }
    rpc GetGameCardConfByTabIds (ga.game_card.GetGameCardConfByTabIdsReq) returns (ga.game_card.GetGameCardConfByTabIdsResp) {
      option (logic.gateway.command) =  {
        id:31251
      };
    }

    rpc GetGameCardConf (ga.game_card.GetGameCardConfReq) returns (ga.game_card.GetGameCardConfResp) {
      option (logic.gateway.command) =  {
        id:31300
      };
    }
    rpc ModifyGameCardInChannel (ga.game_card.ModifyGameCardInChannelReq) returns (ga.game_card.ModifyGameCardInChannelResp) {
        option (logic.gateway.command) =  {
            id:31301
      };
    }
    //进房获取麦上用户游戏卡信息
    rpc GetChannelMicroUserGameCard (ga.channel.GetChannelMicroUserGameCardReq) returns (ga.channel.GetChannelMicroUserGameCardResp) {
      option (logic.gateway.command) =  {
        id:31302
      };
    }

    //usertaglogic重构过来的命令号
    rpc GetAllGameCardConf (ga.game_card.GetAllGameCardConfReq) returns (ga.game_card.GetAllGameCardConfResp) {
      option (logic.gateway.command) =  {
        id:31303
      };
    }
    rpc GetGameCard (ga.game_card.GetGameCardReq) returns (ga.game_card.GetGameCardResp) {
      option (logic.gateway.command) =  {
        id:31304
      };
    }
    rpc SetGameCard (ga.game_card.SetGameCardReq) returns (ga.game_card.SetGameCardResp) {
      option (logic.gateway.command) = {
        id:31305
      };
    }
    rpc CreateGameCard (ga.game_card.CreateGameCardReq) returns (ga.game_card.CreateGameCardResp) {
      option (logic.gateway.command) = {
        id:31306
      };
    }
    rpc DeleteGameCard (ga.game_card.DeleteGameCardReq) returns (ga.game_card.DeleteGameCardResp) {
      option (logic.gateway.command) = {
        id:31307
      };
    }
    rpc CreateGameCardInRegister (ga.game_card.CreateGameCardInRegisterReq) returns (ga.game_card.CreateGameCardInRegisterResp) {
      option (logic.gateway.command) = {
        id:31308
      };
    }
    rpc GetGameCardByTab (ga.game_card.GetGameCardByTabReq) returns (ga.game_card.GetGameCardByTabResp) {
      option (logic.gateway.command) = {
        id:31309
      };
    }

    rpc SendChannelGameNick (ga.channel.SendChannelGameNickReq) returns (ga.channel.SendChannelGameNickResp) {
      option (logic.gateway.command) = {
        id: 30462
        deprecated: true
      };
    }
}