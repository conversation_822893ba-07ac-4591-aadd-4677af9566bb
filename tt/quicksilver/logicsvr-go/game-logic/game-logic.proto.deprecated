syntax = "proto3";

package logic;

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/game-logic";


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "tt/quicksilver/game_.proto";


service GameLogic {
    /**
     * @name: 
     * @msg:热门游戏 
     * @param {*}
     * @return {*}
     */
    rpc HotGames (ga.HotGamesReq) returns (ga.HotGamesResp) {
        option (logic.gateway.command) = {
            id: 83
        };
    }
    /**
     * @name: 
     * @msg: 搜索游戏
     * @param {*}
     * @return {*}
     */
    rpc SearchGame (ga.SearchGameReq) returns (ga.SearchGameResp) {
        option (logic.gateway.command) = {
            id: 93
        };
    }

    /**
     * @name: 
     * @msg: 
     * @param {*}
     * @return {*}
     */
    rpc FeaturedGames (ga.FeaturedGamesReq) returns (ga.FeaturedGamesResp) {
        option (logic.gateway.command) = {
            id: 95
        };
    }

    /**
     * @name: 
     * @msg: 客户端游戏名匹配服务器游戏
     * @param {*}
     * @return {*}
     */
    rpc MatchUserGame (ga.MatchUserGameReq) returns (ga.MatchUserGameResp) {
        option (logic.gateway.command) = {
            id: 139
        };
    }
    /**
     * @name: 
     * @msg: 获取浮窗游戏黑白名单
     * @param {*}
     * @return {*}
     */
    rpc GetGameConfig (ga.GetGameConfigReq) returns (ga.GetGameConfigResp) {
        option (logic.gateway.command) = {
            id: 166
        };
    }
    /**
     * @name: 
     * @msg: 公会正在玩的游戏
     * @param {*}
     * @return {*}
     */
    rpc GuildPlayingGame (ga.GuildPlayingGameReq) returns (ga.GuildPlayingGameResp) {
        option (logic.gateway.command) = {
            id: 167
        };
    }

    /**
     * @name: 
     * @msg: 	客户报上报游戏包下载统计(+1)
     * @param {*}
     * @return {*}
     */
    rpc IncreaseGamePackageDownloadCount (ga.IncreaseGamePackageDownloadCountReq) returns (ga.IncreaseGamePackageDownloadCountResp) {
        option (logic.gateway.command) = {
            id: 201
        };
    }
    /** 运营游戏 BEGIN **/
    rpc TopGameGetList (ga.TopGameGetListReq) returns (ga.TopGameGetListResp) {
        option (logic.gateway.command) = {
            id: 320
        };
    }
    rpc TopGameGetDownloadUrl (ga.TopGameGetDownloadUrlReq) returns (ga.TopGameGetDownloadUrlResp) {
        option (logic.gateway.command) = {
            id: 321
        };
    }
    rpc GetGameTabList (ga.GetGameTabListReq) returns (ga.GetGameTabListResp) {
        option (logic.gateway.command) = {
            id: 323
        };
    }
    rpc GetDiscoveryGame (ga.GetDiscoveryGameReq) returns (ga.GetDiscoveryGameResp) {
        option (logic.gateway.command) = {
            id: 324
        };
    }
    //游戏中心内容 大杂烩拉取
    rpc GameTabGetListV2 (ga.GetGameCenterTabListReq) returns (ga.GetGameCenterTabListResp) {
        option (logic.gateway.command) = {
            id: 340;
        };
    }
    //拉取限时任务 VIP专区这一部分的主副标题
    rpc GetDiscoverPageContent(ga.GetDiscoverPageContentReq) returns (ga.GetDiscoverPageContentResp) {
        option (logic.gateway.command) = {
            id: 341
        };
    }
    //根据游戏分类拉取游戏
    rpc GameGetGameByTag (ga.GetGameByTagReq) returns (ga.GetGameByTagResp) {
        option (logic.gateway.command) = {
            id: 342
        };
    }
    //拉取游戏分类
    rpc GameGetGameTag (ga.GetGameTagReq) returns (ga.GetGameTagResp) {
        option (logic.gateway.command) = {
            id: 343
        };
    }
    //开黑及好友热玩
    rpc GameGetPopGame (ga.GetPopGameReq) returns (ga.GetPopGameResp) {
        option (logic.gateway.command) = {
            id: 344
        };
    }

    /** 运营游戏 END **/

    /** 下载管理器 BEGIN **/
    rpc CheckGameUpgrade (ga.CheckGameUpgradeReq) returns (ga.CheckGameUpgradeResp) {
        option (logic.gateway.command) = {
            id: 330
        };
    }
    /** 下载管理器 END **/
    
    /** 新游戏相关 BEGIN**/
    rpc SearchGameAndCircle (ga.SearchGameAndCircleReq) returns (ga.SearchGameAndCircleResp) {
        option (logic.gateway.command) = {
            id: 490
        };
    }
    rpc SearchGuildPlying (ga.SearchGuildPlayingReq) returns (ga.SearchGuildPlayingResp) {
        option (logic.gateway.command) = {
            id: 491
        };
    }
    rpc GetBothLikeGame (ga.GetBothLikeGameReq) returns (ga.GetBothLikeGameResp) {
        option (logic.gateway.command) = {
            id: 492
        };
    }
    /** 新游戏相关 END*/

    /** 游戏推荐 BEGIN **/
    rpc GetGameRecommendCardList (ga.GetGameRecommendCardListReq) returns (ga.GetGameRecommendCardListResp) {
        option (logic.gateway.command) = {
            id:  701
        };
    }
    /** 游戏推荐 END **/

    // 拉取公会部落公告列表
    rpc GameGetAnnDetail (ga.GameGetAnnDetailReq) returns (ga.GameGetAnnDetailResp) {
        option (logic.gateway.command) = {
            id: 357;
        };
    }

}
