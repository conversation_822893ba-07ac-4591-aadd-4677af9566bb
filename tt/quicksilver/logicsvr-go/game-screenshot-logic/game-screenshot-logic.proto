syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "game_screenshot_logic/game-screenshot-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/game-screenshot-logic";

service GameScreenshotLogic {
    option (logic.gateway.service_ext) = {
        service_name: "game-screenshot-logic"
    };

    rpc GetGameScreenshotSummary (ga.game_screenshot_logic.GetGameScreenshotSummaryReq) returns (ga.game_screenshot_logic.GetGameScreenshotSummaryResp) {
        option (logic.gateway.command) = {
            id: 36500
        };
    }

    rpc GetGameScreenshotSetting (ga.game_screenshot_logic.GetGameScreenshotSettingReq) returns (ga.game_screenshot_logic.GetGameScreenshotSettingResp) {
        option (logic.gateway.command) = {
            id: 36501
        };
    }

    rpc UpdateGameScreenshotSetting (ga.game_screenshot_logic.UpdateGameScreenshotSettingReq) returns (ga.game_screenshot_logic.UpdateGameScreenshotSettingResp) {
        option (logic.gateway.command) = {
            id: 36502
        };
    }

    rpc GetGameScreenshotHint (ga.game_screenshot_logic.GetGameScreenshotHintReq) returns (ga.game_screenshot_logic.GetGameScreenshotHintResp) {
        option (logic.gateway.command) = {
            id: 36503
        };
    }

    rpc GetDiyGameScreenshot (ga.game_screenshot_logic.GetDiyGameScreenshotReq) returns (ga.game_screenshot_logic.GetDiyGameScreenshotResp) {
        option (logic.gateway.command) = {
            id: 36504
        };
    }

    rpc SetDiyGameScreenshotFinish (ga.game_screenshot_logic.SetDiyGameScreenshotFinishReq) returns (ga.game_screenshot_logic.SetDiyGameScreenshotFinishResp) {
        option (logic.gateway.command) = {
            id: 36506
        };
    }

    rpc GetGameScreenshotGuide (ga.game_screenshot_logic.GetGameScreenshotGuideReq) returns (ga.game_screenshot_logic.GetGameScreenshotGuideResp) {
        option (logic.gateway.command) = {
            id: 36507
        };
    }
}