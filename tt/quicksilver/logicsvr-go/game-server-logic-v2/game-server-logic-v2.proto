syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "game_server_v2/game-server-v2_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/game-server-logic-v2";

service GameServerLogicV2 {
    option (logic.gateway.service_ext) = {
        service_name: "game-server-logic-v2"
    };

    rpc GetScanGameListConf(ga.game_server_v2.GetScanGameListConfReq) returns (ga.game_server_v2.GetScanGameListConfResp) {
        option (logic.gateway.command) = {
          id: 30701
        };
    }
    rpc ReportGameScanResult(ga.game_server_v2.ReportGameScanResultReq) returns (ga.game_server_v2.ReportGameScanResultResp) {
        option (logic.gateway.command) = {
            id: 30702
        };
    }
}