syntax = "proto3";

package logic.game_ugc_logic;


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "game_ugc/game_ugc.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/game-ugc-logic";

service GameUgcLogic {
    option (logic.gateway.service_ext) = {
        service_name: "game-ugc-logic"
    };

    rpc GetGameNewsFeeds(ga.game_ugc.GetGameNewsFeedsReq) returns (ga.game_ugc.GetGameNewsFeedsResp) {
        option (logic.gateway.command) = {
          id: 5400
        };
    }
    
    rpc GamePostPost(ga.game_ugc.GamePostPostReq) returns (ga.game_ugc.GamePostPostResp) {
        option (logic.gateway.command) = {
            id: 5401
        };
    }
    
    rpc GetConfigTabByTabId(ga.game_ugc.GetConfigTabByTabIdReq) returns (ga.game_ugc.GetConfigTabByTabIdResp) {
        option (logic.gateway.command) = {
            id: 5403
        };
    }

    rpc GetConfigTabTitle(ga.game_ugc.GetConfigTabTitleReq) returns (ga.game_ugc.GetConfigTabTitleResp) {
        option (logic.gateway.command) = {
            id: 5404
        };
    }

    rpc CheckUserIsBannedPost(ga.game_ugc.CheckUserIsBannedPostReq) returns (ga.game_ugc.CheckUserIsBannedPostResp) {
        option (logic.gateway.command) = {
            id: 5405
        };
    }

    rpc GetComprehensiveChannelInfo(ga.game_ugc.GetComprehensiveChannelInfoReq) returns (ga.game_ugc.GetComprehensiveChannelInfoResp) {
        option (logic.gateway.command) = {
            id: 5406
        };
    }

    rpc GetGameFeedByIds(ga.game_ugc.GetGameFeedByIdsReq) returns (ga.game_ugc.GetGameFeedByIdsResp) {
        option (logic.gateway.command) = {
            id: 5407
        };
    }

    rpc NeedShowPersonalSourceFilter(ga.game_ugc.NeedShowPersonalSourceFilterReq) returns (ga.game_ugc.NeedShowPersonalSourceFilterResp) {
        option (logic.gateway.command) = {
            id: 5408
        };
    }

    rpc IsConfigTabVisible(ga.game_ugc.IsConfigTabVisibleReq) returns (ga.game_ugc.IsConfigTabVisibleResp) {
        option (logic.gateway.command) = {
            id: 5409;
        };
    }
}