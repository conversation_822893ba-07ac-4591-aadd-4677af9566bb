syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "gameradarlogic/gameradar-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/gameradarlogic";

service GameradarLogic {
    rpc InvitePlay (ga.gameradarlogic.InvitePlayReq) returns (ga.gameradarlogic.InvitePlayRsp) {
        option (logic.gateway.command) = {
            id: 30306
        };
    }

    rpc InviteSuc (ga.gameradarlogic.InviteSucReq) returns (ga.gameradarlogic.InviteSucRsp) {
        option (logic.gateway.command) = {
            id: 30307
        };
    }

    rpc RadarDisplay (ga.gameradarlogic.RadarDisplayReq) returns (ga.gameradarlogic.RadarDisplayRsp) {
        option (logic.gateway.command) = {
            id: 30308
        };
    }

    rpc GetPlaymates(ga.gameradarlogic.GetPlaymatesReq) returns (ga.gameradarlogic.GetPlaymatesResp) {
        option (logic.gateway.command) = {
            id: 30309
        };
    }

    rpc GetPlaymateTabList(ga.gameradarlogic.GetPlaymateTabListReq) returns (ga.gameradarlogic.GetPlaymateTabListResp) {
        option (logic.gateway.command) = {
            id: 30310
        };
    }

    rpc ListPlaymateTabBlocks(ga.gameradarlogic.ListPlaymateTabBlocksReq) returns (ga.gameradarlogic.ListPlaymateTabBlocksResp) {
        option (logic.gateway.command) = {
            id: 30311
        };
    }

    rpc GetPlaymateNum(ga.gameradarlogic.GetPlaymateNumReq) returns (ga.gameradarlogic.GetPlaymateNumResp) {
        option (logic.gateway.command) = {
            id: 30319
        };
    }

    /*雷达状态 begin*/
    /*-------获取用户雷达状态-------*/
    rpc GetUserRadarStatus(ga.gameradarlogic.GetUserRadarStatusReq) returns (ga.gameradarlogic.GetUserRadarStatusResp) {
        option (logic.gateway.command) = {
            id: 30312
        };
    }
    /*-------获取雷达配置-------*/
    rpc GetRadarConfig(ga.gameradarlogic.GetRadarConfigReq) returns (ga.gameradarlogic.GetRadarConfigResp) {
        option (logic.gateway.command) = {
            id: 30313
        };
    }
    /*-------开启雷达-------*/
    rpc StartRadar(ga.gameradarlogic.StartRadarReq) returns (ga.gameradarlogic.StartRadarResp) {
        option (logic.gateway.command) = {
            id: 30314
        };
    }
    /*-------关闭雷达-------*/
    rpc StopRadar(ga.gameradarlogic.StopRadarReq) returns (ga.gameradarlogic.StopRadarResp) {
        option (logic.gateway.command) = {
            id: 30315
        };
    }
    /*-------拿推荐数-------*/
    rpc GetRecommendNum(ga.gameradarlogic.GetRecommendNumReq) returns (ga.gameradarlogic.GetRecommendNumResp) {
        option (logic.gateway.command) = {
            id: 30316
        };
    }

    /*雷达状态 end*/
}