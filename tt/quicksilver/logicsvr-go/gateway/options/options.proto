syntax="proto3";

package logic.gateway;



import "google/protobuf/descriptor.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/gateway/options";

extend google.protobuf.ServiceOptions {
    ServiceExt service_ext = 12176639;
}

message ServiceExt {
    string service_name = 1;
}

extend google.protobuf.MethodOptions {
    Command command = 12176638;
}

message Command {
    uint32 id = 1;
    uint32 api_level = 2;
    bool deprecated = 3;
    string rewrite_full_path = 4;
}

