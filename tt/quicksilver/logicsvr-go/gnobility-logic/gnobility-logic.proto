syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "gnobilitylogic/gnobility-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/gnobilitylogic";

service GnobilityLogic {
    rpc SetNobilitySwitch (ga.gnobilitylogic.SetNobilitySwitchReq) returns (ga.gnobilitylogic.SetNobilitySwitchResp) {
            option (logic.gateway.command) = {
                id: 5070
            };
    }

    rpc GetNobilitySwitchFlag (ga.gnobilitylogic.GetNobilitySwitchFlagReq) returns (ga.gnobilitylogic.GetNobilitySwitchFlagResp) {
            option (logic.gateway.command) = {
                id: 5071
            };
    }
}