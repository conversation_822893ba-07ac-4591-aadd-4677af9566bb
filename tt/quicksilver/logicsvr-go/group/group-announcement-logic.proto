syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;


import "group/group_.proto";
import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/group/groupannouncementlogic";


service GroupAnnouncementLogic
{
  option (logic.gateway.service_ext) = {
    service_name: "group-announcement-logic"
  };
  rpc GetGroupAnnouncementList(ga.group.GetGroupAnnouncementListReq) returns (ga.group.GetGroupAnnouncementListResp){
      option (logic.gateway.command) = {
        id: 103001
    };
  }


  rpc PrepareApplyGroupAnnouncement(ga.group.PrepareApplyGroupAnnouncementReq) returns (ga.group.PrepareApplyGroupAnnouncementResp){
      option (logic.gateway.command) = {
        id: 103002
    };
  }

  rpc DelGroupAnnouncement(ga.group.DelGroupAnnouncementReq) returns (ga.group.DelGroupAnnouncementResp){
    option (logic.gateway.command) = {
      id: 103003
    };
  }

  rpc GetGroupAnnouncementCheck(ga.group.GetGroupAnnouncementCheckReq) returns (ga.group.GetGroupAnnouncementCheckResp){
    option (logic.gateway.command) = {
      id: 103004
    };
  }

  rpc GetGroupAnnouncementByID(ga.group.GetGroupAnnouncementByIDReq) returns (ga.group.GetGroupAnnouncementByIDResp){
    option (logic.gateway.command) = {
      id: 103005
    };
  }

  rpc GetGroupAnnouncementEditAuth(ga.group.GetGroupAnnouncementEditAuthReq) returns (ga.group.GetGroupAnnouncementEditAuthResp){
    option (logic.gateway.command) = {
      id: 103006
    };
  }
}
