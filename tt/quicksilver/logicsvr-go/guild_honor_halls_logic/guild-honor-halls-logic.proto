syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "guildhonorhallslogic/guild-honor-halls-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/guildhonorhallslogic";

service GuildHonorHallsLogic {
    option (logic.gateway.service_ext) = {
        service_name: "guild-honor-halls-logic"
    };
    
    rpc GetHonorGuildList (ga.guildhonorhallslogic.GetHonorGuildListReq) returns (ga.guildhonorhallslogic.GetHonorGuildListResp) {
        option (logic.gateway.command) = {
            id: 29000   //改成正确的id
        };
    }

    rpc SetGuildHotChannelList (ga.guildhonorhallslogic.SetGuildHotChannelListReq) returns (ga.guildhonorhallslogic.SetGuildHotChannelListResp) {
        option (logic.gateway.command) = {
            id: 29001   //改成正确的id
        };
    }

    rpc SetGuildCharmMemberList (ga.guildhonorhallslogic.SetGuildCharmMemberListReq) returns (ga.guildhonorhallslogic.SetGuildCharmMemberListResp) {
        option (logic.gateway.command) = {
            id: 29002   //改成正确的id
        };
    }

    rpc SearchGuildMember (ga.guildhonorhallslogic.SearchGuildMemberReq) returns (ga.guildhonorhallslogic.SearchGuildMemberResp) {
        option (logic.gateway.command) = {
            id: 29003   //改成正确的id
        };
    }

    rpc GetHonorGuild (ga.guildhonorhallslogic.GetHonorGuildReq) returns (ga.guildhonorhallslogic.GetHonorGuildResp) {
        option (logic.gateway.command) = {
            id: 29004   //改成正确的id
        };
    }
}