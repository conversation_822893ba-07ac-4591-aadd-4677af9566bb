syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/helloworld_logic";


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "helloworld_logic/helloworldlogic_.proto";

service HelloWorldLogic {
    rpc GetHello (ga.helloworld_logic.HelloWorldLogicReq) returns (ga.helloworld_logic.HelloWorldLogicResp) {
            option (logic.gateway.command) = {
                id: 2800
            };
    }

    rpc GetHelloV2 (ga.helloworld_logic.HelloWorldLogicReq) returns (ga.helloworld_logic.HelloWorldLogicResp) {
        option (logic.gateway.command) = {
            id: 2800
        };
    }
}