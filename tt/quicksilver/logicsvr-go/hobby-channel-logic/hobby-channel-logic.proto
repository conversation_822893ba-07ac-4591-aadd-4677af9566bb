syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/hobby-channel-logic";


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "hobby_channel/hobby-channel_.proto";

service HobbyChannelLogic {
    option (logic.gateway.service_ext) = {
        service_name: "hobby-channel-logic"
    };

//    //迁移至channel-play-logic
//    rpc CreateHobbyChannel (ga.hobby_channel.CreateHobbyChannelReq) returns (ga.hobby_channel.CreateHobbyChannelResp) {
//        option (logic.gateway.command) = {
//          id: 31500
//        };
//    }

    rpc SwitchHobbyChannelSubject (ga.hobby_channel.SwitchHobbyChannelSubjectReq) returns (ga.hobby_channel.SwitchHobbyChannelSubjectResp) {
        option (logic.gateway.command) = {
            id: 31501
        };
    }

    rpc PublishHobbyChannel (ga.hobby_channel.PublishHobbyChannelReq) returns (ga.hobby_channel.PublishHobbyChannelResp) {
        option (logic.gateway.command) = {
          id: 31502
        };
    }

    rpc ListHobbyChannel (ga.hobby_channel.ListHobbyChannelReq) returns (ga.hobby_channel.ListHobbyChannelResp) {
        option (logic.gateway.command) = {
          id: 31503
        };
    }

    rpc QuickMatchHobbyChannel (ga.hobby_channel.QuickMatchHobbyChannelReq) returns (ga.hobby_channel.QuickMatchHobbyChannelResp) {
        option (logic.gateway.command) = {
          id: 31504
        };
    }

    rpc KeepHobbyChannelPublish (ga.hobby_channel.KeepHobbyChannelPublishReq) returns (ga.hobby_channel.KeepHobbyChannelPublishResp) {
        option (logic.gateway.command) = {
          id: 31505
        };
    }

    rpc CancelHobbyChannelPublish (ga.hobby_channel.CancelHobbyChannelPublishReq) returns (ga.hobby_channel.CancelHobbyChannelPublishResp) {
        option (logic.gateway.command) = {
          id: 31506
        };
    }

    rpc ListMusicHomePageCategory(ga.hobby_channel.ListMusicHomePageCategoryReq) returns (ga.hobby_channel.ListMusicHomePageCategoryResp) {
        option (logic.gateway.command) = {
            id: 31508
        };
    }

    rpc ListMusicHomePageBlock(ga.hobby_channel.ListMusicHomePageBlockReq) returns (ga.hobby_channel.ListMusicHomePageBlockResp) {
        option (logic.gateway.command) = {
            id: 31509
        };
    }

    rpc GetGameHomePageDIYFilter(ga.hobby_channel.GetGameHomePageDIYFilterReq) returns (ga.hobby_channel.GetGameHomePageDIYFilterResp) {
        option (logic.gateway.command) = {
            id: 31510
            deprecated: true
        };
    }

    rpc SetGameHomePageDIYFilter(ga.hobby_channel.SetGameHomePageDIYFilterReq) returns (ga.hobby_channel.SetGameHomePageDIYFilterResp) {
        option (logic.gateway.command) = {
            id: 31511
            deprecated: true
        };
    }

    rpc GetGameHomePageFilter(ga.hobby_channel.GetGameHomePageFilterReq) returns (ga.hobby_channel.GetGameHomePageFilterResp) {
        option (logic.gateway.command) = {
            id: 31512
            deprecated: true
        };
    }

    rpc GetGameTabByCategory(ga.hobby_channel.GetGameTabByCategoryReq) returns (ga.hobby_channel.GetGameTabByCategoryResp) {
        option (logic.gateway.command) = {
            id: 31513
        };
    }

    rpc GetMusicHomePageView(ga.hobby_channel.GetMusicHomePageViewReq) returns (ga.hobby_channel.GetMusicHomePageViewResp) {
        option (logic.gateway.command) = {
            id: 31514
        };
    }

    rpc GetMusicHomePageDialog(ga.hobby_channel.GetMusicHomePageDialogReq) returns (ga.hobby_channel.GetMusicHomePageDialogResp) {
        option (logic.gateway.command) = {
            id: 31515
        };
    }

    rpc GetMusicChannelFilter(ga.hobby_channel.GetMusicChannelFilterReq) returns (ga.hobby_channel.GetMusicChannelFilterResp) {
        option (logic.gateway.command) = {
            id: 31516
        };
    }

    rpc GetChannelByNewUserTag(ga.hobby_channel.GetChannelByNewUserTagReq) returns (ga.hobby_channel.GetChannelByNewUserTagResp) {
        option (logic.gateway.command) = {
            id: 31517
        };
    }

    rpc ListHobbyElementConf(ga.hobby_channel.ListHobbyElementConfReq) returns (ga.hobby_channel.ListHobbyElementConfResp) {
        option (logic.gateway.command) = {
            id: 31518
        };
    }

    rpc GetAllQuickPlayConf(ga.hobby_channel.GetAllQuickPlayConfReq) returns (ga.hobby_channel.GetAllQuickPlayConfResp) {
      option (logic.gateway.command) = {
        id: 31519
      };
    }
}
