syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "hotwordsearch/hotwordsearch_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/hotwordsearchlogic-go";

service HotwordsearchlogicGo {
    option (logic.gateway.service_ext) = {
        service_name: "hotwordsearchlogic-go"
    };

    rpc HotWordSearchFetch (ga.hotwordsearch.HotWordSearchFetchReq) returns (ga.hotwordsearch.HotWordSearchFetchResp) {
            option (logic.gateway.command) = {
                id: 5067
                deprecated: true
            };
    }
}
