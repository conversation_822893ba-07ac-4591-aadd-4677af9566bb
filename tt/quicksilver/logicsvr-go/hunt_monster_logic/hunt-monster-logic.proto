syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "huntmonsterlogic/hunt-monster-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/huntmonsterlogic";

service HuntMonsterLogic {
    option (logic.gateway.service_ext) = {
        service_name: "hunt-monster-logic"
    };

    // rpc Get<PERSON>ello (ga.huntmonsterlogic.HuntMonsterLogicReq) returns (ga.huntmonsterlogic.HuntMonsterLogicResp) {
    //         option (logic.gateway.command) = {
    //             id: 0   //改成正确的id
    //         };
    // }

    //rpc GetMonsterActivityConf (ga.huntmonsterlogic.GetMonsterActivityConfReq) returns (ga.huntmonsterlogic.GetMonsterActivityConfResp) {
    //        option (logic.gateway.command) = {
    //            id: 5600
    //        };
    //}

    rpc GetMonsterList (ga.huntmonsterlogic.GetMonsterListReq) returns (ga.huntmonsterlogic.GetMonsterListResp) {
            option (logic.gateway.command) = {
                id: 5601
            };
    }

    rpc AttackMonster (ga.huntmonsterlogic.AttackMonsterReq) returns (ga.huntmonsterlogic.AttackMonsterResp) {
            option (logic.gateway.command) = {
                id: 5602
            };
    }

    rpc GetOneMonsterChannel ( ga.huntmonsterlogic.GetOneMonsterChannelReq ) returns (ga.huntmonsterlogic.GetOneMonsterChannelResp ) {
            option (logic.gateway.command) = {
                id: 5603
            };
    }

    rpc GetHuntMonsterItem ( ga.huntmonsterlogic.GetHuntMonsterItemReq ) returns ( ga.huntmonsterlogic.GetHuntMonsterItemResp ) {
       option (logic.gateway.command) = {
           id: 5604
       };
    }

    rpc GetUserHuntMissionInfo ( ga.huntmonsterlogic.GetUserHuntMissionInfoReq ) returns ( ga.huntmonsterlogic.GetUserHuntMissionInfoResp ) {
       option (logic.gateway.command) = {
           id: 5605
       };
    }
}