syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "im_activity_center_logic/im-activity-center-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/im_activity_center_logic";

service ImActivityCenterLogic {
  // IM活动中心入口
  rpc ImActivityCenterEntrance (ga.im_activity_center_logic.ImActivityCenterEntranceReq) returns (ga.im_activity_center_logic.ImActivityCenterEntranceResp){
    option (logic.gateway.command) = {
      id: 30380
      deprecated: true
    };
  }
  
}
