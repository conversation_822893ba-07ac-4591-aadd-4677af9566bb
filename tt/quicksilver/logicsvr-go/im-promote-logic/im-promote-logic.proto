syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "impromotelogic/im-promote-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/impromotelogic";

service ImPromoteLogic {

    option (logic.gateway.service_ext) = {
        service_name: "im-promote-logic"
    };

    //获取免密进房的token
    rpc GetImPromoteLogicToken (ga.impromotelogic.GetImPromoteLogicTokenReq) returns (ga.impromotelogic.GetImPromoteLogicTokenResp) {
        option (logic.gateway.command) = {
            id: 30300
        };
    }
    //获取一起玩列表
    rpc GetPlayTogetherTabList (ga.impromotelogic.GetPlayTogetherTabListReq) returns (ga.impromotelogic.GetPlayTogetherTabListResp) {
        option (logic.gateway.command) = {
            id: 30301
        };
    }
    /*获取一起玩匹配配置*/
    rpc GetPlayTogetherMatchConfig(ga.impromotelogic.GetPlayTogetherMatchConfigReq) returns (ga.impromotelogic.GetPlayTogetherMatchConfigResp) {
        option (logic.gateway.command) = {
            id: 30302
        };
    }
    /*获取cue 配置*/
    rpc GetCueConfig (ga.impromotelogic.GetCueConfigReq) returns (ga.impromotelogic.GetCueConfigResp) {
        option (logic.gateway.command) = {
            id: 30330
        };
    }
    rpc PushButtonsSave (ga.impromotelogic.PushButtonsReq) returns (ga.impromotelogic.PushButtonsRsp) {
        option (logic.gateway.command) = {
            id: 30336
        };
    }
    rpc GetPushButtons (ga.impromotelogic.GetPushButtonsReq) returns (ga.impromotelogic.GetPushButtonsRsp) {
        option (logic.gateway.command) = {
            id: 30337
        };
    }
}