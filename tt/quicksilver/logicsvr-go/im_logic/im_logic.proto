syntax="proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/im_logic";


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "im/im.proto";

// Im Logic Service
service ImLogic {
    option (logic.gateway.service_ext) = {
        service_name: "im-logic"
    };

    // 标记消息已读状态
    rpc MarkMessageRead (ga.im.MarkMsgReadReq) returns (ga.im.MarkMsgReadResp) {
        option (logic.gateway.command) = {
            id: 26,
            api_level: 1
        };
    };

    rpc GetMessagePeerReadStatus (ga.im.GetMessagePeerReadStatusReq) returns (ga.im.GetMessagePeerReadStatusResp) {
        option (logic.gateway.command) = {
            id: 192,
            api_level: 1
        };
    };

    rpc BatchDelMsg (ga.im.BatchDelMsgReq) returns (ga.im.BatchDelMsgResp) {
        option (logic.gateway.command) = {
            id: 196,
            api_level: 1
        };
    };


    /*来自C++的imlogic*/
    rpc CancelMsgGo (ga.im.CancelMsgReq) returns (ga.im.CancelMsgResp) {
        option (logic.gateway.command) = {
            id: 230
        };
    }

    rpc CheckAtAllMsgGo (ga.im.CheckSendAtEveryoneGroupMsgReq) returns (ga.im.CheckSendAtEveryoneGroupMsgResp) {
        option (logic.gateway.command) = {
            id: 860
        };
    }

    rpc GetImGuideTriggerInfo(ga.im.GetImGuideTriggerInfoReq) returns (ga.im.GetImGuideTriggerInfoResp) {
        option (logic.gateway.command) = {
            id: 861
        };
    }

    rpc UploadAttachment(ga.im.UploadAttachmentReq) returns (ga.im.UploadAttachmentResp) {
        option (logic.gateway.command) = {
            id: 23;
            deprecated: true;
        };
    }

    //go 重构
    rpc SendMsgGo (ga.im.SendMsgReq) returns (ga.im.SendMsgResp) {
        option (logic.gateway.command) = {
            id: 11,
            /*
            api_level: 1
             */
            api_level: 1;
            deprecated: true;
        };
    };

    /*来自C++的imlogic ==end==*/

}

