syntax = "proto3";

package logic.installed_game_logic;



option go_package = "golang.52tt.com/protocol/services/logicsvr-go/installed-game-logic";

import "game/game_.proto";
import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";

service InstalledGameLogic {
  option (logic.gateway.service_ext) = {
    service_name: "installed-game-logic"
  };

  rpc GetUserGames (ga.game.GetUserGamesReq) returns (ga.game.GetUserGamesResp) {
    option (logic.gateway.command) = {
      id: 29
    };
  }
}