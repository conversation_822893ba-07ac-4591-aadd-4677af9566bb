syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "interact_proxy_logic/interact-proxy-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/interact-proxy-logic";

service InteractProxyLogic {
	option (logic.gateway.service_ext) = {
		service_name: "interact-proxy-logic"
	};

	rpc ReportCommonInvitationResult (ga.interact_proxy_logic.ReportCommonInvitationResultReq) returns (ga.interact_proxy_logic.ReportCommonInvitationResultResp) {
		option (logic.gateway.command) = {
			id: 36050
		};
	}
}
