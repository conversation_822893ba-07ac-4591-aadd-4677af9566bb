syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/interaction-intimacy-logic";
import "interaction_intimacy/interaction-intimacy-logic_.proto";
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";

service InteractionIntimacyLogic {
    // 批量获取亲密度信息
    rpc BatchGetIntimacyInfo (ga.interaction_intimacy.BatchGetIntimacyInfoReq) returns (ga.interaction_intimacy.BatchGetIntimacyInfoResp) {
        option (logic.gateway.command) = {
            id: 30200
        };
    }
}
