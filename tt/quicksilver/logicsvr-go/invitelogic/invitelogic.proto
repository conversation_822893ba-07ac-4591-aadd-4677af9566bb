syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "invitelogic/invitelogic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/invitelogic";

//
service InviteLogic {
    option (logic.gateway.service_ext) = {
        service_name: "invitelogic"
    };

    rpc SendInviteFromChannelPush (ga.invitelogic.SendInviteFromChannelPushReq) returns (ga.invitelogic.SendInviteFromChannelPushResp) {
        option (logic.gateway.command) = {
            id: 30480
        };
    }
    //获取邀请列表
    rpc GetInviteFromChannelList(ga.invitelogic.GetInviteFromChannelListReq) returns (ga.invitelogic.GetInviteFromChannelListResp){
        option (logic.gateway.command) = {
            id: 30481
        };
    }

    //获取邀请列表
    rpc ReplyInviteFromChannel(ga.invitelogic.ReplyInviteFromChannelReq) returns (ga.invitelogic.ReplyInviteFromChannelResp){
        option (logic.gateway.command) = {
            id: 30482
        };
    }
}