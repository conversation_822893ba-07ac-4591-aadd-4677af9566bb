syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "knightgrouplogic/knight-group-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/knightgrouplogic";

service KnightGroupLogic {
    rpc JoinKnightGroup (ga.knightgrouplogic.JoinKnightGroupReq) returns (ga.knightgrouplogic.JoinKnightGroupResp) {
            option (logic.gateway.command) = {
                id: 3771
            };
    }

    rpc GetKnightLoveRank (ga.knightgrouplogic.GetKnightLoveRankReq) returns (ga.knightgrouplogic.GetKnightLoveRankResp) {
            option (logic.gateway.command) = {
                id: 3772
            };
    }

    rpc GetKnightWeekRank (ga.knightgrouplogic.GetKnightWeekRankReq) returns (ga.knightgrouplogic.GetKnightWeekRankResp) {
            option (logic.gateway.command) = {
                id: 3773
            };
    }

    rpc GetKnightGroupCampInfo (ga.knightgrouplogic.GetKnightGroupCampInfoReq) returns (ga.knightgrouplogic.GetKnightGroupCampInfoResp) {
            option (logic.gateway.command) = {
                id: 3779
            };
    }

    rpc GetKnightMission (ga.knightgrouplogic.GetKnightMissionReq) returns (ga.knightgrouplogic.GetKnightMissionResp) {
      option (logic.gateway.command) = {
        id: 3778
      };
    }
}