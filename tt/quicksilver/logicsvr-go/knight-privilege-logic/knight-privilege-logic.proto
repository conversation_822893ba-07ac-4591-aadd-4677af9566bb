syntax = "proto3";

package logic.knightprivilege;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "knightprivilegelogic/knight-privilege-logic_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/knight-privilege-logic";


service KnightPrivilegeLogic {
    option (logic.gateway.service_ext) = {
        service_name: "knight-privilege-logic"
    };

    rpc GetKnightGroupEntry ( ga.knightprivilegelogic.GetKnightGroupEntryReq ) returns ( ga.knightprivilegelogic.GetKnightGroupEntryResp ) {
        option (logic.gateway.command) = {
            id: 3774
        };
    }
    rpc GetKnightGroupDetialInfo ( ga.knightprivilegelogic.GetKnightGroupDetialInfoReq ) returns ( ga.knightprivilegelogic.GetKnightGroupDetialInfoResp ) {
        option (logic.gateway.command) = {
            id: 3775
        };
    }
    rpc GetKnightGroupOpenStatus ( ga.knightprivilegelogic.GetKnightGroupOpenStatusReq ) returns ( ga.knightprivilegelogic.GetKnightGroupOpenStatusResp ) {
        option (logic.gateway.command) = {
            id: 3776
        };
    }
    rpc GetKnightCardInfo ( ga.knightprivilegelogic.GetKnightCardInfoReq ) returns ( ga.knightprivilegelogic.GetKnightCardInfoResp ) {
        option (logic.gateway.command) = {
            id: 3777
        };
    }

}