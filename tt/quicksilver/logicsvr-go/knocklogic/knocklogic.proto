syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "knocklogic/knocklogic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/knocklogic";

service KnockLogic {
    option (logic.gateway.service_ext) = {
        service_name: "knocklogic"
    };

    rpc <PERSON> (ga.knocklogic.KnockLogicReq) returns (ga.knocklogic.KnockLogicResp) {
        option (logic.gateway.command) = {
            id: 2800
        };
    }

    rpc <PERSON>le<PERSON>nock (ga.knocklogic.HandleKnockLogicReq) returns (ga.knocklogic.HandleKnockLogicRsp) {
        option (logic.gateway.command) = {
            id: 2801
        };
    }
}