syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto"; //-I=golang.52tt.com/third-party/tt-protocol/service/quicksilver
import "levelup_present_logic/levelup-present-logic_.proto";  //-I=golang.52tt.com/third-party/tt-protocol

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/levelup-present-logic";

service LevelUpPresentLogic {
  option (logic.gateway.service_ext) = {
    service_name: "levelup-present-logic"
  };

  //获取全部的升级礼物配置（基础+批量）
  rpc GetAllLevelUpPresentList(ga.levelup_present_logic.GetAllLevelUpPresentListReq) returns (ga.levelup_present_logic.GetAllLevelUpPresentListResp) {
    option (logic.gateway.command) = {
      id: 31200
    };
  }

  //获取用户全部升级礼物当前版本的状态
  rpc GetUserLevelUpPresentStatus(ga.levelup_present_logic.GetUserLevelUpPresentStatusReq) returns (ga.levelup_present_logic.GetUserLevelUpPresentStatusResp) {
    option (logic.gateway.command) = {
      id: 31201
    };
  }

  //赠送升级礼物，背包或T豆
  rpc SendLevelUpPresent(ga.levelup_present_logic.SendLevelUpPresentReq) returns (ga.levelup_present_logic.SendLevelUpPresentResp) {
    option (logic.gateway.command) = {
      id: 31202
    };
  }
}