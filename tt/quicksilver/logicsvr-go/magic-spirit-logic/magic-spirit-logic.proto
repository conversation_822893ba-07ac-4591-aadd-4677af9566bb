syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "magic_spirit_logic/magic-spirit-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/magic-spirit-logic";


service MagicSpiritLogic {
  option (logic.gateway.service_ext) = {
    service_name: "magic-spirit-logic"
  };

  // 获取魔发精灵配置信息
  rpc GetMagicSpiritConf (ga.magic_spirit_logic.GetMagicSpiritConfReq) returns (ga.magic_spirit_logic.GetMagicSpiritConfResp) {
    option (logic.gateway.command) = {
      id: 31150    
    };
  }


  // 获取魔法精灵可用条件&是否可用
  rpc GetMagicSpiritUsable (ga.magic_spirit_logic.GetMagicSpiritUsableReq) returns (ga.magic_spirit_logic.GetMagicSpiritUsableResp) {
    option (logic.gateway.command) = {
      id: 31151    
    };
  }

  // 发送魔发精灵
  rpc SendMagicSpirit (ga.magic_spirit_logic.SendMagicSpiritReq) returns (ga.magic_spirit_logic.SendMagicSpiritResp) {
    option (logic.gateway.command) = {
      id: 31152    
    };
  }

  // 开箱礼物
  rpc SendUnpackGift (ga.magic_spirit_logic.SendUnpackGiftReq) returns (ga.magic_spirit_logic.SendUnpackGiftResp) {
    option (logic.gateway.command) = {
      id: 31153
    };
  }

  // 获取房间带开箱礼物信息列表
  rpc GetChannelAllUnpackGift (ga.magic_spirit_logic.GetChannelAllUnpackGiftReq) returns (ga.magic_spirit_logic.GetChannelAllUnpackGiftResp) {
    option (logic.gateway.command) = {
      id: 31154
    };
  }

}