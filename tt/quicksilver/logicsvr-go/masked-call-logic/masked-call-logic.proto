syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "masked_call/masked-call_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/masked-call-logic";

service MaskedCall {
    option (logic.gateway.service_ext) = {
        service_name: "masked-call-logic"
    };

    // --------- 玩家匹配 ----------

    //查询匹配信息
    rpc QueryMatchInfo (ga.masked_call.QueryMatchInfoReq) returns (ga.masked_call.QueryMatchInfoResp) {
        option (logic.gateway.command) = {
			id: 30180
		};
    }

    //开始匹配
    rpc StartMatch (ga.masked_call.StartMatchReq) returns (ga.masked_call.StartMatchResp) {
        option (logic.gateway.command) = {
			id: 30181
		};
    }

    //取消匹配
    rpc CancelMatch (ga.masked_call.CancelMatchReq) returns (ga.masked_call.CancelMatchResp) {
        option (logic.gateway.command) = {
			id: 30182
		};
    }

    // --------- 通话控制 ----------
    //获取通话信息
    rpc QueryCallInfo (ga.masked_call.QueryCallInfoReq) returns (ga.masked_call.QueryCallInfoResp) {
        option (logic.gateway.command) = {
			id: 30183
		};
    }

    //设置连接状态
    rpc SetConnectStatus (ga.masked_call.SetConnectStatusReq) returns (ga.masked_call.SetConnectStatusResp) {
        option (logic.gateway.command) = {
			id: 30184
		};
    }

	//公开身份
    rpc Unmask (ga.masked_call.UnmaskReq) returns (ga.masked_call.UnmaskResp) {
        option (logic.gateway.command) = {
			id: 30185
		};
    }

    //邀请公开身份
    rpc InviteUnmask (ga.masked_call.InviteUnmaskReq) returns (ga.masked_call.InviteUnmaskResp) {
        option (logic.gateway.command) = {
			id: 30186
		};
    }

	//评价
    rpc Comment (ga.masked_call.CommentReq) returns (ga.masked_call.CommentResp) {
         option (logic.gateway.command) = {
			id: 30187
		};
    }

	//roll点
    rpc Roll (ga.masked_call.RollReq) returns (ga.masked_call.RollResp) {
        option (logic.gateway.command) = {
			id: 30188
		};
    }

    rpc ReportAudio (ga.masked_call.ReportAudioReq) returns (ga.masked_call.ReportAudioResp) {
        option (logic.gateway.command) = {
			id: 30189
		};
    }

    rpc GetAudioUploadToken (ga.masked_call.GetAudioUploadTokenReq) returns (ga.masked_call.GetAudioUploadTokenResp) {
        option (logic.gateway.command) = {
			id: 30190
		};
    }
}
