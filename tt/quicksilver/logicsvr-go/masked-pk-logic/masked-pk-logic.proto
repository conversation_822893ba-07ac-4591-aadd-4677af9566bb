syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "masked_pk_logic/masked-pk-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/masked-pk-logic";

service MaskedPKLogic {
  option (logic.gateway.service_ext) = {
    service_name: "masked-pk-logic"
  };

  // 娱乐厅开始pk流程（包括领取筹码）
  rpc StartChannelMaskedPK (ga.masked_pk_logic.StartChannelMaskedPKReq) returns (ga.masked_pk_logic.StartChannelMaskedPKResp) {
    option (logic.gateway.command) = {
      id: 30550   //改成正确的id
    };
  }

  // 娱乐厅取消pk匹配
  rpc CancelChannelMaskedPK (ga.masked_pk_logic.CancelChannelMaskedPKReq) returns (ga.masked_pk_logic.CancelChannelMaskedPKResp) {
    option (logic.gateway.command) = {
      id: 30551   //改成正确的id
    };
  }

  // 娱乐厅获取pk信息
  rpc GetChannelMaskedPKInfo (ga.masked_pk_logic.GetChannelMaskedPKInfoReq) returns (ga.masked_pk_logic.GetChannelMaskedPKInfoResp) {
    option (logic.gateway.command) = {
      id: 30552   //改成正确的id
    };
  }

  // 娱乐厅获取当前pk活动配置
  rpc GetChannelMaskedPKConf (ga.masked_pk_logic.GetChannelMaskedPKConfReq) returns (ga.masked_pk_logic.GetChannelMaskedPKConfResp) {
    option (logic.gateway.command) = {
      id: 30553   //改成正确的id
    };
  }

  // 娱乐厅获取主播本轮比赛战力榜
  rpc GetChannelMaskedPKAnchorRank (ga.masked_pk_logic.GetChannelMaskedPKAnchorRankReq) returns (ga.masked_pk_logic.GetChannelMaskedPKAnchorRankResp) {
    option (logic.gateway.command) = {
      id: 30554   //改成正确的id
    };
  }

  // 娱乐厅放弃参与本次比赛
  rpc GiveUpChannelMaskedPK (ga.masked_pk_logic.GiveUpChannelMaskedPKReq) returns (ga.masked_pk_logic.GiveUpChannelMaskedPKResp) {
    option (logic.gateway.command) = {
      id: 30555   //改成正确的id
    };
  }

  // 获取房间有蒙面pk资格的主播列表
  rpc GetChannelQualifiedAnchor (ga.masked_pk_logic.GetChannelQualifiedAnchorReq) returns (ga.masked_pk_logic.GetChannelQualifiedAnchorResp) {
    option (logic.gateway.command) = {
      id: 30556   //改成正确的id
    };
  }

  // 娱乐厅获取主播本场比赛战力榜
  rpc GetChannelSinglePKAnchorRank (ga.masked_pk_logic.GetChannelSinglePKAnchorRankReq) returns (ga.masked_pk_logic.GetChannelSinglePKAnchorRankResp) {
    option (logic.gateway.command) = {
      id: 30557   //改成正确的id
    };
  }

  // 娱乐厅快捷送礼列表
  rpc GetQuickSendPresentConfig (ga.masked_pk_logic.GetQuickSendPresentConfigReq) returns (ga.masked_pk_logic.GetQuickSendPresentConfigResp) {
    option (logic.gateway.command) = {
      id: 30558   //改成正确的id
    };
  }

  // 语音直播间开始pk流程（包括领取筹码）
  rpc StartLiveChannelMaskedPK (ga.masked_pk_logic.StartLiveChannelMaskedPKReq) returns (ga.masked_pk_logic.StartLiveChannelMaskedPKResp) {
    option (logic.gateway.command) = {
      id: 30561   //改成正确的id
    };
  }

  // 语音直播间取消pk匹配
  rpc CancelLiveChannelMaskedPK (ga.masked_pk_logic.CancelLiveChannelMaskedPKReq) returns (ga.masked_pk_logic.CancelLiveChannelMaskedPKResp) {
    option (logic.gateway.command) = {
      id: 30562   //改成正确的id
    };
  }

  // 语音直播间获取pk信息
  rpc GetLiveChannelMaskedPKInfo (ga.masked_pk_logic.GetLiveChannelMaskedPKInfoReq) returns (ga.masked_pk_logic.GetLiveChannelMaskedPKInfoResp) {
    option (logic.gateway.command) = {
      id: 30563   //改成正确的id
    };
  }

  // 语音直播间获取当前pk活动配置
  rpc GetLiveChannelMaskedPKConf (ga.masked_pk_logic.GetLiveChannelMaskedPKConfReq) returns (ga.masked_pk_logic.GetLiveChannelMaskedPKConfResp) {
    option (logic.gateway.command) = {
      id: 30564   //改成正确的id
    };
  }

  // 语音直播间获取观众火力榜
  rpc GetLivePKAudienceRank (ga.masked_pk_logic.GetLivePKAudienceRankReq) returns (ga.masked_pk_logic.GetLivePKAudienceRankResp) {
    option (logic.gateway.command) = {
      id: 30565   //改成正确的id
    };
  }

  // 语音直播间放弃参与本次比赛
  rpc GiveUpLiveChannelMaskedPK (ga.masked_pk_logic.GiveUpLiveChannelMaskedPKReq) returns (ga.masked_pk_logic.GiveUpLiveChannelMaskedPKResp) {
    option (logic.gateway.command) = {
      id: 30566   //改成正确的id
    };
  }

  // 语音直播间快捷送礼列表
  rpc GetLiveQuickSendPresentConfig (ga.masked_pk_logic.GetLiveQuickSendPresentConfigReq) returns (ga.masked_pk_logic.GetLiveQuickSendPresentConfigResp) {
    option (logic.gateway.command) = {
      id: 30567   //改成正确的id
    };
  }

  // 蒙面pk榜单入口
  rpc CheckMaskedPkRankEntry (ga.masked_pk_logic.CheckMaskedPkRankEntryReq) returns (ga.masked_pk_logic.CheckMaskedPkRankEntryResp) {
    option (logic.gateway.command) = {
      id: 30570   //改成正确的id
    };
  }

    // 蒙面pk房间榜单
  rpc MaskedPkGetConsumeTopN (ga.masked_pk_logic.MaskedPkGetConsumeTopNReq) returns (ga.masked_pk_logic.MaskedPkGetConsumeTopNResp) {
    option (logic.gateway.command) = {
      id: 30571   //改成正确的id
    };
  }
}