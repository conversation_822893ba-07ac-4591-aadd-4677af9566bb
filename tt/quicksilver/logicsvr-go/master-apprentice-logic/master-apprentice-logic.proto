syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "masterapprenticelogic/master-apprentice-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/masterapprenticelogic";

service MasterApprenticeLoigc {
  // 师徒关系 发出邀请
  option (logic.gateway.service_ext) = {
    service_name: "master-apprentice-logic"
  };
  rpc MasterInviteMsg (ga.masterapprenticelogic.MasterInviteMsgReq) returns (ga.masterapprenticelogic.MasterInviteMsgResp){
    option (logic.gateway.command) = {
      id: 30211
    };
  }

  // 师徒关系 接受邀请
  rpc ApprenticeEstablishMsg (ga.masterapprenticelogic.ApprenticeEstablishMsgReq) returns (ga.masterapprenticelogic.ApprenticeEstablishMsgResp){
    option (logic.gateway.command) = {
      id: 30212
    };
  }

  // 入口信息
  rpc EntranceInfo (ga.masterapprenticelogic.EntranceInfoReq) returns (ga.masterapprenticelogic.EntranceInfoResp){
    option (logic.gateway.command) = {
      id: 30213
    };
  }
}
