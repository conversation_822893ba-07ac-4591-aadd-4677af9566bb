syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "melee_channel/melee-channel-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/melee-channel-logic";

service MeleeChannelLogic {
  // 团战房
  option (logic.gateway.service_ext) = {
    service_name: "melee-channel-logic"
  };
  rpc ApplyToMicWhiteList (ga.melee_channel.ApplyToMicWhiteListReq) returns (ga.melee_channel.ApplyToMicWhiteListResp){
    option (logic.gateway.command) = {
      id: 50010
    };
  }
  rpc HandleMicWhiteListApply (ga.melee_channel.HandleMicWhiteListApplyReq) returns (ga.melee_channel.HandleMicWhiteListApplyResp){
    option (logic.gateway.command) = {
      id: 50011
    };
  }
  rpc GetMicWhiteApplyList (ga.melee_channel.GetMicWhiteApplyListReq) returns (ga.melee_channel.GetMicWhiteApplyListResp){
    option (logic.gateway.command) = {
      id: 50012
    };
  }
  rpc GetOnMicWhiteList (ga.melee_channel.GetOnMicWhiteListReq) returns (ga.melee_channel.GetOnMicWhiteListResp){
    option (logic.gateway.command) = {
      id: 50013
    };
  }
  rpc AddToMicWhiteList (ga.melee_channel.AddToMicWhiteListReq) returns (ga.melee_channel.AddToMicWhiteListResp){
    option (logic.gateway.command) = {
      id: 50014
    };
  }
  rpc RemoveFromWhiteList (ga.melee_channel.RemoveFromWhiteListReq) returns (ga.melee_channel.RemoveFromWhiteListResp){
    option (logic.gateway.command) = {
      id: 50015
    };
  }
  rpc GetSelfOnMicQualifications (ga.melee_channel.GetSelfOnMicQualificationsReq) returns (ga.melee_channel.GetSelfOnMicQualificationsResp){
    option (logic.gateway.command) = {
      id: 50016
    };
  }
  
  rpc ApplyOnMicToken (ga.melee_channel.ApplyOnMicTokenReq) returns (ga.melee_channel.ApplyOnMicTokenResp){
    option (logic.gateway.command) = {
      id: 50017
    };
  }

  rpc GetMeleeChannelUserInfoList (ga.melee_channel.GetMeleeChannelUserInfoListReq) returns (ga.melee_channel.GetMeleeChannelUserInfoListResp) {
    option (logic.gateway.command) = {
      id: 50018
    };
  }

  rpc SetWhiteListSwitchStatus (ga.melee_channel.SetWhiteListSwitchStatusReq) returns (ga.melee_channel.SetWhiteListSwitchStatusResp) {
    option (logic.gateway.command) = {
      id: 50019
    };
  }

  rpc GetMeleeChannelMicWhiteCandidateList (ga.melee_channel.GetMeleeChannelMicWhiteCandidateListReq) returns (ga.melee_channel.GetMeleeChannelMicWhiteCandidateListResp) {
    option (logic.gateway.command) = {
      id: 50020
    };
  }

  rpc GetMicWhiteApplyCount (ga.melee_channel.GetMicWhiteApplyCountReq) returns (ga.melee_channel.GetMicWhiteApplyCountResp) {
    option (logic.gateway.command) = {
      id: 50021
    };
  }

  rpc GetChannelRoomApplyList (ga.melee_channel.GetChannelRoomApplyListReq) returns (ga.melee_channel.GetChannelRoomApplyListResp) {
    option (logic.gateway.command) = {
      id: 50022
    };
  }
  rpc AddChannelRoomApplyList (ga.melee_channel.AddChannelRoomApplyListReq) returns (ga.melee_channel.AddChannelRoomApplyListResp) {
    option (logic.gateway.command) = {
      id: 50023
    };
  }
  rpc HandleChannelRoomApply (ga.melee_channel.HandleChannelRoomApplyReq) returns (ga.melee_channel.HandleChannelRoomApplyResp) {
    option (logic.gateway.command) = {
      id: 50024
    };
  }
  rpc GetChannelRoomWhitelist (ga.melee_channel.GetChannelRoomWhitelistReq) returns (ga.melee_channel.GetChannelRoomWhitelistResp) {
    option (logic.gateway.command) = {
      id: 50025
    };
  }
  rpc AddChannelRoomWhiteList (ga.melee_channel.AddChannelRoomWhiteListReq) returns (ga.melee_channel.AddChannelRoomWhiteListResp) {
    option (logic.gateway.command) = {
      id: 50026
    };
  }
  rpc RemoveChannelRoomWhiteList (ga.melee_channel.RemoveChannelRoomWhiteListReq) returns (ga.melee_channel.RemoveChannelRoomWhiteListResp) {
    option (logic.gateway.command) = {
      id: 50027
    };
  }

  rpc EnterRoomWhitelistConfig (ga.melee_channel.EnterRoomWhitelistConfigReq) returns (ga.melee_channel.EnterRoomWhitelistConfigResp) {
    option (logic.gateway.command) = {
      id: 50028
    };
  }
  
}
