syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "missiongologic/missiongo-logic_.proto";
//import "tt/quicksilver/third-party/tt-protocol/missiongo-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/missiongologicsr";

service MissionGoLogic {
  option (logic.gateway.service_ext) = {
        service_name: "missiongo-logic"
  };
  rpc OpenNotificationPermission(ga.missiongologic.OpenNotificationPermissionReq) returns (ga.missiongologic.OpenNotificationPermissionResp) {
    option (logic.gateway.command) = {
      id: 30101
    };
  }
}

