syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "activity/activity_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/molebeatlogic";

service MoleBeatLogic {
    option (logic.gateway.service_ext) = {
        service_name: "molebeat-logic"
    };

    rpc ReportBeat(ga.activity.ReportMoleBeatResultReq) returns (ga.activity.ReportMoleBeatResultRsp) {
        option (logic.gateway.command) = {
                id: 2806   
            };
    }
}