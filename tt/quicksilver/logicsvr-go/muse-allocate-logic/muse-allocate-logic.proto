syntax = "proto3";

package muse_allocate_logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "muse_allocate_logic/muse_allocate_logic.proto";


option go_package = "golang.52tt.com/protocol/services/logicsvr-go/muse-allocate-logic";

service MuseAllocateLogic {
  option (logic.gateway.service_ext) = {
    service_name: "muse-allocate-logic"
  };

  //获取导航栏
  rpc GetMuseAllocateInfo (ga.muse_allocate_logic.GetMuseAllocateInfoReq) returns (ga.muse_allocate_logic.GetMuseAllocateInfoResp) {
    option (logic.gateway.command) = {
      id: 38001
    };
  }
}