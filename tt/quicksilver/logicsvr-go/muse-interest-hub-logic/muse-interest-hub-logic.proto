syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "muse_interest_hub_logic/muse_interest_hub_logic.proto";


// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/muse-interest-hub-logic";

service MuseInterestHubLogic {
  option (logic.gateway.service_ext) = {
    service_name: "muse-interest-hub-logic"
  };

  // Muse兴趣内容通用上报接口
  rpc MuseCommonReport (ga.muse_interest_hub_logic.MuseCommonReportRequest) returns (ga.muse_interest_hub_logic.MuseCommonReportResponse) {
    option (logic.gateway.command) = {
      id: 31560
    };
  }

// 兴趣内容相关 开关合集
  rpc GetMuseSwitchHub (ga.muse_interest_hub_logic.GetMuseSwitchHubRequest) returns (ga.muse_interest_hub_logic.GetMuseSwitchHubResponse) {
    option (logic.gateway.command) = {
      id: 31561
    };
  }


// 兴趣内容相关 开关合集
  rpc SetMuseSwitchHub (ga.muse_interest_hub_logic.SetMuseSwitchHubRequest) returns (ga.muse_interest_hub_logic.SetMuseSwitchHubResponse) {
    option (logic.gateway.command) = {
      id: 31562
    };
  }

}