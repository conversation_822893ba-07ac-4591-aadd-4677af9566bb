syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "muse_post_logic/muse-post-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/musepostlogic";

service MusePostLogic {
  option (logic.gateway.service_ext) = {
    service_name: "muse-post-logic"
  };


  // 获取留言列表
  rpc GetMusePostList (ga.muse_post_logic.GetMusePostListReq) returns (ga.muse_post_logic.GetMusePostListResp) {
    option (logic.gateway.command) = {
      id: 33320
    };
  }

  // 获取评论列表
  rpc GetMuseCommentList (ga.muse_post_logic.GetMuseParentCommentListReq) returns (ga.muse_post_logic.GetMuseParentCommentListResp) {
    option (logic.gateway.command) = {
      id: 33321
    };
  }

  // 获取点赞列表
  rpc GetMuseContentLikeList (ga.muse_post_logic.GetMuseContentLikeListReq) returns (ga.muse_post_logic.GetMuseContentLikeListResp) {
    option (logic.gateway.command) = {
      id: 33322
    };
  }

  // 获取留言列表
  rpc GetMusePostDetail (ga.muse_post_logic.GetMusePostDetailReq) returns (ga.muse_post_logic.GetMusePostDetailResp) {
    option (logic.gateway.command) = {
      id: 33323
    };
  }

  // 点赞
  rpc MuseAddLike (ga.muse_post_logic.MuseAddLikeReq) returns (ga.muse_post_logic.MuseAddLikeResp) {
    option (logic.gateway.command) = {
      id: 33324
    };
  }

  // 取消点赞
  rpc MuseResetLike (ga.muse_post_logic.MuseResetLikeReq) returns (ga.muse_post_logic.MuseResetLikeResp) {
    option (logic.gateway.command) = {
      id: 33325
    };
  }

  // 发布留言
  rpc MusePublishPost (ga.muse_post_logic.MusePublishPostReq) returns (ga.muse_post_logic.MusePublishPostResp) {
    option (logic.gateway.command) = {
      id: 33326
    };
  }

  // 发布评论
  rpc MusePublishComment (ga.muse_post_logic.MusePublishCommentReq) returns (ga.muse_post_logic.MusePublishCommentResp) {
    option (logic.gateway.command) = {
      id: 33327
    };
  }

  // 获取留言列表
  rpc MuseHaveNewPost (ga.muse_post_logic.MuseHaveNewPostReq) returns (ga.muse_post_logic.MuseHaveNewPostResp) {
    option (logic.gateway.command) = {
      id: 33328
    };
  }

  // 标记已读
  rpc MarkMuseInteractiveMsgRead (ga.muse_post_logic.MarkMuseInteractiveMsgReadReq) returns (ga.muse_post_logic.MarkMuseInteractiveMsgReadResp) {
    option (logic.gateway.command) = {
      id: 33329
    };
  }

  // 获取互动消息
  rpc GetMuseInteractiveMsg (ga.muse_post_logic.GetMuseInteractiveMsgReq) returns (ga.muse_post_logic.GetMuseInteractiveMsgResp) {
    option (logic.gateway.command) = {
      id: 33330
    };
  }

  // 删除留言或评论
  rpc DeleteMuseContent (ga.muse_post_logic.DeleteMuseContentReq) returns (ga.muse_post_logic.DeleteMuseContentResp) {
    option (logic.gateway.command) = {
      id: 33331
    };
  }

  // 检查是否可以发布
  rpc CheckMusePublishRight (ga.muse_post_logic.CheckMusePublishRightReq) returns (ga.muse_post_logic.CheckMusePublishRightResp) {
    option (logic.gateway.command) = {
      id: 33332
    };
  }

  // 获取二级评论
  rpc GetMuseSubCommentList (ga.muse_post_logic.GetMuseSubCommentListReq) returns (ga.muse_post_logic.GetMuseSubCommentListResp) {
    option (logic.gateway.command) = {
      id: 33333
    };
  }

  // 获取个人发布记录
  rpc GetMuseUserRecord (ga.muse_post_logic.GetMuseUserRecordRequest) returns (ga.muse_post_logic.GetMuseUserRecordResponse) {
    option (logic.gateway.command) = {
      id: 33334
    };
  }

  // 搜索有红花的好友
  rpc GetMusePostAtFriends (ga.muse_post_logic.GetMusePostAtFriendsRequest) returns (ga.muse_post_logic.GetMusePostAtFriendsResponse) {
    option (logic.gateway.command) = {
      id: 33335
    };
  }
}