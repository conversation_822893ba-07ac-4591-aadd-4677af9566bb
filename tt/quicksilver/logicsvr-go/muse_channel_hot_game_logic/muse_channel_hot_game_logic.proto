syntax = "proto3";

package muse_channel_hot_game_logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "muse_channel_hot_game_logic/muse_channel_hot_game_logic.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/muse-channel-hot-game-logic";

service MuseChannelHotGame {
  option (logic.gateway.service_ext) = {
    service_name: "muse-channel-hot-game-logic"
  };

  //获取玩法的热聊挑战
  rpc GetMuseChannelHotGame (ga.muse_channel_hot_game_logic.GetMuseChannelHotGameRequest) returns (ga.muse_channel_hot_game_logic.GetMuseChannelHotGameResponse) {
    option (logic.gateway.command) = {
      id: 39250
    };
  }

  //获取热聊挑战浮窗
  rpc GetMuseChannelHotGameFloat (ga.muse_channel_hot_game_logic.GetMuseChannelHotGameFloatRequest) returns (ga.muse_channel_hot_game_logic.GetMuseChannelHotGameFloatResponse) {
    option (logic.gateway.command) = {
      id: 39251
    };
  }

  /* 参加或取消参加热聊 */
  rpc SetMuseChannelHotGameJoinStatus(ga.muse_channel_hot_game_logic.SetMuseChannelHotGameJoinStatusRequest) returns (ga.muse_channel_hot_game_logic.SetMuseChannelHotGameJoinStatusResponse) {
    option (logic.gateway.command) = {
      id: 39252
    };
  }


}