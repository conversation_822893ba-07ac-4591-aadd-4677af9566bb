syntax = "proto3";

package muse_social_community_logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "muse_social_community_logic/muse_social_community_logic.proto";
import "channel/channel_.proto";


option go_package = "golang.52tt.com/protocol/services/logicsvr-go/muse-social-community-logic";

service MuseSocialCommunity {
  option (logic.gateway.service_ext) = {
    service_name: "muse-social-community-logic"
  };

  //获取导航栏
  rpc ListMuseSocialCommunityNavBars (ga.muse_social_community_logic.ListMuseSocialCommunityNavBarsRequest) returns (ga.muse_social_community_logic.ListMuseSocialCommunityNavBarsResponse) {
    option (logic.gateway.command) = {
      id: 36700
    };
  }

  //获取二级导航栏
  rpc ListMuseSocialCommunityNavSecondaryBars (ga.muse_social_community_logic.ListMuseSocialCommunityNavSecondaryBarsRequest) returns (ga.muse_social_community_logic.ListMuseSocialCommunityNavSecondaryBarsResponse) {
    option (logic.gateway.command) = {
      id: 36701
    };
  }

  /* 社群聊天室历史消息 */
  rpc GetSocialCommunityChatChannelHistoryMsg(ga.channel.GetChannelMsgReq) returns (ga.channel.GetChannelMsgResp) {
    option (logic.gateway.command) = {
      id: 36702
    };
  }

  /* 获取用户社群角色 */
  rpc BatGetMuseSocialCommunityUsersRole (ga.muse_social_community_logic.BatGetMuseSocialCommunityUsersRoleRequest) returns (ga.muse_social_community_logic.BatGetMuseSocialCommunityUsersRoleResponse) {
    option (logic.gateway.command) = {
      id: 36703
    };
  }

  rpc JoinSocialCommunityFans(ga.muse_social_community_logic.JoinSocialCommunityFansRequest) returns (ga.muse_social_community_logic.JoinSocialCommunityFansResponse) {
    option (logic.gateway.command) = {
      id: 36705
    };
  }

  rpc GetSocialCommunityDetail(ga.muse_social_community_logic.GetSocialCommunityDetailRequest) returns (ga.muse_social_community_logic.GetSocialCommunityDetailResponse) {
    option (logic.gateway.command) = {
      id: 36706
    };
  }

  rpc GetSocialCommunityProfilePages(ga.muse_social_community_logic.GetSocialCommunityProfilePagesRequest) returns (ga.muse_social_community_logic.GetSocialCommunityProfilePagesResponse) {
    option (logic.gateway.command) = {
      id: 36707
    };
  }

  rpc GetMySocialCommunity(ga.muse_social_community_logic.GetMySocialCommunityRequest) returns (ga.muse_social_community_logic.GetMySocialCommunityResponse) {
    option (logic.gateway.command) = {
      id: 36708
    };
  }

  /*点击欢迎，推送消息*/
  rpc SendWelcomePush(ga.muse_social_community_logic.SendWelcomePushRequest) returns (ga.muse_social_community_logic.SendWelcomePushResponse) {
    option (logic.gateway.command) = {
      id: 36710
    };
  }

  /* 上榜社群的公演房增加榜单入口 */
  rpc BatGetRankInChannel(ga.muse_social_community_logic.BatGetRankInChannelRequest) returns (ga.muse_social_community_logic.BatGetRankInChannelResponse) {
    option (logic.gateway.command) = {
      id: 36711
    };
  }

  rpc RemoveSocialCommunityMember(ga.muse_social_community_logic.RemoveSocialCommunityMemberRequest) returns (ga.muse_social_community_logic.RemoveSocialCommunityMemberResponse) {
    option (logic.gateway.command) = {
      id: 36712
    };
  }

  rpc ExitSocialCommunity(ga.muse_social_community_logic.ExitSocialCommunityRequest) returns (ga.muse_social_community_logic.ExitSocialCommunityResponse) {
    option (logic.gateway.command) = {
      id: 36713
    };
  }

  rpc UpdateMemberRole(ga.muse_social_community_logic.UpdateMemberRoleRequest) returns (ga.muse_social_community_logic.UpdateMemberRoleResponse) {
    option (logic.gateway.command) = {
      id: 36714
      deprecated: true
    };
  }

  rpc GetSocialCommunityRoleLeftNumbers(ga.muse_social_community_logic.GetSocialCommunityRoleLeftNumbersRequest) returns (ga.muse_social_community_logic.GetSocialCommunityRoleLeftNumbersResponse) {
    option (logic.gateway.command) = {
      id: 36715
      deprecated: true
    };
  }

  /* 获取兴趣讨论区 */
  rpc GetChannelAssociateSocialCommunity(ga.muse_social_community_logic.GetChannelAssociateSocialCommunityRequest) returns (ga.muse_social_community_logic.GetChannelAssociateSocialCommunityResponse) {
    option (logic.gateway.command) = {
      id: 36716
    };
  }

  /* 获取我的社群 */
  rpc GetMySocialCommunityPage(ga.muse_social_community_logic.GetMySocialCommunityPageRequest) returns (ga.muse_social_community_logic.GetMySocialCommunityPageResponse) {
    option (logic.gateway.command) = {
      id: 36717
    };
  }

  /* 获取品类类型 */
  rpc ListCategoryTypes(ga.muse_social_community_logic.ListCategoryTypesRequest) returns (ga.muse_social_community_logic.ListCategoryTypesResponse) {
    option (logic.gateway.command) = {
      id: 36718
    };
  }

  /* 获取品类 */
  rpc ListCategories(ga.muse_social_community_logic.ListCategoriesRequest) returns (ga.muse_social_community_logic.ListCategoriesResponse) {
    option (logic.gateway.command) = {
      id: 36719
    };
  }

  /* 申请创建社群 */
  rpc ApplyCreateSocialCommunity(ga.muse_social_community_logic.ApplyCreateSocialCommunityRequest) returns (ga.muse_social_community_logic.ApplyCreateSocialCommunityResponse) {
    option (logic.gateway.command) = {
      id: 36720
    };
  }

  /* 获取悬浮窗 */
  rpc GetSocialCommunityFloat(ga.muse_social_community_logic.GetSocialCommunityFloatRequest) returns (ga.muse_social_community_logic.GetSocialCommunityFloatResponse) {
    option (logic.gateway.command) = {
      id: 36721
    };
  }

  /* 加入社团核心 */
  rpc JoinSocialCommunity(ga.muse_social_community_logic.JoinSocialCommunityRequest) returns (ga.muse_social_community_logic.JoinSocialCommunityResponse) {
    option (logic.gateway.command) = {
      id: 36722
    };
  }

  /* 查看用户是否有资格创建社群 */
  rpc ValidateUserHasCreateQualification(ga.muse_social_community_logic.ValidateUserHasCreateQualificationRequest) returns (ga.muse_social_community_logic.ValidateUserHasCreateQualificationResponse) {
    option (logic.gateway.command) = {
      id: 36723
    };
  }


  /*获取社群基本信息*/
  rpc GetSocialCommunityBase(ga.muse_social_community_logic.GetSocialCommunityBaseRequest) returns (ga.muse_social_community_logic.GetSocialCommunityBaseResponse) {
    option (logic.gateway.command) = {
      id: 36724
    };
  }

  /*获取用户当前社群群聊Id*/
  rpc GetUserSocialGroupIds(ga.muse_social_community_logic.GetUserSocialGroupIdsRequest) returns (ga.muse_social_community_logic.GetUserSocialGroupIdsResponse) {
    option (logic.gateway.command) = {
      id: 36725
    };
  }
  /*获取群预览信息*/
  rpc MuseSocialPreviewGroupMessage(ga.muse_social_community_logic.MuseSocialPreviewGroupMessageRequest) returns (ga.muse_social_community_logic.MuseSocialPreviewGroupMessageResponse) {
    option (logic.gateway.command) = {
      id: 36726
    };
  }

  /*移除管理员*/
  rpc MuseSocialGroupRemoveAdmin(ga.muse_social_community_logic.MuseSocialGroupRemoveAdminRequest) returns (ga.muse_social_community_logic.MuseSocialGroupRemoveAdminResponse) {
    option (logic.gateway.command) = {
      id: 36727
    };
  }

  /*全部禁言*/
  rpc MuseSocialGroupSetAllMute(ga.muse_social_community_logic.MuseSocialGroupSetAllMuteRequest) returns (ga.muse_social_community_logic.MuseSocialGroupSetAllMuteResponse) {
    option (logic.gateway.command) = {
      id: 36728
    };
  }

  /*禁言*/
  rpc MuseSocialGroupMuteMember(ga.muse_social_community_logic.MuseSocialGroupMuteMemberRequest) returns (ga.muse_social_community_logic.MuseSocialGroupMuteMemberResponse) {
    option (logic.gateway.command) = {
      id: 36729
    };
  }

  /*解除禁言*/
  rpc MuseSocialGroupUnmuteMember(ga.muse_social_community_logic.MuseSocialGroupUnmuteMemberRequest) returns (ga.muse_social_community_logic.MuseSocialGroupUnmuteMemberResponse) {
    option (logic.gateway.command) = {
      id: 36730
    };
  }

  /*禁言列表*/
  rpc MuseSocialGroupGetMuteList(ga.muse_social_community_logic.MuseSocialGroupGetMuteListRequest) returns (ga.muse_social_community_logic.MuseSocialGroupGetMuteListResponse) {
    option (logic.gateway.command) = {
      id: 36731
    };
  }

  /*成员列表*/
  rpc MuseSocialGroupGetMemberList(ga.muse_social_community_logic.MuseSocialGroupGetMemberListRequest) returns (ga.muse_social_community_logic.MuseSocialGroupGetMemberListResponse) {
    option (logic.gateway.command) = {
      id: 36732
    };
  }

  /*群详情*/
  rpc MuseSocialGroupGetDetailInfo(ga.muse_social_community_logic.MuseSocialGroupGetDetailInfoRequest) returns (ga.muse_social_community_logic.MuseSocialGroupGetDetailInfoResponse) {
    option (logic.gateway.command) = {
      id: 36733
    };
  }

  /*加入管理员*/
  rpc MuseSocialGroupAddAdmin(ga.muse_social_community_logic.MuseSocialGroupAddAdminRequest) returns (ga.muse_social_community_logic.MuseSocialGroupAddAdminResponse) {
    option (logic.gateway.command) = {
      id: 36734
    };
  }

  /*获取群聊在线人数*/
  rpc GetSocialGroupOnlineMembers(ga.muse_social_community_logic.GetSocialGroupOnlineMembersRequest) returns (ga.muse_social_community_logic.GetSocialGroupOnlineMembersResponse) {
    option (logic.gateway.command) = {
      id: 36735
    };
  }

  /*获取社群核心成员人数*/
  rpc BatGetSocialCommunityKernelMembers(ga.muse_social_community_logic.BatGetSocialCommunityKernelMembersRequest) returns (ga.muse_social_community_logic.BatGetSocialCommunityKernelMembersResponse) {
    option (logic.gateway.command) = {
      id: 36736
    };
  }

  /*获取群聊核心成员*/
  rpc GetGroupActiveMembers(ga.muse_social_community_logic.GetGroupActiveMembersRequest) returns (ga.muse_social_community_logic.GetGroupActiveMembersResponse) {
    option (logic.gateway.command) = {
      id: 36737
    };
  }

  /* 获取社群成员列表*/
  rpc GetSocialCommunityMemberList(ga.muse_social_community_logic.GetSocialCommunityMemberListRequest) returns (ga.muse_social_community_logic.GetSocialCommunityMemberListResponse) {
    option (logic.gateway.command) = {
      id: 36738
    };
  }

  /* 获取社群新消息数量*/
  rpc GetSocialCommunityAnnounceNewsCount(ga.muse_social_community_logic.GetSocialCommunityAnnounceNewsCountRequest) returns (ga.muse_social_community_logic.GetSocialCommunityAnnounceNewsCountResponse) {
    option (logic.gateway.command) = {
      id: 36740
    };
  }

  /*创建、修改公告*/
  rpc UpsertMuseSocialAnnounce(ga.muse_social_community_logic.UpsertMuseSocialAnnounceRequest) returns (ga.muse_social_community_logic.UpsertMuseSocialAnnounceResponse) {
    option (logic.gateway.command) = {
      id: 36741
    };
  }

  /*公告位置列表*/
  rpc ListAnnounceDestinations(ga.muse_social_community_logic.ListAnnounceDestinationsRequest) returns (ga.muse_social_community_logic.ListAnnounceDestinationsResponse) {
    option (logic.gateway.command) = {
      id: 36742
    };
  }
  /*公告列表*/
  rpc ListMuseSocialAnnounces(ga.muse_social_community_logic.ListMuseSocialAnnouncesRequest) returns (ga.muse_social_community_logic.ListMuseSocialAnnouncesResponse) {
    option (logic.gateway.command) = {
      id: 36743
    };
  }

  /* 设置感兴趣*/
  rpc SetMuseSocialAnnounceInterest(ga.muse_social_community_logic.SetMuseSocialAnnounceInterestRequest) returns (ga.muse_social_community_logic.SetMuseSocialAnnounceInterestResponse) {
    option (logic.gateway.command) = {
      id: 36744
    };
  }


  /* 移除通告牌*/
  rpc RemoveMuseSocialAnnounce(ga.muse_social_community_logic.RemoveMuseSocialAnnounceRequest) returns (ga.muse_social_community_logic.RemoveMuseSocialAnnounceResponse) {
    option (logic.gateway.command) = {
      id: 36745
    };
  }

  /*通告牌感兴趣的成员列表*/
  rpc ListMuseSocialAnnounceInterestUsers(ga.muse_social_community_logic.ListMuseSocialAnnounceInterestUsersRequest) returns (ga.muse_social_community_logic.ListMuseSocialAnnounceInterestUsersResponse) {
    option (logic.gateway.command) = {
      id: 36746
    };
  }

  /*通告牌感兴趣的成员列表*/
  rpc ValidateUserHasCreateAnnouncePermissions(ga.muse_social_community_logic.ValidateUserHasCreateAnnouncePermissionsRequest) returns (ga.muse_social_community_logic.ValidateUserHasCreateAnnouncePermissionsResponse) {
    option (logic.gateway.command) = {
      id: 36747
    };
  }

  /*设置加群模式*/
  rpc SetCommunityAdditionMode(ga.muse_social_community_logic.SetCommunityAdditionModeRequest) returns (ga.muse_social_community_logic.SetCommunityAdditionModeResponse) {
    option (logic.gateway.command) = {
      id: 36748
    };
  }

  /*获取加群模式*/
  rpc GetCommunityAdditionMode(ga.muse_social_community_logic.GetCommunityAdditionModeRequest) returns (ga.muse_social_community_logic.GetCommunityAdditionModeResponse) {
    option (logic.gateway.command) = {
      id: 36749
    };
  }

  /*获取社群系统通知列表*/
  rpc ListSocialCommunitySystemMessage(ga.muse_social_community_logic.ListSocialCommunitySystemMessageRequest) returns (ga.muse_social_community_logic.ListSocialCommunitySystemMessageResponse) {
    option (logic.gateway.command) = {
      id: 36750
    };
  }

  /*提交加入请求*/
  rpc SubmitApplicationToJoinCommunity(ga.muse_social_community_logic.SubmitApplicationToJoinCommunityRequest) returns (ga.muse_social_community_logic.SubmitApplicationToJoinCommunityResponse) {
    option (logic.gateway.command) = {
      id: 36751
    };
  }

  /*成员申请加入社群的状态变更 */
  rpc UpsertJoinSocialCommunityMessageStatus(ga.muse_social_community_logic.UpsertJoinSocialCommunityMessageStatusRequest) returns (ga.muse_social_community_logic.UpsertJoinSocialCommunityMessageStatusResponse) {
    option (logic.gateway.command) = {
      id: 36752
    };
  }

  /*获取升级提醒*/
  rpc GetSocialCommunityUpdateLevelTip(ga.muse_social_community_logic.GetSocialCommunityUpdateLevelTipRequest) returns (ga.muse_social_community_logic.GetSocialCommunityUpdateLevelTipResponse) {
    option (logic.gateway.command) = {
      id: 36753
    };
  }

  /*社团等级页*/
  rpc GetSocialCommunityLevelDetail(ga.muse_social_community_logic.GetSocialCommunityLevelDetailRequest) returns (ga.muse_social_community_logic.GetSocialCommunityLevelDetailResponse) {
    option (logic.gateway.command) = {
      id: 36754
    };
  }

  /*签到*/
  rpc SocialCommunityCheckIn(ga.muse_social_community_logic.SocialCommunityCheckInRequest) returns (ga.muse_social_community_logic.SocialCommunityCheckInResponse) {
    option (logic.gateway.command) = {
      id: 36755
    };
  }

  /*我的社群红点展示*/
  rpc GetSocialCommunityContentStreamNewsCount(ga.muse_social_community_logic.GetSocialCommunityContentStreamNewsCountRequest) returns (ga.muse_social_community_logic.GetSocialCommunityContentStreamNewsCountResponse) {
    option (logic.gateway.command) = {
      id: 36756
    };
  }

  /*根据品类id或者帖子id获取流信息*/
  rpc GetSocialCommunityContentStream(ga.muse_social_community_logic.GetSocialCommunityContentStreamRequest) returns (ga.muse_social_community_logic.GetSocialCommunityContentStreamResponse) {
    option (logic.gateway.command) = {
      id: 36757
    };
  }

  /*获取互动消息评论*/
  rpc ListMuseSocialCommunityCommentMessage(ga.muse_social_community_logic.ListMuseSocialCommunityCommentMessageRequest) returns (ga.muse_social_community_logic.ListMuseSocialCommunityCommentMessageResponse) {
    option (logic.gateway.command) = {
      id: 36758
    };
  }

  /*获取互动消息点赞*/
  rpc ListMuseSocialCommunityAttitudeMessage(ga.muse_social_community_logic.ListMuseSocialCommunityAttitudeMessageRequest) returns (ga.muse_social_community_logic.ListMuseSocialCommunityAttitudeMessageResponse) {
    option (logic.gateway.command) = {
      id: 36759
    };
  }

  /*获取私域用户卡片详情*/
  rpc GetSocialCommunityNonPublicUserCard(ga.muse_social_community_logic.GetSocialCommunityNonPublicUserCardRequest) returns (ga.muse_social_community_logic.GetSocialCommunityNonPublicUserCardResponse) {
    option (logic.gateway.command) = {
      id: 36760
    };
  }

  /*根据品类获取推荐社团id*/
  rpc IntroduceSocialCommunityByCategoryId(ga.muse_social_community_logic.IntroduceSocialCommunityByCategoryIdRequest) returns (ga.muse_social_community_logic.IntroduceSocialCommunityByCategoryIdResponse) {
    option (logic.gateway.command) = {
      id: 36761
    };
  }

  /*导航栏*/
  rpc ListMuseSocialCommunityNavBarsV2(ga.muse_social_community_logic.ListMuseSocialCommunityNavBarsV2Request) returns (ga.muse_social_community_logic.ListMuseSocialCommunityNavBarsV2Response) {
    option (logic.gateway.command) = {
      id: 36762
    };
  }

  /*获取二级导航栏*/
  rpc ListMuseSocialCommunityNavSecondaryBarsV2(ga.muse_social_community_logic.ListMuseSocialCommunityNavSecondaryBarsV2Request) returns (ga.muse_social_community_logic.ListMuseSocialCommunityNavSecondaryBarsV2Response) {
    option (logic.gateway.command) = {
      id: 36763
    };
  }

  /*获取群聊列表*/
  rpc ListMuseSocialCommunityGroups(ga.muse_social_community_logic.ListMuseSocialCommunityGroupsRequest) returns (ga.muse_social_community_logic.ListMuseSocialCommunityGroupsResponse) {
    option (logic.gateway.command) = {
      id: 36764
    };
  }

  /*获取社群导航状态*/
  rpc BatchMuseSocialCommunityNavBarsV2(ga.muse_social_community_logic.BatchMuseSocialCommunityNavBarsV2Request) returns (ga.muse_social_community_logic.BatchMuseSocialCommunityNavBarsV2Response) {
    option (logic.gateway.command) = {
      id: 36765
    };
  }

  /*编辑社群信息*/
  rpc UpdateSocialCommunityInfo(ga.muse_social_community_logic.UpdateSocialCommunityInfoRequest) returns (ga.muse_social_community_logic.UpdateSocialCommunityInfoResponse) {
    option (logic.gateway.command) = {
      id: 36766
    };
  }

  /*获取可编辑的社群信息*/
  rpc GetSocialCommunityEditableInfo(ga.muse_social_community_logic.GetSocialCommunityEditableInfoRequest) returns (ga.muse_social_community_logic.GetSocialCommunityEditableInfoResponse) {
    option (logic.gateway.command) = {
      id: 36767
    };
  }

  //上报用户在个人房点击了社群入口
  rpc ReportPersonalChannelViewSocialCommunity(ga.muse_social_community_logic.ReportPersonalChannelViewSocialCommunityRequest) returns (ga.muse_social_community_logic.ReportPersonalChannelViewSocialCommunityResponse) {
    option (logic.gateway.command) = {
      id: 36768
    };
  }

  //查询用户是否加入了某个社群
  rpc GetMemberStatusInTheSocialCommunity(ga.muse_social_community_logic.GetMemberStatusInTheSocialCommunityRequest) returns (ga.muse_social_community_logic.GetMemberStatusInTheSocialCommunityResponse) {
    option (logic.gateway.command) = {
      id: 36769
    };
  }

  // 获取社团助手消息数
  rpc GetSocialCommunityAssistantMsgCount(ga.muse_social_community_logic.GetSocialCommunityAssistantMsgCountRequest) returns (ga.muse_social_community_logic.GetSocialCommunityAssistantMsgCountResponse) {
    option (logic.gateway.command) = {
      id: 36770
    };
  }

}
