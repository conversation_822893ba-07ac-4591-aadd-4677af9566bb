syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/music-list-channel-logic";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_play/channel-play_.proto";


service MusicListChannelLogic {
  option (logic.gateway.service_ext) = {
    service_name: "music-list-channel-logic"
  };

  rpc HomePageMixChannelList(ga.channel_play.ListTopicChannelReq) returns (ga.channel_play.ListTopicChannelResp) {
    option (logic.gateway.command) = {
      id: 31534;
    };
  }

}
