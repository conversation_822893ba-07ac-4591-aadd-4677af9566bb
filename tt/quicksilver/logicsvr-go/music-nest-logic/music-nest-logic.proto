syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "music_nest_logic/music-nest-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/music-nest-logic";

service MusicNestLogic {
  option (logic.gateway.service_ext) = {
    service_name: "music-nest-logic"
  };

  //音乐首页获取乐窝封面和现场
  rpc GetMusicNestCoverAndLiveList (ga.music_nest_logic.GetMusicNestCoverAndLiveListReq) returns (ga.music_nest_logic.GetMusicNestCoverAndLiveListResp) {
    option (logic.gateway.command) = {
      id: 31321
    };
  }

  //关注乐窝
  rpc SubMusicNest (ga.music_nest_logic.SubMusicNestReq) returns (ga.music_nest_logic.SubMusicNestResp) {
    option (logic.gateway.command) = {
      id: 31322
    };
  }

  //获取乐窝首页
  rpc GetMusicNestHomePage (ga.music_nest_logic.GetMusicNestHomePageReq) returns (ga.music_nest_logic.GetMusicNestHomePageResp) {
    option (logic.gateway.command) = {
      id: 31323
    };
  }

  //关注乐窝某场活动
  rpc SubMusicNestActivity (ga.music_nest_logic.SubMusicNestActivityReq) returns (ga.music_nest_logic.SubMusicNestActivityResp) {
    option (logic.gateway.command) = {
      id: 31324
    };
  }

  // 节目单相关接口
  rpc GetMusicNestPerformance (ga.music_nest_logic.GetMusicNestPerformanceReq) returns (ga.music_nest_logic.GetMusicNestPerformanceResp) {
    option (logic.gateway.command) = {
      id: 31325
    };
  }

  rpc SetCurrentMusicNestPerformanceStage (ga.music_nest_logic.SetCurrentMusicNestPerformanceStageReq) returns (ga.music_nest_logic.SetCurrentMusicNestPerformanceStageResp) {
    option (logic.gateway.command) = {
      id: 31326
    };
  }

  /* 关闭下一场引导 */
  rpc CloseNestDirectionAct (ga.music_nest_logic.CloseNestDirectionActReq) returns (ga.music_nest_logic.CloseNestDirectionActResp) {
    option (logic.gateway.command) = {
      id: 31328
    };
  }

  // 已看过人数获取
  rpc GetSpecifiedChannelVisitedSize (ga.music_nest_logic.GetSpecifiedChannelVisitedSizeReq) returns (ga.music_nest_logic.GetSpecifiedChannelVisitedSizeResp) {
    option (logic.gateway.command) = {
      id: 31331
    };
  }
  rpc GetWelcomePop(ga.music_nest_logic.GetWelcomePopReq)returns (ga.music_nest_logic.GetWelcomePopResp){
    option (logic.gateway.command) = {
      id: 31341
    };
  }
  rpc UserClickPop(ga.music_nest_logic.UserClickPopReq)returns (ga.music_nest_logic.UserClickPopResp){
    option (logic.gateway.command) = {
      id: 31342
    };
  }
  rpc GetMusicNestLiveInfo(ga.music_nest_logic.GetMusicNestLiveInfoReq)returns (ga.music_nest_logic.GetMusicNestLiveInfoResp){
    option (logic.gateway.command) = {
      id: 31343
    };
  }

  // 获取乐窝配置的社群信息
  rpc GetMusicNestSocialCommunity(ga.music_nest_logic.GetMusicNestSocialCommunityReq)returns (ga.music_nest_logic.GetMusicNestSocialCommunityResp){
    option (logic.gateway.command) = {
      id: 31563
    };
  }


}