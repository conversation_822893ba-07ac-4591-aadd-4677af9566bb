syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;


// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/music-topic-channel-logic";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "hobby_channel/hobby-channel_.proto";
import "music_topic_channel/music-topic-channel-logic_.proto";


service MusicTopicChannelLogic {
  option (logic.gateway.service_ext) = {
    service_name: "music-topic-channel-logic"
  };

  rpc GetMusicChannelFilterV2 (ga.music_topic_channel.GetMusicChannelFilterV2Req) returns (ga.music_topic_channel.GetMusicChannelFilterV2Resp) {
    option (logic.gateway.command) = {
      id: 31520
    };
  }

  rpc ListHobbyChannelV2 (ga.music_topic_channel.ListHobbyChannelV2Req ) returns (ga.hobby_channel.ListHobbyChannelResp) {
    option (logic.gateway.command) = {
      id: 31521
    };
  }

  rpc GetMusicHomePageViewV2 (ga.music_topic_channel.GetMusicHomePageViewV2Req ) returns (ga.music_topic_channel.GetMusicHomePageViewV2Resp) {
    option (logic.gateway.command) = {
      id: 31522
    };
  }

  rpc QuickMatchHobbyChannelV2 (ga.music_topic_channel.QuickMatchHobbyChannelV2Req ) returns (ga.music_topic_channel.QuickMatchHobbyChannelV2Resp) {
    option (logic.gateway.command) = {
      id: 31523
    };
  }

  rpc PublishMusicChannel (ga.music_topic_channel.PublishMusicChannelReq) returns (ga.music_topic_channel.PublishMusicChannelResp) {
    option (logic.gateway.command) = {
      id: 31524
    };
  }

  rpc CancelMusicChannelPublish (ga.music_topic_channel.CancelMusicChannelPublishReq) returns (ga.music_topic_channel.CancelMusicChannelPublishResp) {
    option (logic.gateway.command) = {
      id: 31525
    };
  }

  rpc GetMusicFilterItemByIds (ga.music_topic_channel.GetMusicFilterItemByIdsReq) returns (ga.music_topic_channel.GetMusicFilterItemByIdsResp) {
    option (logic.gateway.command) = {
      id: 31526
    };
  }

  rpc ListMusicChannels (ga.music_topic_channel.ListMusicChannelsReq) returns (ga.music_topic_channel.ListMusicChannelsResp) {
    option (logic.gateway.command) = {
      id: 31527
    };
  }

  rpc GetTabPublishHotRcmd (ga.music_topic_channel.GetTabPublishHotRcmdReq) returns (ga.music_topic_channel.GetTabPublishHotRcmdResp) {
    option (logic.gateway.command) = {
      id: 31528
    };
  }
  rpc GetResourceConfigByChannelId (ga.music_topic_channel.GetResourceConfigByChannelIdReq) returns (ga.music_topic_channel.GetResourceConfigByChannelIdResp) {
    option (logic.gateway.command) = {
      id: 31529
    };
  }

  rpc MuseGetTopicChannelInfo(ga.music_topic_channel.MuseGetTopicChannelInfoRequest) returns (ga.music_topic_channel.MuseGetTopicChannelInfoResponse) {
    option (logic.gateway.command) = {
      id: 31530;
    };
  }

  rpc GetAssociateRevChannels(ga.music_topic_channel.GetAssociateRevChannelsRequest) returns (ga.music_topic_channel.GetAssociateRevChannelsResponse) {
    option (logic.gateway.command) = {
      id: 31531;
    };
  }

  // 社群导航栏房间频道列表
  rpc ListMuseSocialCommunityChannels(ga.music_topic_channel.ListMuseSocialCommunityChannelsRequest) returns (ga.music_topic_channel.ListMuseSocialCommunityChannelsResponse) {
    option (logic.gateway.command) = {
      id: 31532;
    };
  }

  // 偏好关键词
  rpc ListChannelPreferenceKeywords(ga.music_topic_channel.ListChannelPreferenceKeywordsRequest) returns (ga.music_topic_channel.ListChannelPreferenceKeywordsResponse) {
    option (logic.gateway.command) = {
      id: 31535;
    };
  }

}
