syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "mystery_box_logic/mystery-box-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/mystery-box-logic";

service MysteryBoxLogic {
	option (logic.gateway.service_ext) = {
		service_name: "mystery-box-logic"
	};

	rpc GetMysteryBoxWin (ga.mystery_box_logic.GetMysteryBoxWinReq) returns (ga.mystery_box_logic.GetMysteryBoxWinResp) {
		option (logic.gateway.command) = {
			id: 33086
		};
	}

    rpc GetMysteryBoxTask (ga.mystery_box_logic.GetMysteryBoxTaskReq) returns (ga.mystery_box_logic.GetMysteryBoxTaskResp) {
        option (logic.gateway.command) = {
            id: 33087
        };
    }
}