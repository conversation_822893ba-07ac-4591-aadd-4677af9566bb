syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "mystery_place_logic/mystery-place-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/mystery-place-logic";


service MysteryPlaceLogic {
  option (logic.gateway.service_ext) = {
    service_name: "mystery-place-logic"
  };

  rpc ListScenarioInfo (ga.mystery_place_logic.ListScenarioInfoReq) returns (ga.mystery_place_logic.ListScenarioInfoResp) {
    option (logic.gateway.command) = {
      id: 36000
    };
  }

  rpc GetScenarioInfo (ga.mystery_place_logic.GetScenarioInfoReq) returns (ga.mystery_place_logic.GetScenarioInfoResp) {
    option (logic.gateway.command) = {
      id: 36001
    };
  }

  rpc GetLoginTask (ga.mystery_place_logic.GetLoginTaskReq) returns (ga.mystery_place_logic.GetLoginTaskResp) {
    option (logic.gateway.command) = {
      id: 36002
    };
  }

  rpc ReceiveLoginTaskAward (ga.mystery_place_logic.ReceiveLoginTaskAwardReq) returns (ga.mystery_place_logic.ReceiveLoginTaskAwardResp) {
    option (logic.gateway.command) = {
      id: 36003
    };
  }

  rpc GetBalanceInfo (ga.mystery_place_logic.GetBalanceInfoReq) returns (ga.mystery_place_logic.GetBalanceInfoResp) {
    option (logic.gateway.command) = {
      id: 36004
    };
  }

  rpc CheckScenarioRoom (ga.mystery_place_logic.CheckScenarioRoomReq) returns (ga.mystery_place_logic.CheckScenarioRoomResp) {
    option (logic.gateway.command) = {
      id: 36005
    };
  }

  rpc Invite2MyRoom (ga.mystery_place_logic.Invite2MyRoomReq) returns (ga.mystery_place_logic.Invite2MyRoomResp) {
    option (logic.gateway.command) = {
      id: 36006
    };
  }

  rpc GetInvite2MyRoomResult (ga.mystery_place_logic.GetInvite2MyRoomResultReq) returns (ga.mystery_place_logic.GetInvite2MyRoomResultResp) {
    option (logic.gateway.command) = {
      id: 36007
    };
  }

  rpc ListRecommendedScenarioSimpleInfo (ga.mystery_place_logic.ListRecommendedScenarioSimpleInfoReq) returns (ga.mystery_place_logic.ListRecommendedScenarioSimpleInfoResp) {
    option (logic.gateway.command) = {
      id: 36008
    };
  }

  rpc GetRecommendedScenarioDetailInfo (ga.mystery_place_logic.GetRecommendedScenarioDetailInfoReq) returns (ga.mystery_place_logic.GetRecommendedScenarioDetailInfoResp) {
    option (logic.gateway.command) = {
      id: 36009
    };
  }

  rpc GetRoomShareLinkByTabId (ga.mystery_place_logic.GetRoomShareLinkByTabIdReq) returns (ga.mystery_place_logic.GetRoomShareLinkByTabIdResp) {
    option (logic.gateway.command) = {
      id: 36010
    };
  }

  rpc MysteryPlaceChannelList (ga.mystery_place_logic.MysteryPlaceChannelListReq) returns (ga.mystery_place_logic.MysteryPlaceChannelListResp) {
    option (logic.gateway.command) = {
      id: 36011
    };
  }

  rpc GetCommentPageParams (ga.mystery_place_logic.GetCommentPageParamsReq) returns (ga.mystery_place_logic.GetCommentPageParamsResp) {
    option (logic.gateway.command) = {
      id: 36012
    };
  }

  rpc CommentToScenario (ga.mystery_place_logic.CommentToScenarioReq) returns (ga.mystery_place_logic.CommentToScenarioResp) {
    option (logic.gateway.command) = {
      id: 36013
    };
  }

  rpc GetNewScenarioTip(ga.mystery_place_logic.GetNewScenarioTipReq) returns (ga.mystery_place_logic.GetNewScenarioTipResp) {
    option (logic.gateway.command) = {
      id: 36020;
    };
  }

  rpc MarkNewScenarioTipRead(ga.mystery_place_logic.MarkNewScenarioTipReadReq) returns (ga.mystery_place_logic.MarkNewScenarioTipReadResp) {
    option (logic.gateway.command) = {
      id: 36021;
    };
  }

  rpc MysteryPlaceClientConfig(ga.mystery_place_logic.MysteryPlaceClientConfigReq) returns (ga.mystery_place_logic.MysteryPlaceClientConfigResp) {
    option (logic.gateway.command) = {
      id: 36022;
    };
  }

  rpc GetPlayedScenarioRecordList(ga.mystery_place_logic.GetPlayedScenarioRecordListReq) returns (ga.mystery_place_logic.GetPlayedScenarioRecordListResp) {
    option (logic.gateway.command) = {
      id: 36023;
    };
  }

  rpc GetScenarioChapterSummary(ga.mystery_place_logic.GetScenarioChapterSummaryReq) returns (ga.mystery_place_logic.GetScenarioChapterSummaryResp) {
    option (logic.gateway.command) = {
      id: 36024;
    };
  }

  rpc SetPlayedScenarioRecordVisibility(ga.mystery_place_logic.SetPlayedScenarioRecordVisibilityReq) returns (ga.mystery_place_logic.SetPlayedScenarioRecordVisibilityResp) {
    option (logic.gateway.command) = {
      id: 36025;
    };
  }

  rpc GetPlayedScenarioRecordDetail(ga.mystery_place_logic.GetPlayedScenarioRecordDetailReq) returns (ga.mystery_place_logic.GetPlayedScenarioRecordDetailResp) {
    option (logic.gateway.command) = {
      id: 36026;
    };
  }

  rpc GetRcmdMiJingTab(ga.mystery_place_logic.GetRcmdMiJingTabReq) returns (ga.mystery_place_logic.GetRcmdMiJingTabResp) {
    option (logic.gateway.command) = {
      id: 36027;
    };
  }
/*
  rpc GetHomepageTrifleRecordList(ga.mystery_place_logic.GetHomepageTrifleRecordListReq) returns (ga.mystery_place_logic.GetHomepageTrifleRecordListResp) {
    option (logic.gateway.command) = {
      id: 36028;
    };
  }
  rpc GetTrifleRecordList(ga.mystery_place_logic.GetTrifleRecordListReq) returns (ga.mystery_place_logic.GetTrifleRecordListResp) {
    option (logic.gateway.command) = {
      id: 36029;
    };
  }
  rpc MarkTrifleRead(ga.mystery_place_logic.MarkTrifleReadReq) returns (ga.mystery_place_logic.MarkTrifleReadResp) {
    option (logic.gateway.command) = {
      id: 36030;
    };
  }*/

  rpc ListRecommendedScenarioDetailInfo (ga.mystery_place_logic.ListRecommendedScenarioDetailInfoReq) returns (ga.mystery_place_logic.ListRecommendedScenarioDetailInfoResp) {
    option (logic.gateway.command) = {
      id: 36031
    };
  }

  // 用户是否有剧本限免券
  rpc IsUserHasScenarioFreeCoupons (ga.mystery_place_logic.IsUserHasScenarioFreeCouponsReq) returns (ga.mystery_place_logic.IsUserHasScenarioFreeCouponsResp) {
    option (logic.gateway.command) = {
      id: 36033
    };
  }

  rpc HomePageBigTofu(ga.mystery_place_logic.HomePageBigTofuReq) returns (ga.mystery_place_logic.HomePageBigTofuResp) {
    option (logic.gateway.command) = {
      id: 36040;
    };
  }
  rpc HomePageRightTofu(ga.mystery_place_logic.HomePageRightTofuReq) returns (ga.mystery_place_logic.HomePageRightTofuResp) {
    option (logic.gateway.command) = {
      id: 36041;
    };
  }
  // 获取房间剧本信息
  rpc GetChannelScenarioInfo(ga.mystery_place_logic.GetChannelScenarioInfoReq) returns (ga.mystery_place_logic.GetChannelScenarioInfoResp) {
    option (logic.gateway.command) = {
      id: 36044;
    };
  }
  // 设置预约状态
  rpc ReportScenarioSubscribeStatus(ga.mystery_place_logic.ReportScenarioSubscribeStatusReq) returns (ga.mystery_place_logic.ReportScenarioSubscribeStatusResp){
    option (logic.gateway.command) = {
      id: 36065;
    };
  }
  // 根据ID获取剧本记录
  rpc GetPlayedScenarioRecordListByID(ga.mystery_place_logic.GetPlayedScenarioRecordListByIDReq) returns (ga.mystery_place_logic.GetPlayedScenarioRecordListByIDResp) {
    option (logic.gateway.command) = {
      id: 36067;
    };
  }
  // 风控
  rpc CheckRisk(ga.mystery_place_logic.CheckRiskReq) returns (ga.mystery_place_logic.CheckRiskResp) {
    option (logic.gateway.command) = {
      id: 36068;
    };
  }
  // 获取剧本热度榜单信息
  rpc GetHotRankScenarioList(ga.mystery_place_logic.GetHotRankScenarioListReq) returns (ga.mystery_place_logic.GetHotRankScenarioListResp) {
    option (logic.gateway.command) = {
      id: 36431;
    };
  }
  // 获取剧本热度榜单信息
  rpc MijingShowCommentEntrance(ga.mystery_place_logic.MijingShowCommentEntranceReq) returns (ga.mystery_place_logic.MijingShowCommentEntranceResp) {
    option (logic.gateway.command) = {
      id: 36432;
    };
  }
}