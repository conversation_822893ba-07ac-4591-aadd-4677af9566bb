syntax = "proto3";
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "nameplate/nameplate_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/nameplate-logic";

service NameplateLogic {
    option (logic.gateway.service_ext) = {
        service_name: "nameplate-logic"
    };
    // 获取用户的铭牌
    rpc GetUserNameplate(ga.nameplate.GetUserNameplateReq) returns (ga.nameplate.GetUserNameplateResp){
        option (logic.gateway.command) = {
            id: 103060
        };
    }
}
