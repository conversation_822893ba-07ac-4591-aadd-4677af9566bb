syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "numeric_logic/numeric-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/numeric-logic";

service NumericLogic {

  // 设置页面 获取签约用户财富值控制开关状态
  rpc GetUserRichSwitch (ga.numeric_logic.GetUserRichSwitchReq) returns (ga.numeric_logic.GetUserRichSwitchResp) {
    option (logic.gateway.command) = {
      id: 31400
    };
  }

  // 设置页面 变更签约用户财富值控制开关状态
  rpc SetUserRichSwitch (ga.numeric_logic.SetUserRichSwitchReq) returns (ga.numeric_logic.SetUserRichSwitchResp) {
    option (logic.gateway.command) = {
      id: 31401
    };
  }

  rpc GetUserNumericLock(ga.numeric_logic.GetUserNumericLockReq) returns (ga.numeric_logic.GetUserNumericLockResp) {
    option (logic.gateway.command) = {
      id: 31402; // 获取用户财富值魅力值锁定状态
    };
  }

  rpc SetUserNumericLock(ga.numeric_logic.SetUserNumericLockReq) returns (ga.numeric_logic.SetUserNumericLockResp) {
    option (logic.gateway.command) = {
      id: 31403; // 设置用户财富值魅力值锁定状态
    };
  }

  // 获取荣誉榜单
  rpc GetUserGloryRank(ga.numeric_logic.GetUserGloryRankReq) returns (ga.numeric_logic.GetUserGloryRankResp) {
    option (logic.gateway.command) = {
      id: 31410
    };
  }

  // 获取用户VIP礼包信息
  rpc GetUserVipGiftPackageInfo(ga.numeric_logic.GetUserVipGiftPackageInfoReq) returns (ga.numeric_logic.GetUserVipGiftPackageInfoResp) {
    option (logic.gateway.command) = {
      id: 31415;
    };
  }
}
