syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "oauth2logic/oauth2-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/oauth2logic";

service OAuth2Logic {
  rpc GetScope (ga.oauth2logic.GetScopeReq) returns (ga.oauth2logic.GetScopeResp) {
    option (logic.gateway.command) = {
      id: 30390
    };
  }
  
  rpc GetAuthorizeCode (ga.oauth2logic.GetAuthorizeCodeReq) returns (ga.oauth2logic.GetAuthorizeCodeResp) {
    option (logic.gateway.command) = {
      id: 30391
    };
  }
}