syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/obs-logic";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "obs/obs.proto";

service ObsLogic {
  option (logic.gateway.service_ext) = {
    service_name: "obs-logic"
  };

  rpc ClaimToken(ga.obs.ClaimTokenRequest) returns (ga.obs.ClaimTokenResponse) {
    option (logic.gateway.command) = {
      id: 30861
    };
  }
}

