syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "official_live_channel/official-live-channel_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/official-live-channel-logic";

service OfficialLiveChannelLogic {
    option (logic.gateway.service_ext) = {
        service_name: "official-live-channel-logic"
    };

    rpc GetOfficialLiveChannelInfo (ga.official_live_channel.GetOfficialLiveChannelInfoReq) returns (ga.official_live_channel.GetOfficialLiveChannelInfoResp) {
        option (logic.gateway.command) = {
            id: 30410
      };
    }

    rpc GetOfficialLiveChannelRelaySchedule (ga.official_live_channel.GetOfficialLiveChannelRelayScheduleReq) returns (ga.official_live_channel.GetOfficialLiveChannelRelayScheduleResp) {
        option (logic.gateway.command) = {
            id: 30411
      };
    }

    rpc CancelOfficialLiveChannelRelay (ga.official_live_channel.CancelOfficialLiveChannelRelayReq) returns (ga.official_live_channel.CancelOfficialLiveChannelRelayResp) {
        option (logic.gateway.command) = {
            id: 30412
      };
    }

    rpc GetLiveChannelRelay (ga.official_live_channel.GetLiveChannelRelayReq) returns (ga.official_live_channel.GetLiveChannelRelayResp) {
        option (logic.gateway.command) = {
            id: 30413
      };
    }

    rpc ReportRelayLiveChannelAudio (ga.official_live_channel.ReportRelayLiveChannelAudioReq) returns (ga.official_live_channel.ReportRelayLiveChannelAudioResp) {
        option (logic.gateway.command) = {
            id: 30414
      };
    }

    rpc GetOfficialChannelDescribe (ga.official_live_channel.GetOfficialChannelDescribeReq) returns (ga.official_live_channel.GetOfficialChannelDescribeResp) {
        option (logic.gateway.command) = {
            id: 30415
        };
    }
}
