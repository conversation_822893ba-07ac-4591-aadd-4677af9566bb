syntax="proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/offline_msg_logic";


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "push/push_.proto";

// Offline Msg Logic Service
service OfflineMsgLogic {
    option (logic.gateway.service_ext) = {
        service_name: "offline-msg-logic"
    };

    // 获取离线消息
    rpc PullOfflineMessages (ga.push.PullOfflineMessagesReq) returns (ga.push.PullOfflineMessagesResp) {
        option (logic.gateway.command) = {
            id: 20000,
        };
    };

    rpc ReportMessagesReceivedAck (ga.push.MessagesReceivedAckReq) returns (ga.push.MessagesReceivedAckResp) {
        option (logic.gateway.command) = {
            id: 20001,
        };
    };
}
