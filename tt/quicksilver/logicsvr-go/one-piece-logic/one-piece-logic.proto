syntax = "proto3";

package logic.one_piece_logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "one_piece_logic/one-piece-logic_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/one-piece-logic";

service OnePieceLogic {
  option (logic.gateway.service_ext) = {
    service_name: "one-piece-logic"
  };

  rpc GetEntryAndNotifyInfo (ga.one_piece_logic.GetEntryAndNotifyInfoReq) returns (ga.one_piece_logic.GetEntryAndNotifyInfoResp) {
    option (logic.gateway.command) = {
      id: 32121   //改成正确的id
    };
  }

  rpc LotteryDraw (ga.one_piece_logic.LotteryDrawReq) returns (ga.one_piece_logic.LotteryDrawResp) {
    option (logic.gateway.command) = {
      id: 32122   //改成正确的id
    };
  }

  rpc BuyChance (ga.one_piece_logic.BuyChanceReq) returns (ga.one_piece_logic.BuyChanceResp) {
    option (logic.gateway.command) = {
      id: 32123   //改成正确的id
    };
  }

  rpc GetOnePieceInfo (ga.one_piece_logic.GetOnePieceInfoReq) returns (ga.one_piece_logic.GetOnePieceInfoResp) {
    option (logic.gateway.command) = {
      id: 32124   //改成正确的id
    };
  }

  rpc GetWinningRecord (ga.one_piece_logic.GetWinningRecordsReq) returns (ga.one_piece_logic.GetWinningRecordsResp) {
    option (logic.gateway.command) = {
      id: 32125   //改成正确的id
    };
  }

  rpc GetRecentWinningRecords (ga.one_piece_logic.GetRecentWinningRecordsReq) returns (ga.one_piece_logic.GetRecentWinningRecordsResp) {
    option (logic.gateway.command) = {
      id: 32126   //改成正确的id
    };
  }

}