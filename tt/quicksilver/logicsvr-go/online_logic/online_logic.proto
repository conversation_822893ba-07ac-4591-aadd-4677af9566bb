syntax="proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/online_logic";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "online/friendol_.proto";

// Online Logic Service
service OnlineLogic {
    option (logic.gateway.service_ext) = {
        service_name: "online-logic"
    };

    // 获取群在线人数
    rpc GetGroupOnlineCount( ga.online.GetGroupOnlineCountReq ) returns( ga.online.GetGroupOnlineCountResp ) {
        option(logic.gateway.command) = {
            id: 1100
        };
    }

    // 上报用户在线游戏状态
    rpc ReportUserOnlineGameActive( ga.online.ReportPlayingGameReq ) returns( ga.online.ReportPlayingGameResp ) {
        option(logic.gateway.command) = {
            id: 1121
        };
    }

    // 获取用户在线好友列表
    rpc GetUserOnlineFriendList( ga.online.GetOnlineFriendsReq ) returns( ga.online.GetOnlineFriendsResp ) {
        option(logic.gateway.command) = {
            id: 1122
        };
    }

    // 获取用户离线好友列表
    rpc GetUserOfflineFriendList( ga.online.GetOfflineFriendsReq ) returns( ga.online.GetOfflineFriendsResp ) {
        option(logic.gateway.command) = {
            id: 1123
        };
    }

     // 修改频道跟随开关
    rpc ReportFollowChannelAuth( ga.online.UpdateFollowChannelAuthReq ) returns( ga.online.UpdateFollowChannelAuthResp ) {
        option(logic.gateway.command) = {
            id: 1124
        };
    }

    // 获取频道跟随开关
    rpc GetFollowChannelAuth( ga.online.GetFollowChannelAuthReq ) returns( ga.online.GetFollowChannelAuthResp ) {
        option(logic.gateway.command) = {
            id: 1125
        };
    }

    
    rpc GetUserFollowChannelInfo( ga.online.GetUserFollowChannelInfoReq ) returns( ga.online.GetUserFollowChannelInfoResp ) {
        option(logic.gateway.command) = {
            id: 1126
        };
    }

    rpc GetChannelOnlineMemberCnt( ga.online.GetChannelOnlineMemberCntReq ) returns( ga.online.GetChannelOnlineMemberCntResp ) {
        option(logic.gateway.command) = {
            id: 1127
        };
    }
}

