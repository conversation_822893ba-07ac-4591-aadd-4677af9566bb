syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "perfect_couple_match_logic/perfect_couple_match_logic.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/perfect-couple-match-logic";

service PerfectCoupleMatchLogic {
  option (logic.gateway.service_ext) = {
    service_name: "perfect-couple-match-logic"
  };

  // 获取当前游戏信息
  rpc GetPrefectCpGameInfo(ga.perfect_couple_match_logic.GetPrefectCpGameInfoRequest) returns (ga.perfect_couple_match_logic.GetPrefectCpGameInfoResponse) {
    option (logic.gateway.command) = {
      id: 36531
    };
  }

  // 设置游戏阶段
  rpc SetPrefectCpGamePhase(ga.perfect_couple_match_logic.SetPrefectCpGamePhaseRequest) returns (ga.perfect_couple_match_logic.SetPrefectCpGamePhaseResponse) {
    option (logic.gateway.command) = {
      id: 36532
    };
  }

  // 申请上玩家麦
  rpc ApplyToHoldMic(ga.perfect_couple_match_logic.ApplyToHoldMicRequest) returns (ga.perfect_couple_match_logic.ApplyToHoldMicResponse) {
    option (logic.gateway.command) = {
      id: 36533
    };
  }

  // 为指定玩家爆灯
  rpc BlowLight(ga.perfect_couple_match_logic.BlowLightRequest) returns (ga.perfect_couple_match_logic.BlowLightResponse) {
    option (logic.gateway.command) = {
      id: 36534
    };
  }

  // 选择心动对象
  rpc ChooseTheOne(ga.perfect_couple_match_logic.ChooseTheOneRequest) returns (ga.perfect_couple_match_logic.ChooseTheOneResponse) {
    option (logic.gateway.command) = {
      id: 36535
    };
  }

  // 查询已有线索
  rpc GetCoupleClues(ga.perfect_couple_match_logic.GetCoupleCluesRequest) returns (ga.perfect_couple_match_logic.GetCoupleCluesResponse) {
    option (logic.gateway.command) = {
      id: 36536
    };
  }

  // 查询我的问卷
  rpc GetMyQuestionnaire(ga.perfect_couple_match_logic.GetMyQuestionnaireRequest) returns (ga.perfect_couple_match_logic.GetMyQuestionnaireResponse) {
    option (logic.gateway.command) = {
      id: 36537
    };
  }

  // 查询我的道具
  rpc GetMyCluesProp(ga.perfect_couple_match_logic.GetMyCluesPropRequest) returns (ga.perfect_couple_match_logic.GetMyCluesPropResponse) {
    option (logic.gateway.command) = {
      id: 36538
    };
  }

  // 使用道具获得线索
  rpc UseCluesProp(ga.perfect_couple_match_logic.UseCluesPropRequest) returns (ga.perfect_couple_match_logic.UseCluesPropResponse) {
    option (logic.gateway.command) = {
      id: 36539
    };
  }

  // 主持人发放线索
  rpc PublishClues(ga.perfect_couple_match_logic.PublishCluesRequest) returns (ga.perfect_couple_match_logic.PublishCluesResponse) {
    option (logic.gateway.command) = {
      id: 36540
    };
  }

  // ============================== 匹配阶段 =============================
  // 获取天配匹配入口
  rpc GetPerfectMatchEntry(ga.perfect_couple_match_logic.GetPerfectMatchEntryRequest) returns (ga.perfect_couple_match_logic.GetPerfectMatchEntryResponse) {
    option (logic.gateway.command) = {
      id: 36551
    };
  }

  // 匹配报名
  rpc EnrollPerfectMatch(ga.perfect_couple_match_logic.EnrollPerfectMatchRequest) returns (ga.perfect_couple_match_logic.EnrollPerfectMatchResponse) {
    option (logic.gateway.command) = {
      id: 36552
    };
  }

  // 获取题目
  rpc GetPerfectMatchQuestions(ga.perfect_couple_match_logic.GetPerfectMatchQuestionsRequest) returns (ga.perfect_couple_match_logic.GetPerfectMatchQuestionsResponse) {
    option (logic.gateway.command) = {
      id: 36553
    };
  }

  // 匹配心跳
  rpc SendPerfectMatchHeartbeat(ga.perfect_couple_match_logic.SendPerfectMatchHeartbeatRequest) returns (ga.perfect_couple_match_logic.SendPerfectMatchHeartbeatResponse) {
    option (logic.gateway.command) = {
      id: 36554
    };
  }

  // 发送答案
  rpc SendPerfectMatchAnswer(ga.perfect_couple_match_logic.SendPerfectMatchAnswerRequest) returns (ga.perfect_couple_match_logic.SendPerfectMatchAnswerResponse) {
    option (logic.gateway.command) = {
      id: 36555
    };
  }

  // 取消匹配
  rpc CancelPerfectMatch(ga.perfect_couple_match_logic.CancelPerfectMatchRequest) returns (ga.perfect_couple_match_logic.CancelPerfectMatchResponse) {
    option (logic.gateway.command) = {
      id: 36556
    };
  }

}