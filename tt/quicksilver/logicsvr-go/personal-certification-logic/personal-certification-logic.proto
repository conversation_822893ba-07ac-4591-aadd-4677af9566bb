syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "personalcertificationlogic/personal-certification-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/personalcertificationlogic";

service PersonalCertificationLogic {
  option (logic.gateway.service_ext) = {
    service_name: "personal-certification-logic"
  };

  rpc GetPersonalCertification (ga.personalcertificationlogic.GetPersonalCertificationReq) returns (ga.personalcertificationlogic.GetPersonalCertificationResp) {
    option (logic.gateway.command) = {
      id: 31327
    };
  }

  /* 设置优先展示的认证标 */
  rpc SetPriorityDisplayCert (ga.personalcertificationlogic.SetPriorityDisplayCertReq) returns (ga.personalcertificationlogic.SetPriorityDisplayCertResp) {
    option (logic.gateway.command) = {
      id: 31701
    };
  }

}
