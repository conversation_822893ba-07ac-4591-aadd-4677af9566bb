syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "pgc_channel_game_logic/pgc-channel-game-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/pgc-channel-game-logic";


service PgcChannelGameLogic {
  option (logic.gateway.service_ext) = {
    service_name: "pgc-channel-game-logic"
  };
 
  // 获取小游戏列表
  rpc GetGameList(ga.pgc_channel_game_logic.GetGameListReq) returns (ga.pgc_channel_game_logic.GetGameListResp) {
    option (logic.gateway.command) = {
      id: 36351
    };
  }

  //设置小游戏开始或者结束
  rpc SetGamePhase(ga.pgc_channel_game_logic.SetGamePhaseReq) returns (ga.pgc_channel_game_logic.SetGamePhaseResp) {
    option (logic.gateway.command) = {
      id: 36352
    };
  }

  //甩雷
  rpc SetNextBombUser(ga.pgc_channel_game_logic.SetNextBombUserReq) returns (ga.pgc_channel_game_logic.SetNextBombUserResp) {
    option (logic.gateway.command) = {
      id: 36353
    };
  }


  //进房获取游戏信息
  rpc GetChannelGameInfo(ga.pgc_channel_game_logic.GetChannelGameInfoReq) returns (ga.pgc_channel_game_logic.GetChannelGameInfoResp) {
    option (logic.gateway.command) = {
      id: 36354
    };
  }

  // 数字炸弹报名
  rpc DigitalBombEnroll(ga.pgc_channel_game_logic.DigitalBombEnrollReq) returns (ga.pgc_channel_game_logic.DigitalBombEnrollResp) {
    option (logic.gateway.command) = {
      id: 36355
    };
  }

  // 数字炸弹选择参与用户
  rpc DigitalBombSelectGameUser(ga.pgc_channel_game_logic.DigitalBombSelectGameUserReq) returns (ga.pgc_channel_game_logic.DigitalBombSelectGameUserResp) {
    option (logic.gateway.command) = {
      id: 36356
    };
  }

  // 数字炸弹选择数字
  rpc DigitalBombSelectNumber(ga.pgc_channel_game_logic.DigitalBombSelectNumberReq) returns (ga.pgc_channel_game_logic.DigitalBombSelectNumberResp) {
    option (logic.gateway.command) = {
      id: 36357
    };
  }

  // 大冒险报名
  rpc AdventureEnroll(ga.pgc_channel_game_logic.AdventureEnrollReq) returns (ga.pgc_channel_game_logic.AdventureEnrollResp) {
    option (logic.gateway.command) = {
      id: 36358
    };
  }

  // 大冒险选择参与用户
  rpc AdventureSelectGameUser(ga.pgc_channel_game_logic.AdventureSelectGameUserReq) returns (ga.pgc_channel_game_logic.AdventureSelectGameUserResp) {
    option (logic.gateway.command) = {
      id: 36359
    };
  }

  // 大冒险摇步数
  rpc AdventureRandomSteps(ga.pgc_channel_game_logic.AdventureRandomStepsReq) returns (ga.pgc_channel_game_logic.AdventureRandomStepsResp) {
    option (logic.gateway.command) = {
      id: 36360
    };
  }

  // 大冒险控制下一步操作
  rpc AdventureControlNext(ga.pgc_channel_game_logic.AdventureControlNextReq) returns (ga.pgc_channel_game_logic.AdventureControlNextResp) {
    option (logic.gateway.command) = {
      id: 36361
    };
  }

}