syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "pgc_channel_pk_logic/pgc-channel-pk-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/pgc-channel-pk-logic";


service PgcChannelPKLogic {
  option (logic.gateway.service_ext) = {
    service_name: "pgc-channel-pk-logic"
  };

  // 获取PK入口
  rpc GetPgcChannelPKEntry(ga.pgc_channel_pk_logic.GetPgcChannelPKEntryReq) returns (ga.pgc_channel_pk_logic.GetPgcChannelPKEntryResp) {
    option (logic.gateway.command) = {
      id: 36300
    };
  }
  //获取PK房间列表
  rpc GetPgcChannelPKChannelList(ga.pgc_channel_pk_logic.GetPgcChannelPKChannelListReq) returns (ga.pgc_channel_pk_logic.GetPgcChannelPKChannelListResp) {
    option (logic.gateway.command) = {
      id: 36301
    };
  }
  //设置pk开关
  rpc SetPgcChannelPKSwitch(ga.pgc_channel_pk_logic.SetPgcChannelPKSwitchReq) returns (ga.pgc_channel_pk_logic.SetPgcChannelPKSwitchResp) {
    option (logic.gateway.command) = {
      id: 36302
    };
  }
  //发起PK
  rpc StartPgcChannelPK(ga.pgc_channel_pk_logic.StartPgcChannelPKReq) returns (ga.pgc_channel_pk_logic.StartPgcChannelPKResp) {
    option (logic.gateway.command) = {
      id: 36303
    };
  }
  //处理收到PK邀请
  rpc AcceptPgcChannelPK(ga.pgc_channel_pk_logic.AcceptPgcChannelPKReq) returns (ga.pgc_channel_pk_logic.AcceptPgcChannelPKResp) {
    option (logic.gateway.command) = {
      id: 36304
    };
  }
  // 获取房间PK信息
  rpc GetPgcChannelPKInfo(ga.pgc_channel_pk_logic.GetPgcChannelPKInfoReq) returns (ga.pgc_channel_pk_logic.GetPgcChannelPKInfoResp) {
    option (logic.gateway.command) = {
      id: 36305
    };
  }
  //语音流变化上报
  rpc PgcChannelPKReportClientIDChange(ga.pgc_channel_pk_logic.PgcChannelPKReportClientIDChangeReq) returns (ga.pgc_channel_pk_logic.PgcChannelPKReportClientIDChangeResp) {
    option (logic.gateway.command) = {
      id: 36306
    };
  }
  //语音流变化上报
  rpc SetPgcChannelPKOpponentMicFlag(ga.pgc_channel_pk_logic.SetPgcChannelPKOpponentMicFlagReq) returns (ga.pgc_channel_pk_logic.SetPgcChannelPKOpponentMicFlagResp) {
    option (logic.gateway.command) = {
      id: 36307
    };
  }

  //获取自己在当前PK中送给to_uid的送礼值
  rpc GetPgcChannelPKSendGiftScore(ga.pgc_channel_pk_logic.GetPgcChannelPKSendGiftScoreReq) returns (ga.pgc_channel_pk_logic.GetPgcChannelPKSendGiftScoreResp) {
    option (logic.gateway.command) = {
      id: 36308
    };
  }

  // 获取PK观众火力榜
  rpc GetPgcChannelPKAudienceRank(ga.pgc_channel_pk_logic.GetPgcChannelPKAudienceRankReq) returns (ga.pgc_channel_pk_logic.GetPgcChannelPKAudienceRankResp) {
    option (logic.gateway.command) = {
      id: 36309
    };
  }

  
   //选择互动玩家
  rpc ChoseInteraction(ga.pgc_channel_pk_logic.ChoseInteractionReq) returns (ga.pgc_channel_pk_logic.ChoseInteractionResp) {
    option (logic.gateway.command) = {
      id: 36310
    };
  }


  //提前结束
  rpc SetPgcChannelPKEnd(ga.pgc_channel_pk_logic.SetPgcChannelPKEndReq) returns (ga.pgc_channel_pk_logic.SetPgcChannelPKEndResp) {
    option (logic.gateway.command) = {
      id: 36311
    };
  }

}