syntax = "proto3";

package logic.pia_logic;

import "pia/pia_.proto";
import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";


option go_package = "golang.52tt.com/protocol/services/logicsvr-go/pia-logic";

service PiaLogic {
    option (logic.gateway.service_ext) = {
        service_name: "pia-logic"
    };

    rpc GetChannelPiaStatus (ga.pia.GetChannelPiaStatusReq) returns (ga.pia.GetChannelPiaStatusResp) {
        option (logic.gateway.command) = {
            id: 33021   // 获取房间Pia戏开启状态
        };
    }

    rpc SetPiaSwitch (ga.pia.SetPiaSwitchReq) returns (ga.pia.SetPiaSwitchResp) {
        option (logic.gateway.command) = {
            id: 33022   // 切换成Pia戏模式
        };
    }

    rpc GetDrama (ga.pia.GetDramaReq) returns (ga.pia.GetDramaResp) {
        option (logic.gateway.command) = {
            id: 33023   // 获取剧本
        };
    }

    rpc GetSearchOptionGroup (ga.pia.GetSearchOptionGroupReq) returns (ga.pia.GetSearchOptionGroupResp) {
        option (logic.gateway.command) = {
            id: 33024   // 获取剧本搜索标签
        };
    }

    rpc GetCurrentPiaInfo (ga.pia.GetCurrentPiaInfoReq) returns (ga.pia.GetCurrentPiaInfoResp) {
        option (logic.gateway.command) = {
            id: 33026   // 获取当前Pia戏信息
        };
    }

    rpc SetPiaPhase (ga.pia.SetPiaPhaseReq) returns (ga.pia.SetPiaPhaseResp) {
        option (logic.gateway.command) = {
            id: 33027   // 获取当前Pia戏信息
        };
    }

    rpc SetPiaProgress (ga.pia.SetPiaProgressReq) returns (ga.pia.SetPiaProgressResp) {
        option (logic.gateway.command) = {
            id: 33028   // 设置Pia戏进度
        };
    }

    rpc GetPlayingChannel (ga.pia.GetPlayingChannelReq) returns (ga.pia.GetPlayingChannelResp) {
        option (logic.gateway.command) = {
            id: 33029   // 获取正在玩的房间
        };
    }

    rpc GetQualityDramaList (ga.pia.GetQualityDramaListReq) returns (ga.pia.GetQualityDramaListResp) {
        option (logic.gateway.command) = {
            id: 33030   // 获取优质剧场列表
        };
    }

    rpc GetPracticeDramaList (ga.pia.GetPracticeDramaListReq) returns (ga.pia.GetPracticeDramaListResp) {
        option (logic.gateway.command) = {
            id: 33031   // 获取排练列表
        };
    }

    rpc SelectDrama (ga.pia.SelectDramaReq) returns (ga.pia.SelectDramaResp) {
        option (logic.gateway.command) = {
            id: 33032   // 选择剧本
        };
    }

    rpc SetBgmInfo (ga.pia.SetBgmInfoReq) returns (ga.pia.SetBgmInfoResp) {
        option (logic.gateway.command) = {
            id: 33033   // 设置bgm信息
        };
    }

    rpc GetBgmInfo (ga.pia.GetBgmInfoReq) returns (ga.pia.GetBgmInfoResp) {
        option (logic.gateway.command) = {
            id: 33034   // 获取排练列表
        };
    }

    rpc SetCompereMic (ga.pia.SetCompereMicReq) returns (ga.pia.SetCompereMicResp) {
        option (logic.gateway.command) = {
            id: 33035   // 设置主持麦
        };
    }

    rpc OrderDrama (ga.pia.OrderDramaReq) returns (ga.pia.OrderDramaResp) {
        option (logic.gateway.command) = {
            id: 33036   // 点本
        };
    }
    rpc GetOrderDramaList (ga.pia.GetOrderDramaListReq) returns (ga.pia.GetOrderDramaListResp) {
        option (logic.gateway.command) = {
            id: 33037   // 获取已点列表
        };
    }
    rpc DeleteOrderDrama (ga.pia.DeleteOrderDramaReq) returns (ga.pia.DeleteOrderDramaResp) {
        option (logic.gateway.command) = {
            id: 33038   // 删除已点记录
        };
    }
    rpc PiaSelectRole (ga.pia.PiaSelectRoleReq) returns (ga.pia.PiaSelectRoleResp) {
        option (logic.gateway.command) = {
            id: 33039   // 选择角色
        };
    }
    rpc PiaCancelSelectRole (ga.pia.PiaCancelSelectRoleReq) returns (ga.pia.PiaCancelSelectRoleResp) {
        option (logic.gateway.command) = {
            id: 33040   // 角色取消选择
        };
    }
    rpc SelectDramaV2 (ga.pia.SelectDramaV2Req) returns (ga.pia.SelectDramaV2Resp) {
        option (logic.gateway.command) = {
            id: 33041   // 选本
        };
    }
    rpc PiaOperateDrama (ga.pia.PiaOperateDramaReq) returns (ga.pia.PiaOperateDramaResp) {
        option (logic.gateway.command) = {
            id: 33042   // 走本操作
        };
    }
    rpc PiaGetDramaStatus (ga.pia.PiaGetDramaStatusReq) returns (ga.pia.PiaGetDramaStatusResp) {
        option (logic.gateway.command) = {
            id: 33043   // 获取当前房间走本详情
        };
    }
    rpc PiaOperateBgm (ga.pia.PiaOperateBgmReq) returns (ga.pia.PiaOperateBgmResp) {
        option (logic.gateway.command) = {
            id: 33044   // BGM操作
        };
    }
    rpc GetSearchOptionGroupV2 (ga.pia.GetSearchOptionGroupV2Req) returns (ga.pia.GetSearchOptionGroupV2Resp) {
        option (logic.gateway.command) = {
            id: 33045   // 获取筛选标签组
        };
    }
    rpc GetDramaList (ga.pia.GetDramaListReq) returns (ga.pia.GetDramaListResp) {
        option (logic.gateway.command) = {
            id: 33046   // 分页获取剧本库列表
        };
    }
    rpc GetDramaDetailById (ga.pia.GetDramaDetailByIdReq) returns (ga.pia.GetDramaDetailByIdResp) {
        option (logic.gateway.command) = {
            id: 33047   // 剧本详情
        };
    }
    rpc PiaGetDramaCopyId (ga.pia.PiaGetDramaCopyIdReq) returns (ga.pia.PiaGetDramaCopyIdResp) {
        option (logic.gateway.command) = {
            id: 33048 // 获取剧本副本id
        };
    }
    rpc PiaCreateDramaCopy (ga.pia.PiaCreateDramaCopyReq) returns (ga.pia.PiaCreateDramaCopyResp) {
        option (logic.gateway.command) = {
            id: 33049 // 生成剧本副本
        };
    }
    rpc PiaOperateBgmVol (ga.pia.PiaOperateBgmVolReq) returns (ga.pia.PiaOperateBgmVolResp) {
        option (logic.gateway.command) = {
            id: 33050 // 生成剧本副本
        };
    }
    rpc DoUserDramaCollect (ga.pia.DoUserDramaCollectReq) returns (ga.pia.DoUserDramaCollectResp) {
        option (logic.gateway.command) = {
            id: 33051 // 收藏/取消收藏剧本
        };
    }
    rpc GetUserDramaCollection (ga.pia.GetUserDramaCollectionReq) returns (ga.pia.GetUserDramaCollectionResp) {
        option (logic.gateway.command) = {
            id: 33052 // 获取用户搜藏列表
        };
    }
    rpc GetPlayingChannelV2 (ga.pia.GetPlayingChannelV2Req) returns (ga.pia.GetPlayingChannelV2Resp) {
        option (logic.gateway.command) = {
            id: 33053 // 获取在玩房间
        };
    }
    rpc PiaChangePlayType (ga.pia.PiaChangePlayTypeReq) returns (ga.pia.PiaChangePlayTypeResp) {
        option (logic.gateway.command) = {
            id: 33054 // 切换走本方式
        };
    };
    rpc GetMyDramaPlayingRecord (ga.pia.GetMyDramaPlayingRecordReq) returns (ga.pia.GetMyDramaPlayingRecordResp) {
        option (logic.gateway.command) = {
            id: 33055 // 获取我的参演记录列表
        };
    }
    rpc PiaBatchDeleteMyPlayingRecord (ga.pia.PiaBatchDeleteMyPlayingRecordReq) returns (ga.pia.PiaBatchDeleteMyPlayingRecordResp) {
        option (logic.gateway.command) = {
            id: 33056 // 批量删除我的参演记录
        };
    }
    rpc PiaGetRankingList (ga.pia.PiaGetRankingListReq) returns (ga.pia.PiaGetRankingListResp) {
        option (logic.gateway.command) = {
            id: 33057 // 获取排行榜剧本列表
        };
    }
    rpc PiaGetMyPlayingRecordIdList (ga.pia.PiaGetMyPlayingRecordIdListReq) returns (ga.pia.PiaGetMyPlayingRecordIdListResp) {
        option (logic.gateway.command) = {
            id: 33058 // 根据筛选条件获取所有相关参演记录id列表
        };
    }
    rpc GetMyDramaCopyList (ga.pia.GetMyDramaCopyListReq) returns (ga.pia.GetMyDramaCopyListResp) {
        option (logic.gateway.command) = {
            id: 33059 // 我的副本库列表
        };
    }
    rpc PiaCopyDramaList (ga.pia.PiaCopyDramaListReq) returns (ga.pia.PiaCopyDramaListResp){
        option (logic.gateway.command) = {
            id: 33060 // 具体某个副本的副本库列表
        };
    }
    rpc PiaConfirmCoverCopy(ga.pia.PiaConfirmCoverCopyDramaReq)returns (ga.pia.PiaConfirmCoverCopyDramaResp){
        option (logic.gateway.command) = {
            id: 33061 // 确认保存副本
        };
    }
    rpc SetDramaCopyStatus(ga.pia.SetDramaCopyStatusReq)returns (ga.pia.SetDramaCopyStatusResp){
        option (logic.gateway.command) = {
            id: 33062 // 删除副本
        };
    }
    rpc DeleteDramaCopy(ga.pia.DeleteDramaCopyReq)returns (ga.pia.DeleteDramaCopyResp){
        option (logic.gateway.command) = {
            id: 33063 // 判断是否能保存副本
        };
    }
    rpc PiaCreateDramaCopyV2(ga.pia.PiaCreateDramaCopyV2Req)returns (ga.pia.PiaCreateDramaCopyV2Resp){
        option (logic.gateway.command) = {
            id: 33064 // 生成副本V2
        };
    }
    rpc PiaPerformDrama(ga.pia.PiaPerformDramaRequest)returns (ga.pia.PiaPerformDramaResponse){
        option (logic.gateway.command) = {
            id: 33065 // 选择新本演绎
        };
    }

    rpc PiaSendDialogueIndex(ga.pia.PiaSendDialogueIndexRequest)returns (ga.pia.PiaSendDialogueIndexResponse){
        option (logic.gateway.command) = {
            id: 33066 // 发送台词定位
        };
    }

    rpc PiaFollowMic(ga.pia.PiaFollowMicRequest)returns (ga.pia.PiaFollowMicResponse){
        option (logic.gateway.command) = {
            id: 33067 // 跟随麦位
        };
    }

    rpc PiaReportDialogueIndex(ga.pia.PiaReportDialogueIndexRequest)returns (ga.pia.PiaReportDialogueIndexResponse){
        option (logic.gateway.command) = {
            id: 33068 // 上报走本段落
        };
    }

    rpc PiaGetPreviousDialogueIndex(ga.pia.PiaGetPreviousDialogueIndexRequest)returns (ga.pia.PiaGetPreviousDialogueIndexResponse){
        option (logic.gateway.command) = {
            id: 33069 // 获取上个用户的段落记录
        };
    }

    rpc PiaUnFollowMic(ga.pia.PiaUnFollowMicRequest)returns (ga.pia.PiaUnFollowMicResponse){
        option (logic.gateway.command) = {
            id: 33070 // 取消跟随
        };
    }

    rpc PiaGetMyFollowInfo(ga.pia.PiaGetMyFollowInfoRequest) returns (ga.pia.PiaGetMyFollowInfoResponse) {
        option (logic.gateway.command) = {
            id: 33071 // 获取我的跟随状态
        };
    }

    rpc PiaGetFollowedStatusOfMicList(ga.pia.PiaGetFollowedStatusOfMicListRequest) returns (ga.pia.PiaGetFollowedStatusOfMicListResponse) {
        option (logic.gateway.command) = {
            id: 33072 // 获取当前房间各个麦位的跟随状态
        };
    }
}