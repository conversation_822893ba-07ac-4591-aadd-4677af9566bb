syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "playerlogic/playerlogic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/playerlogic";

service PlayerLogic {
    option (logic.gateway.service_ext) = {
      service_name: "playerlogic"
    };

    rpc GetPlayer (ga.playerlogic.PlayerReq) returns (ga.playerlogic.PlayerResp) {
            option (logic.gateway.command) = {
                id: 30140
            };
    }

    rpc PlayerProvided (ga.playerlogic.PlayerProvidedReq) returns (ga.playerlogic.PlayerProvidedRsp) {
      option (logic.gateway.command) = {
        id: 30141
      };
    }

    rpc StrangerCard (ga.playerlogic.StrangerCardReq) returns (ga.playerlogic.StrangerCardResp) {
      option (logic.gateway.command) = {
        id: 30142
      };
    }

    rpc GetUserPlayerFoundSetting (ga.playerlogic.GetPlayerFoundSettingReq) returns (ga.playerlogic.GetPlayerFoundSettingResp) {
      option (logic.gateway.command) = {
        id: 30143
      };
    }

    rpc UpdateUserPlayerFoundSetting (ga.playerlogic.UpdatePlayerFoundSettingReq) returns (ga.playerlogic.UpdatePlayerFoundSettingResp) {
      option (logic.gateway.command) = {
        id: 30144
      };
    }

    rpc GetPosts (ga.playerlogic.PostsReq) returns (ga.playerlogic.PostsRsp) {
          option (logic.gateway.command) = {
            id: 30146
          };
     }

     rpc GetNewPosts (ga.playerlogic.NewPostsReq) returns (ga.playerlogic.NewPostsRsp) {
           option (logic.gateway.command) = {
             id: 30147
           };
      }

    rpc ClosePopUp (ga.playerlogic.ClosePopUpReq) returns (ga.playerlogic.ClosePopUpResp) {
      option (logic.gateway.command) = {
        id: 30139
      };
    }

}

