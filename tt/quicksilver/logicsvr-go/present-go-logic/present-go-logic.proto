syntax = "proto3";

package logic.present_go_logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "present_go_logic/present-go-logic_.proto";
import "redpacket/redpacket_.proto";
import "userpresent/userpresent_.proto";
import "time_present/time_present.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/present-go-logic";

service PresentGoLogic {
  option (logic.gateway.service_ext) = {
    service_name: "present-go-logic"
  };

  rpc GetNeedPopUpPresentList (ga.present_go_logic.GetNeedPopUpPresentListReq) returns (ga.present_go_logic.GetNeedPopUpPresentListResp) {
    option (logic.gateway.command) = {
      id: 1174   //改成正确的id
    };
  }

  rpc GetUserActPresentArea (ga.present_go_logic.GetUserActPresentAreaReq) returns (ga.present_go_logic.GetUserActPresentAreaResp) {
    option (logic.gateway.command) = {
      id: 1190   //改成正确的id
    };
  }

  rpc GetPresentExtraConfig (ga.present_go_logic.GetPresentExtraConfigReq) returns (ga.present_go_logic.GetPresentExtraConfigResp) {
    option (logic.gateway.command) = {
      id: 1191   //改成正确的id
    };
  }

  rpc GetPresentEffectTimeDetail (ga.present_go_logic.GetPresentEffectTimeDetailReq) returns (ga.present_go_logic.GetPresentEffectTimeDetailResp) {
    option (logic.gateway.command) = {
      id: 1192   //改成正确的id
    };
  }

  rpc ChannelPresentSend (ga.userpresent.SendPresentReq) returns (ga.userpresent.SendPresentResp) {
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 1165   //改成正确的id
      deprecated: true
    };
  }

  rpc PresentBatchSend (ga.userpresent.BatchSendPresentReq) returns (ga.userpresent.BatchSendPresentResp) {
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 1170   //改成正确的id
      deprecated: true
    };
  }

  rpc GetCustomizedPresentList (ga.present_go_logic.GetCustomizedPresentListReq) returns (ga.present_go_logic.GetCustomizedPresentListResp) {
    option (logic.gateway.command) = {
      id: 36200   //改成正确的id
    };
  }

  rpc GetCustomizedPresentDetail (ga.present_go_logic.GetCustomizedPresentDetailReq) returns (ga.present_go_logic.GetCustomizedPresentDetailResp) {
    option (logic.gateway.command) = {
      id: 36201  //改成正确的id
    };
  }

  rpc ReportCustomOptionChoose (ga.present_go_logic.ReportCustomOptionChooseReq) returns (ga.present_go_logic.ReportCustomOptionChooseResp) {
    option (logic.gateway.command) = {
      id: 36202   //改成正确的id
    };
  }

  rpc GetUserPresentInfo (ga.userpresent.GetUserPresentInfoReq) returns (ga.userpresent.GetUserPresentInfoResp) {
    option (logic.gateway.command) = {
      id: 1166   //改成正确的id
      deprecated: true
    };
  }

  
  rpc GetPresentConfigList (ga.userpresent.GetPresentConfigListReq) returns (ga.userpresent.GetPresentConfigListResp) {
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 1167   //改成正确的id
      deprecated: true
    };
  }

  rpc GetPresentConfigById (ga.userpresent.GetPresentConfigByIdReq) returns (ga.userpresent.GetPresentConfigByIdResp) {
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 1168   //改成正确的id
      deprecated: true
    };
  }

  rpc GetUserPresentDetailList(ga.userpresent.GetUserPresentDetailListReq) returns (ga.userpresent.GetUserPresentDetailListResp){
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 1169   //改成正确的id
      deprecated: true
    };
  }

  rpc PresentSendByIM (ga.userpresent.IMSendPresentReq) returns (ga.userpresent.IMSendPresentResp) {
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 1171   //改成正确的id
      deprecated: true
    };
  }

  rpc GetNamingPresentConfigList (ga.userpresent.GetNamingPresentConfigListReq) returns (ga.userpresent.GetNamingPresentConfigListResp) {
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 1172   //改成正确的id
      deprecated: true
    };
  }

  rpc GetPresentDynamicTemplateConfig (ga.userpresent.GetPresentDynamicTemplateConfigReq) returns (ga.userpresent.GetPresentDynamicTemplateConfigResp) {
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 1173   //改成正确的id
      deprecated: true
    };
  }

  rpc PresentGetFlowConfigList  (ga.userpresent.GetPresentFlowConfigListReq) returns (ga.userpresent.GetPresentFlowConfigListResp) {
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 1175   //改成正确的id
      deprecated: true
    };
  }

  rpc GetPresentFlowConfigById (ga.userpresent.GetPresentFlowConfigByIdReq) returns (ga.userpresent.GetPresentFlowConfigByIdResp) {
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 1176   //改成正确的id
      deprecated: true
    };
  }

  rpc GetDrawPresentPara (ga.userpresent.GetDrawPresentParaReq) returns (ga.userpresent.GetDrawPresentParaResp) {
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 1177   //改成正确的id
      deprecated: true
    };
  }

  rpc GetImPresentItemIdList (ga.userpresent.GetImPresentItemIdListReq) returns (ga.userpresent.GetImPresentItemIdListResp) {
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 1178   //改成正确的id
      deprecated: true
    };
  }

  rpc GetStangerImItemIdList (ga.userpresent.GetStangerImItemIdListReq) returns (ga.userpresent.GetStangerImItemIdListResp){
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 1179   //改成正确的id
      deprecated: true
    };

  }

  rpc GetFriendGift (ga.redpacket.GetFriendGiftReq) returns (ga.redpacket.GetFriendGiftResp){
    //    暂时通过c++转到go
    option (logic.gateway.command) = {
      id: 2802   //改成正确的id
      deprecated: true
    };
  }

  rpc UnpackPresentBox (ga.userpresent.UnpackPresentBoxReq) returns (ga.userpresent.UnpackPresentBoxResp) {
    option (logic.gateway.command) = {
      id: 1193   //改成正确的id
    };
  }

  rpc CommonSendPresent (ga.present_go_logic.CommonSendPresentReq) returns (ga.present_go_logic.CommonSendPresentResp) {
    option (logic.gateway.command) = {
      id: 1194   //改成正确的id
    };
  }

  rpc PresentConfigSync (ga.present_go_logic.PresentConfigSyncReq) returns (ga.present_go_logic.PresentConfigSyncResp) {
    option (logic.gateway.command) = {
      id: 1195   //改成正确的id
    };
  }

  rpc EmperorSetSend (ga.present_go_logic.EmperorSetSendReq) returns (ga.present_go_logic.EmperorSetSendResp) {
    option (logic.gateway.command) = {
      id: 1196   //改成正确的id
    };
  }

  rpc GetEmperorSetConfigById  (ga.present_go_logic.GetEmperorSetConfigByIdReq) returns (ga.present_go_logic.GetEmperorSetConfigByIdResp) {
    option (logic.gateway.command) = {
      id: 1197   //改成正确的id
    };
  }

  rpc UnpackEmperorBox  (ga.present_go_logic.UnpackEmperorBoxReq) returns (ga.present_go_logic.UnpackEmperorBoxResp) {
    option (logic.gateway.command) = {
      id: 1198   //改成正确的id
    };
  }

  rpc GetTimePresentList  (ga.time_present.GetTimePresentListReq) returns (ga.time_present.GetTimePresentListResp) {
    option (logic.gateway.command) = {
      id: 1199   //改成正确的id
    };
  }

  rpc GetTimePresentOnShelf  (ga.time_present.GetTimePresentOnShelfReq) returns (ga.time_present.GetTimePresentOnShelfResp) {
    option (logic.gateway.command) = {
      id: 39400   //改成正确的id
    };
  }

  rpc GetChannelLiveIntimatePresentList  (ga.time_present.GetChannelLiveIntimatePresentListReq) returns (ga.time_present.GetChannelLiveIntimatePresentListResp) {
    option (logic.gateway.command) = {
      id: 39401   //改成正确的id
    };
  }

  rpc GetLiveIntimatePresentConfigList  (ga.time_present.GetLiveIntimatePresentConfigListReq) returns (ga.time_present.GetLiveIntimatePresentConfigListResp) {
    option (logic.gateway.command) = {
      id: 39402   //改成正确的id
    };
  }


  rpc GetLiveIntimatePresentOnShelf  (ga.time_present.GetLiveIntimatePresentOnShelfReq) returns (ga.time_present.GetLiveIntimatePresentOnShelfResp) {
    option (logic.gateway.command) = {
      id: 39403   //改成正确的id
    };
  }

  rpc GetPresentSetInfo (ga.present_go_logic.GetPresentSetInfoReq) returns (ga.present_go_logic.GetPresentSetInfoResp) {
    option (logic.gateway.command) = {
      id: 39301   //改成正确的id
    };
  }

  rpc GetPresentSetDetail (ga.present_go_logic.GetPresentSetDetailReq) returns (ga.present_go_logic.GetPresentSetDetailResp) {
    option (logic.gateway.command) = {
      id: 39302   //改成正确的id
    };
  }
}