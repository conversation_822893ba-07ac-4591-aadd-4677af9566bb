syntax = "proto3";

package logic.profile;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "profile/profile_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/profile-logic";

service ProfileLogic {
    option (logic.gateway.service_ext) = {
        service_name: "profile-logic"
    };

    // UserDecorations 用于获取用户的装饰品
    rpc UserDecorations ( ga.profile.UserDecorationsReq ) returns ( ga.profile.UserDecorationsResp ) {
        option (logic.gateway.command) = {
            id: 3401
        };
    }

    // UserCurrDecoration 用于获取用户当前佩戴的装饰品
    rpc UserCurrDecoration ( ga.profile.UserCurrDecorationReq ) returns ( ga.profile.UserCurrDecorationResp ) {
        option (logic.gateway.command) = {
            id: 3402
        };
    }

    // UserAdornDecoration 用于用户佩戴装饰品
    rpc UserAdornDecoration ( ga.profile.UserAdornDecorationReq ) returns ( ga.profile.UserAdornDecorationResp ) {
        option (logic.gateway.command) = {
            id: 3403
        };
    }

    // UserRemoveDecoration 用于用户卸下装饰品
    rpc UserRemoveDecoration ( ga.profile.UserRemoveDecorationReq ) returns ( ga.profile.UserRemoveDecorationResp ) {
        option (logic.gateway.command) = {
            id: 3404
        };
    }

    // ChangeDecorationCustomText 修改自定义文案
    rpc ChangeDecorationCustomText ( ga.profile.ChangeDecorationCustomTextReq  ) returns ( ga.profile.ChangeDecorationCustomTextResp ) {
        option (logic.gateway.command) = {
            id: 3407
        };
    }

    // 点击主播资料卡 个人主页 获取用户考核认证信息
    rpc GetUserExamineCert ( ga.profile.GetUserExamineCertReq ) returns ( ga.profile.GetUserExamineCertResp ) {
        option (logic.gateway.command) = {
            id: 32005
        };
    }

    // 批量获取用户考核认证信息
    rpc BatchGetUserExamineCert ( ga.profile.BatchGetUserExamineCertReq ) returns ( ga.profile.BatchGetUserExamineCertResp ) {
        option (logic.gateway.command) = {
            id: 32007
        };
    }
}
