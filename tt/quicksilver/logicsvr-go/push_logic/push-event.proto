syntax = "proto3";


package logic.push;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "push/push_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/push-logic";

service PushEvent {
    option (logic.gateway.service_ext) = {
        service_name: "push-logic"
    };

    // 推送消息回调
    rpc AckNotification (ga.push.AckNotificationReq) returns (ga.push.AckNotificationResp) {
        option (logic.gateway.command) = {
            id: 407
        };
    }
}