syntax = "proto3";

package logic.push;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "auth/auth.proto";
import "push_logic/push-logic_.proto";


option go_package = "golang.52tt.com/protocol/services/logicsvr-go/push-logic";

service PushLogic {
    option (logic.gateway.service_ext) = {
        service_name: "push-logic"
    };

    // 注册token
    rpc RegisterPushDeviceToken (ga.auth.RegisterApnsDeviceTokenReq) returns (ga.auth.RegisterApnsDeviceTokenResp) {
        option (logic.gateway.command) = {
            id: 401
        };
    }

    rpc GetPushToken (ga.push_logic.GetPushTokenReq) returns (ga.push_logic.GetPushTokenResp) {
        option (logic.gateway.command) = {
            id: 900
        };
    }
}



