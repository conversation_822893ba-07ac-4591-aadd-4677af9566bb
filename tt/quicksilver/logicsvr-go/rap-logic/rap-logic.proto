syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "rap_logic/rap-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/rap-logic";

service RapLogic {
  option (logic.gateway.service_ext) = {
    service_name: "rap-logic"
  };

  // 获取说唱玩法的respect值
  rpc RapGetChannelRespectInfo (ga.rap_logic.RapGetChannelRespectInfoReq) returns (ga.rap_logic.RapGetChannelRespectInfoResp) {
    option (logic.gateway.command) = {
      id: 31371
    };
  }

  // 说唱房间respect触发
  rpc RapExpressRespect (ga.rap_logic.RapExpressRespectReq) returns (ga.rap_logic.RapExpressRespectResp) {
    option (logic.gateway.command) = {
      id: 31372
    };
  }

  // 获取房间当日点赞数接口
  rpc MuseChannelGetLikeCount (ga.rap_logic.MuseChannelGetLikeCountReq) returns (ga.rap_logic.MuseChannelGetLikeCountResp) {
    option (logic.gateway.command) = {
      id: 31373
    };
  }

  // 点赞接口
  rpc MuseChannelAddLike (ga.rap_logic.MuseChannelAddLikeReq) returns (ga.rap_logic.MuseChannelAddLikeResp) {
    option (logic.gateway.command) = {
      id: 31374
    };
  }

  // 历史总数+规则说明接口
  rpc MuseChannelGetLikeInfo (ga.rap_logic.MuseChannelGetLikeInfoReq) returns (ga.rap_logic.MuseChannelGetLikeInfoResp) {
    option (logic.gateway.command) = {
      id: 31375
    };
  }

  // 房间互动功能开关接口
  rpc MuseChannelGetInteraction (ga.rap_logic.MuseChannelGetInteractionReq) returns (ga.rap_logic.MuseChannelGetInteractionResp) {
    option (logic.gateway.command) = {
      id: 31370
    };
  }

  // 房间公屏快捷消息
  rpc GetChannelScreenQuickMsg (ga.rap_logic.GetChannelScreenQuickMsgRequest) returns (ga.rap_logic.GetChannelScreenQuickMsgResponse) {
    option (logic.gateway.command) = {
      id: 31570
    };
  }

}