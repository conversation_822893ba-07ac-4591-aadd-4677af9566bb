syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "auth/auth.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/realnameauth-go-logic";


service RealNameAuthGoLogic {
  option (logic.gateway.service_ext) = {
    service_name: "realnameauth-go-logic"
  };

  rpc GetRealNameAuthWarningRule (ga.auth.GetRealNameAuthWarningRuleReq) returns (ga.auth.GetRealNameAuthWarningRuleResp) {
    option (logic.gateway.command) = {
      id: 30641
    };
  }
  
  rpc GetUserRechargeFrontInfo (ga.auth.GetUserRechargeFrontInfoReq) returns (ga.auth.GetUserRechargeFrontInfoResp) {
    option (logic.gateway.command) = {
      id: 30651
    };
  }

  rpc GetRealNameAuthStateV2 (ga.auth.GetRealNameAuthStateV2Req) returns (ga.auth.GetRealNameAuthStateV2Resp) {
    option (logic.gateway.command) = {
      id: 2605
    };
  }

  rpc GetUserIdentityInfo(ga.auth.GetUserIdentityInfoReq) returns (ga.auth.GetUserIdentityInfoResp) {
    option (logic.gateway.command) = {
      id: 2601
    };
  }

  rpc AuthByTwoElement(ga.auth.AuthByTwoElementReq) returns (ga.auth.AuthByTwoElementResp) {
    option (logic.gateway.command) = {
      id: 229
    };
  }

  rpc CheckUserIdentityInfo(ga.auth.CheckUserIdentityInfoReq) returns (ga.auth.CheckUserIdentityInfoResp) {
    option (logic.gateway.command) = {
      id: 224
    };
  }

  rpc GetRealNameAuthState(ga.auth.GetRealNameAuthStateReq) returns (ga.auth.GetRealNameAuthStateResp) {
    option (logic.gateway.command) = {
      id: 220
    };
  }
}