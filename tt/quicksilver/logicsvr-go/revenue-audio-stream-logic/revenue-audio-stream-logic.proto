syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "revenue_audio_stream_logic/revenue_audio_stream_logic.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/revenue-audio-stream-logic";

service RevenueAudioStreamLogic {
  option (logic.gateway.service_ext) = {
    service_name: "revenue-audio-stream-logic"
  };

  // 上报语音流
  rpc ReportAudioStream(ga.revenue_audio_stream_logic.ReportAudioStreamReq) returns (ga.revenue_audio_stream_logic.ReportAudioStreamResp) {
    option (logic.gateway.command) = {
      id: 36951
    };
  }

  // 进房拉取连麦信息
  rpc GetAudioStreamInfo(ga.revenue_audio_stream_logic.GetAudioStreamInfoReq) returns (ga.revenue_audio_stream_logic.GetAudioStreamInfoResp) {
    option (logic.gateway.command) = {
      id: 36952
    };
  }

  // 设置闭麦状态
  rpc SetBlankingStreamStatus(ga.revenue_audio_stream_logic.SetBlankingStreamStatusReq) returns (ga.revenue_audio_stream_logic.SetBlankingStreamStatusResp) {
    option (logic.gateway.command) = {
      id: 36953
    };
  }

}