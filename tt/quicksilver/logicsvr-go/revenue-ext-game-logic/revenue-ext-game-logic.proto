syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "revenue_ext_game_logic/revenue_ext_game_logic.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/revenue-ext-game-logic";

service RevenueExtGameLogic {
  option (logic.gateway.service_ext) = {
    service_name: "revenue-ext-game-logic"
  };

  // 挂载游戏
  rpc MountExtGame(ga.revenue_ext_game_logic.MountExtGameRequest) returns (ga.revenue_ext_game_logic.MountExtGameResponse) {
    option (logic.gateway.command) = {
      id: 36591
    };
  }

  // 取消挂载
  rpc UnmountExtGame(ga.revenue_ext_game_logic.UnmountExtGameRequest) returns (ga.revenue_ext_game_logic.UnmountExtGameResponse) {
    option (logic.gateway.command) = {
      id: 36592
    };
  }

  // 获取游戏配置列表
  rpc GetExtGameCfgList(ga.revenue_ext_game_logic.GetExtGameCfgListRequest) returns (ga.revenue_ext_game_logic.GetExtGameCfgListResponse) {
    option (logic.gateway.command) = {
      id: 36593
    };
  }

  // 用户点击“我想玩”上报
  rpc ReportUserWantPlay(ga.revenue_ext_game_logic.ReportUserWantPlayRequest) returns (ga.revenue_ext_game_logic.ReportUserWantPlayResponse) {
    option (logic.gateway.command) = {
      id: 36594
    };
  }

  // 获取用户游戏信息
  rpc GetUserExtGameInfo(ga.revenue_ext_game_logic.GetUserExtGameInfoRequest) returns (ga.revenue_ext_game_logic.GetUserExtGameInfoResponse) {
    option (logic.gateway.command) = {
      id: 36595
    };
  }

  // 获取房间外部游戏入口信息
  rpc GetChannelExtGameAccess(ga.revenue_ext_game_logic.GetChannelExtGameAccessRequest) returns (ga.revenue_ext_game_logic.GetChannelExtGameAccessResponse) {
    option (logic.gateway.command) = {
      id: 36596
    };
  }

  // 获取游戏积分榜单
  rpc GetExtGameScoreRank(ga.revenue_ext_game_logic.GetExtGameScoreRankRequest) returns (ga.revenue_ext_game_logic.GetExtGameScoreRankResponse) {
    option (logic.gateway.command) = {
      id: 36597
    };
  }

  // 获取游戏积分榜单标识
  rpc GetExtGameRankNameplate(ga.revenue_ext_game_logic.GetExtGameRankNameplateRequest) returns (ga.revenue_ext_game_logic.GetExtGameRankNameplateResponse) {
    option (logic.gateway.command) = {
      id: 36598
    };
  }

}