syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "revenuenameplatelogic/revenue-nameplate-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/revenuenameplatelogic";

service RevenueNameplateLogic {
  option (logic.gateway.service_ext) = {
    service_name: "revenue-nameplate-logic"
  };

  // 获取用户铭牌配置
  rpc GetUserNameplateInfo (ga.revenuenameplatelogic.GetUserNameplateInfoReq) returns (ga.revenuenameplatelogic.GetUserNameplateInfoResp) {
    option (logic.gateway.command) = {
      id: 3861
    };
  }

  // 设置用户铭牌配置
  rpc SetUserNameplateInfo (ga.revenuenameplatelogic.SetUserNameplateInfoReq) returns (ga.revenuenameplatelogic.SetUserNameplateInfoResp) {
    option (logic.gateway.command) = {
      id: 3862
    };
  }

  // 获取用户所有可配置铭牌
  rpc GetUserAllNameplateList (ga.revenuenameplatelogic.GetUserAllNameplateListReq) returns (ga.revenuenameplatelogic.GetUserAllNameplateListResp) {
    option (logic.gateway.command) = {
      id: 3863
    };
  }

}