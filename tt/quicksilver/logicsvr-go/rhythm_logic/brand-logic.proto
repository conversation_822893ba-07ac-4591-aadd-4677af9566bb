syntax = "proto3";

package logic.rhythm;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "rhythm/rhythm_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/rhythm_logic";

service BrandLogic {
  option (logic.gateway.service_ext) = {
    service_name: "rhythm-logic"
  };

  // 获取厂牌列表
  rpc GetBrandList (ga.rhythm.GetBrandListReq) returns (ga.rhythm.GetBrandListResp) {
    option (logic.gateway.command) = {
      id: 31333
    };
  }

  // 获取厂牌详情
  rpc GetBrandInfo (ga.rhythm.GetBrandInfoReq) returns (ga.rhythm.GetBrandInfoResp) {
    option (logic.gateway.command) = {
      id: 31334
    };
  }

  // 修改厂牌详情
  rpc UpdateBrandInfo (ga.rhythm.UpdateBrandInfoReq) returns (ga.rhythm.UpdateBrandInfoResp) {
    option (logic.gateway.command) = {
      id: 31335
    };
  }

  // 是否开启厂牌专属房间背景及话题入口
  rpc GetBrandChannelBGTopicInfo (ga.rhythm.GetBrandChannelBGTopicInfoReq) returns (ga.rhythm.GetBrandChannelBGTopicInfoResp) {
    option (logic.gateway.command) = {
      id: 31360
    };
  }

  /* 用户厂牌 */
  rpc GetUserBrandInfo (ga.rhythm.GetUserBrandInfoReq) returns (ga.rhythm.GetUserBrandInfoResp) {
    option (logic.gateway.command) = {
      id: 31361
    };
  }


}

