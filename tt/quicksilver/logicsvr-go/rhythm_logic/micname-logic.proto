syntax = "proto3";

package logic.rhythm;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "rhythm/rhythm_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/rhythm_logic";

service MicNameLogic {
  option (logic.gateway.service_ext) = {
    service_name: "rhythm-logic"
  };
  //开启或关闭麦位名称功能
  rpc SwitchMicNameFunc(ga.rhythm.SwitchMicNameFuncReq)returns(ga.rhythm.SwitchMicNameFuncResp){
    option (logic.gateway.command) = {
      id: 31362
    };
  }
  //开启或关闭麦位名称功能
  rpc GetMicName(ga.rhythm.GetMicNameReq)returns(ga.rhythm.GetMicNameResp){
    option (logic.gateway.command) = {
      id: 31363
    };
  }
  //开启或关闭麦位名称功能
  rpc SetMicName(ga.rhythm.SetMicNameReq)returns(ga.rhythm.SetMicNameResp){
    option (logic.gateway.command) = {
      id: 31364
    };
  }


}

