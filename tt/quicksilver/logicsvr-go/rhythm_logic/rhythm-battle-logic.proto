syntax = "proto3";

package logic.rhythm;


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "rhythm/rhythm_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/rhythm_logic";

service RhythmBattleLogic {
  option (logic.gateway.service_ext) = {
    service_name: "rhythm-logic"
  };
  /* 开启battle */
  rpc SetBattleStart(ga.rhythm.SetBattleStartRequest)returns(ga.rhythm.SetBattleStartResponse){
    option (logic.gateway.command) = {
      id: 31702
    };
  }
  /* 关闭battle */
  rpc CancelBattleStart(ga.rhythm.CancelBattleStartRequest)returns(ga.rhythm.CancelBattleStartResponse){
    option (logic.gateway.command) = {
      id: 31703
    };
  }
  /* 获取battle */
  rpc GetBattleStart(ga.rhythm.GetBattleStartRequest)returns(ga.rhythm.GetBattleStartResponse){
    option (logic.gateway.command) = {
      id: 31704
    };
  }


}

