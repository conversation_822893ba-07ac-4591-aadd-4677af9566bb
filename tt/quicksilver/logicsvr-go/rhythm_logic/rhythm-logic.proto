syntax = "proto3";

package logic.rhythm;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "rhythm/rhythm_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/rhythm_logic";

service CollectionLogic {
  option (logic.gateway.service_ext) = {
    service_name: "rhythm-logic"
  };
    rpc GetRhythmSwitch (ga.rhythm.GetRhythmSwitchReq) returns (ga.rhythm.GetRhythmSwitchResp) {
            option (logic.gateway.command) = {
                id: 31329
            };
    }

  rpc SetRhythmSwitch (ga.rhythm.SetRhythmSwitchReq) returns (ga.rhythm.SetRhythmSwitchResp) {
    option (logic.gateway.command) = {
      id: 31330
    };
  }

  // 更新通用相册
  rpc UpdateCommonPhotoAlbum (ga.rhythm.UpdateCommonPhotoAlbumReq) returns (ga.rhythm.UpdateCommonPhotoAlbumResp) {
    option (logic.gateway.command) = {
      id: 31332
    };
  }
  rpc GetMusicZoneTemplate(ga.rhythm.GetMusicZoneTemplateReq) returns (ga.rhythm.GetMusicZoneTemplateResp){
    option (logic.gateway.command) = {
      id: 31336
    };
  }
  //开始投票
  rpc UserDefinedVotePKStart(ga.rhythm.UserDefinedVotePKStartReq) returns (ga.rhythm.UserDefinedVotePKStartResp){
    option (logic.gateway.command) = {
      id: 31337
    };
  }
  //获取全量信息
  rpc GetUserDefinedVotePK(ga.rhythm.GetUserDefinedVotePKReq) returns (ga.rhythm.GetUserDefinedVotePKResp){
    option (logic.gateway.command) = {
      id: 31338
    };
  }
  //投票
  rpc UserDefinedVote(ga.rhythm.UserDefinedVoteReq) returns (ga.rhythm.UserDefinedVoteResp){
    option (logic.gateway.command) = {
      id: 31339
    };
  }
  //关闭投票
  rpc UserDefinedVotePKCancel(ga.rhythm.UserDefinedVotePKCancelReq) returns (ga.rhythm.UserDefinedVotePKCancelResp){
    option (logic.gateway.command) = {
      id: 31340
    };
  }

  /* 设置声音水印 */
  rpc SetVoiceWatermark(ga.rhythm.SetVoiceWatermarkReq) returns (ga.rhythm.SetVoiceWatermarkResp){
    option (logic.gateway.command) = {
      id: 31344
    };
  }
  /* 获取声音水印 */
  rpc GetVoiceWatermark(ga.rhythm.GetVoiceWatermarkReq) returns (ga.rhythm.GetVoiceWatermarkResp){
    option (logic.gateway.command) = {
      id: 31345
    };
  }
  /* 删除声音水印 */
  rpc DelVoiceWatermark(ga.rhythm.DelVoiceWatermarkReq) returns (ga.rhythm.DelVoiceWatermarkResp){
    option (logic.gateway.command) = {
      id: 31346
    };
  }
  /* 读完的音频 */
  rpc SendReadVoice(ga.rhythm.SendReadVoiceReq) returns (ga.rhythm.SendReadVoiceResp){
    option (logic.gateway.command) = {
      id: 31347
    };
  }
  /*获取Tab信息*/
  rpc GetTabInfo(ga.rhythm.GetTabInfoReq)returns (ga.rhythm.GetTabInfoResp){
    option (logic.gateway.command) = {
      id: 31350
    };
  }
  /*根据tab_id获取批量歌词信息*/
  rpc GetLyricInfo(ga.rhythm.GetLyricInfoReq)returns (ga.rhythm.GetLyricInfoResp){
    option (logic.gateway.command) = {
      id: 31351
    };
  }

  /*用户发帖后获取等级提升*/
  rpc PostAIRapperPost(ga.rhythm.PostAIRapperPostReq)returns (ga.rhythm.PostAIRapperPostResp){
    option (logic.gateway.command) = {
      id: 31349
    };
  }

  /*获取等级*/
  rpc GetUserAIRapperLevel(ga.rhythm.GetUserAIRapperLevelReq)returns (ga.rhythm.GetUserAIRapperLevelResp){
    option (logic.gateway.command) = {
      id: 31352
    };
  }
  /*合成，曝光，分享的聚合接口*/
  rpc Aggregation(ga.rhythm.AggregationInfoReq)returns (ga.rhythm.AggregationInfoResp){
    option (logic.gateway.command) = {
      id: 31353
    };
  }
  /*获取用户体验小问卷*/
  rpc GetQuestionnaire(ga.rhythm.GetQuestionnaireReq)returns (ga.rhythm.GetQuestionnaireResp){
    option (logic.gateway.command) = {
      id: 31354
    };
  }

  /* 分享朋友圈 */
  rpc GetH5UrlWithAICtx(ga.rhythm.GetH5UrlWithAICtxReq) returns (ga.rhythm.GetH5UrlWithAICtxResp){
    option (logic.gateway.command) = {
      id: 31355
    };
  }
  /* 专区改造 */
  rpc GetMusicBlockFilter(ga.rhythm.GetMusicBlockFilterReq) returns (ga.rhythm.GetMusicBlockFilterResp){
    option (logic.gateway.command) = {
      id: 31356
    };
  }

  /* ai rapper提升用户等级 */
  rpc UpgradeUserAIRapperLevel(ga.rhythm.UpgradeUserAIRapperLevelReq) returns (ga.rhythm.UpgradeUserAIRapperLevelResp){
    option (logic.gateway.command) = {
      id: 31357
    };
  }

/*获取筛选流房间*/
  rpc RhythmGetRecommendRoom(ga.rhythm.RhythmGetRecommendRoomReq) returns (ga.rhythm.RhythmGetRecommendRoomResp){
    option (logic.gateway.command) = {
      id: 31358
    };
  }

  /* 停留加票 6.13.0 */
  rpc ReportStayAddTicket(ga.rhythm.ReportStayAddTicketReq) returns (ga.rhythm.ReportStayAddTicketResp){
    option (logic.gateway.command) = {
      id: 31310
    };
  }
  /*获取讨论贴*/
  rpc RhythmGetPosts(ga.rhythm.RhythmGetPostsReq) returns (ga.rhythm.RhythmGetPostsResp){
    option (logic.gateway.command) = {
      id: 31359
    };
  }
  /*设置麦位排序*/
  rpc SetMicSort(ga.rhythm.SetMicSortReq) returns (ga.rhythm.SetMicSortResp){
    option (logic.gateway.command) = {
      id: 31365
    };
  }
  /*获取麦位排序*/
  rpc GetMicSort(ga.rhythm.GetMicSortReq) returns (ga.rhythm.GetMicSortResp){
    option (logic.gateway.command) = {
      id: 31366
    };
  }
  /*开启或关闭麦位排序*/
  rpc SwitchMicSort(ga.rhythm.SwitchMicSortReq) returns (ga.rhythm.SwitchMicSortResp){
    option (logic.gateway.command) = {
      id: 31367
    };
  }
  /* 赛博世界首页 */
  rpc GetCyberWorldHome(ga.rhythm.GetCyberWorldHomeReq) returns (ga.rhythm.GetCyberWorldHomeResp){
    option (logic.gateway.command) = {
      id: 31376
    };
  }
  rpc GetBackgroundVideoUrl(ga.rhythm.GetBackgroundVideoUrlReq) returns (ga.rhythm.GetBackgroundVideoUrlResp){
    option (logic.gateway.command) = {
      id: 31377
    };
  }

  // 推荐流讨论话题 强插
  rpc GetForceTopic(ga.rhythm.GetForceTopicReq) returns (ga.rhythm.GetForceTopicResp){
    option (logic.gateway.command) = {
      id: 31700
    };
  }


}


