syntax = "proto3";

package logic;

// import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
// import "tt/quicksilver/room-master-apprentice-logic_.proto";
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/roommasterapprenticelogic";

service RoomMasterApprenticeLoigc {
  // 入口信息
  // rpc EntranceInfo (ga.roommasterapprenticelogic.EntranceInfoReq) returns (ga.roommasterapprenticelogic.EntranceInfoResp){
  //   option (logic.gateway.command) = {
  //     id: 30214
  //   };
  // }

  
}