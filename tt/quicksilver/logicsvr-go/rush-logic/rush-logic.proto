syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/rush-logic";


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "rush/rush_.proto";

service RushLogic {
    rpc RushQueue (ga.rush.RushQueueReq) returns (ga.rush.RushQueueResp) {
        option (logic.gateway.command) = {
            id: 418
        };
    }
}