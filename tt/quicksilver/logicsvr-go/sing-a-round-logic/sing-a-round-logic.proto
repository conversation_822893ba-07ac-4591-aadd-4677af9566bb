syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "sing_a_round_logic/sing-a-round-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/singaroundlogic";

service SingARoundLogic {
  option (logic.gateway.service_ext) = {
    service_name: "sing-a-round-logic"
  };

  // 开始匹配
  rpc StartSingingGameMatching (ga.sing_a_round_logic.StartSingingGameMatchingReq) returns (ga.sing_a_round_logic.StartSingingGameMatchingResp) {
    option (logic.gateway.command) = {
      id: 30901
    };
  }

  // 加入游戏（观众加入游戏时使用）
  rpc JoinSingingGame (ga.sing_a_round_logic.JoinSingingGameReq) returns (ga.sing_a_round_logic.JoinSingingGameResp) {
    option (logic.gateway.command) = {
      id: 30902
    };
  }

  // 获取开始倒计时
  rpc GetSingingGameCountdown (ga.sing_a_round_logic.GetSingingGameCountdownReq) returns (ga.sing_a_round_logic.GetSingingGameCountdownResp) {
    option (logic.gateway.command) = {
      id: 30903
    };
  }

  // 准备（开始游戏）
  rpc StartSingingGame (ga.sing_a_round_logic.StartSingingGameReq) returns (ga.sing_a_round_logic.StartSingingGameResp) {
    option (logic.gateway.command) = {
      id: 30904
    };
  }

  // 抢唱
  rpc GrabSingingGameMic (ga.sing_a_round_logic.GrabSingingGameMicReq) returns (ga.sing_a_round_logic.GrabSingingGameMicResp) {
    option (logic.gateway.command) = {
      id: 30905
    };
  }

  // 请求帮唱
  rpc AskForSingingHelp (ga.sing_a_round_logic.AskForSingingHelpReq) returns (ga.sing_a_round_logic.AskForSingingHelpResp) {
    option (logic.gateway.command) = {
      id: 30906
    };
  }

  // 帮唱
  rpc AnswerSingingHelp (ga.sing_a_round_logic.AnswerSingingHelpReq) returns (ga.sing_a_round_logic.AnswerSingingHelpResp) {
    option (logic.gateway.command) = {
      id: 30907
    };
  }

  // 上报积分
  rpc ReportSingingGameSongScore (ga.sing_a_round_logic.ReportSingingGameSongScoreReq) returns (ga.sing_a_round_logic.ReportSingingGameSongScoreResp) {
    option (logic.gateway.command) = {
      id: 30908
    };
  }

  // 用户房间状态（客户端重连时使用）
  rpc GetSingingGameChannelInfo (ga.sing_a_round_logic.GetSingingGameChannelInfoReq) returns (ga.sing_a_round_logic.GetSingingGameChannelInfoResp) {
    option (logic.gateway.command) = {
      id: 30909
    };
  }

  // 牛啊
  rpc ExpressSingingGameLike (ga.sing_a_round_logic.ExpressSingingGameLikeReq) returns (ga.sing_a_round_logic.ExpressSingingGameLikeResp) {
    option (logic.gateway.command) = {
      id: 30910
    };
  }

  // 碰拳
  rpc SingingGameFistBump (ga.sing_a_round_logic.SingingGameFistBumpReq) returns (ga.sing_a_round_logic.SingingGameFistBumpResp) {
    option (logic.gateway.command) = {
      id: 30911
    };
  }

  // 完成演唱
  rpc AccomplishSingingGameSong (ga.sing_a_round_logic.AccomplishSingingGameSongReq) returns (ga.sing_a_round_logic.AccomplishSingingGameSongResp) {
    option (logic.gateway.command) = {
      id: 30912
    };
  }

  // 获取配置
  rpc GetSingAllConf (ga.sing_a_round_logic.GetSingAllConfReq) returns (ga.sing_a_round_logic.GetSingAllConfResp) {
    option (logic.gateway.command) = {
      id: 30913
    };
  }

  // 获取用户形象
  rpc GetUserSingImage (ga.sing_a_round_logic.GetUserSingImageReq) returns (ga.sing_a_round_logic.GetUserSingImageResp) {
    option (logic.gateway.command) = {
      id: 30914
    };
  }

  // 设置用户形象
  rpc SetUserSingImage (ga.sing_a_round_logic.SetUserSingImageReq) returns (ga.sing_a_round_logic.SetUserSingImageResp) {
    option (logic.gateway.command) = {
      id: 30915
    };
  }

  // 获取房间默认背景
  rpc GetSingingGameBg (ga.sing_a_round_logic.GetSingingGameBgReq) returns (ga.sing_a_round_logic.GetSingingGameBgResp) {
    option (logic.gateway.command) = {
      id: 30916
    };
  }

  // 切换游戏模式
  rpc SwitchSingingGameType (ga.sing_a_round_logic.SwitchSingingGameTypeReq) returns (ga.sing_a_round_logic.SwitchSingingGameTypeResp) {
    option (logic.gateway.command) = {
      id: 30917
    };
  }

  // 开始游戏（UGC房）
  rpc StartUgcChannelSingingGame (ga.sing_a_round_logic.StartUgcChannelSingingGameReq) returns (ga.sing_a_round_logic.StartUgcChannelSingingGameResp) {
    option (logic.gateway.command) = {
      id: 30918
    };
  }

  // 取消准备（UGC房）
  rpc CancelSingingGamePreparation (ga.sing_a_round_logic.CancelSingingGamePreparationReq) returns (ga.sing_a_round_logic.CancelSingingGamePreparationResp) {
    option (logic.gateway.command) = {
      id: 30919
    };
  }

  // 获取分区列表
  rpc GetSingingGame (ga.sing_a_round_logic.GetSingingGameReq) returns (ga.sing_a_round_logic.GetSingingGameResp) {
    option (logic.gateway.command) = {
      id: 30920
    };
  }

  // 投票
  rpc SingingGameVote (ga.sing_a_round_logic.SingingGameVoteReq) returns (ga.sing_a_round_logic.SingingGameVoteResp) {
    option (logic.gateway.command) = {
      id: 30921
    };
  }

  // 完成资源预加载
  rpc AccomplishLoadingRes (ga.sing_a_round_logic.AccomplishLoadingResReq) returns (ga.sing_a_round_logic.AccomplishLoadingResResp) {
    option (logic.gateway.command) = {
      id: 30922
    };
  }

  // 获取排行榜
  rpc GetSingingGameRankList (ga.sing_a_round_logic.GetSingingGameRankListReq) returns (ga.sing_a_round_logic.GetSingingGameRankListResp) {
    option (logic.gateway.command) = {
      id: 30923
    };
  }

  // 客户端不认可结果
  rpc NotApproveSingResult (ga.sing_a_round_logic.NotApproveSingResultReq) returns (ga.sing_a_round_logic.NotApproveSingResultResp) {
    option (logic.gateway.command) = {
      id: 30924
    };
  }

  // 获取通关模式游戏记录
  rpc GetSingingGameRecordList (ga.sing_a_round_logic.GetSingingGameRecordListReq) returns (ga.sing_a_round_logic.GetSingingGameRecordListResp) {
    option (logic.gateway.command) = {
      id: 30925
    };
  }

  // 获取接唱个人信息
  rpc GetSingingGameUserInfo (ga.sing_a_round_logic.GetSingingGameUserInfoReq) returns (ga.sing_a_round_logic.GetSingingGameUserInfoResp) {
    option (logic.gateway.command) = {
      id: 30926
    };
  }
}