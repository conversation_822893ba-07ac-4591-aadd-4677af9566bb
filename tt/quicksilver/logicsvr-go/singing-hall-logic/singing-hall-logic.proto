syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "singing_hall_logic/singing-hall-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/singing-hall-logic";

service SingingHallLogic {
  option (logic.gateway.service_ext) = {
    service_name: "singing-hall-logic"
  };

  rpc HasSongMenuPerm(ga.singing_hall_logic.HasSongMenuPermReq) returns (ga.singing_hall_logic.HasSongMenuPermResp) {
    option (logic.gateway.command) = {
      id: 36100
    };
  }
  rpc GetSongMenu(ga.singing_hall_logic.GetSongMenuReq) returns (ga.singing_hall_logic.GetSongMenuResp) {
    option (logic.gateway.command) = {
      id: 36101
    };
  }

  rpc CreateSongMenu(ga.singing_hall_logic.CreateSongMenuReq) returns (ga.singing_hall_logic.CreateSongMenuResp) {
    option (logic.gateway.command) = {
      id: 36102
    };
  }

  rpc UpdateSongMenu(ga.singing_hall_logic.UpdateSongMenuReq) returns (ga.singing_hall_logic.UpdateSongMenuResp) {
    option (logic.gateway.command) = {
      id: 36103
    };
  }

  rpc EditSongMenu(ga.singing_hall_logic.EditSongMenuReq) returns (ga.singing_hall_logic.EditSongMenuResp) {
    option (logic.gateway.command) = {
      id: 36104
    };
  }

  rpc AddSong(ga.singing_hall_logic.AddSongReq) returns (ga.singing_hall_logic.AddSongResp) {
    option (logic.gateway.command) = {
      id: 36106
    };
  }

  rpc GetSongList(ga.singing_hall_logic.GetSongListReq) returns (ga.singing_hall_logic.GetSongListResp) {
    option (logic.gateway.command) = {
      id: 36107
    };
  }

  rpc EditSongList(ga.singing_hall_logic.EditSongListReq) returns (ga.singing_hall_logic.EditSongListResp) {
    option (logic.gateway.command) = {
      id: 36108
    };
  }
}