syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "slipnotelogic/slip-note-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/slipnotelogic";

service SlipNoteLogic {
    option (logic.gateway.service_ext) = {
      service_name: "slip-note-logic"
    };

    rpc GetSlipNoteConfig (ga.slipnotelogic.GetSlipNoteConfigReq) returns (ga.slipnotelogic.GetSlipNoteConfigResp) {
      option (logic.gateway.command) = {
        id: 30581
      };
    }

    rpc PublishSlipNote (ga.slipnotelogic.PublishSlipNoteReq) returns (ga.slipnotelogic.PublishSlipNoteResp) {
      option (logic.gateway.command) = {
        id: 30582
      };
    }

    rpc CloseSlipNote (ga.slipnotelogic.CloseSlipNoteReq) returns (ga.slipnotelogic.CloseSlipNoteResp) {
      option (logic.gateway.command) = {
        id: 30583
      };
    }

    rpc GetSlipNoteStatus (ga.slipnotelogic.GetSlipNoteStatusReq) returns (ga.slipnotelogic.GetSlipNoteStatusResp) {
      option (logic.gateway.command) = {
        id: 30584
      };
    }

    rpc PickSlipNote (ga.slipnotelogic.PickSlipNoteReq) returns (ga.slipnotelogic.PickSlipNoteResp) {
      option (logic.gateway.command) = {
        id: 30585
      };
    }

    rpc SetSlipNote (ga.slipnotelogic.SetSlipNoteReq) returns (ga.slipnotelogic.SetSlipNoteResp) {
      option (logic.gateway.command) = {
        id: 30586
      };
    }

    rpc FetchSlipNote (ga.slipnotelogic.FetchSlipNoteReq) returns (ga.slipnotelogic.FetchSlipNoteResp) {
      option (logic.gateway.command) = {
        id: 30587
      };
    }

    //    rpc GetIMCountDownRecord (ga.slipnotelogic.GetIMCountDownRecordReq) returns (ga.slipnotelogic.GetIMCountDownRecordResp) {
    //      option (logic.gateway.command) = {
    //        id: 30588
    //      };
    //    }

    rpc CommentToSlipNote (ga.slipnotelogic.CommentToSlipNoteReq) returns (ga.slipnotelogic.CommentToSlipNoteResp) {
      option (logic.gateway.command) = {
        id: 30588
      };
    }

    rpc PullSlipNoteCommentList (ga.slipnotelogic.PullSlipNoteCommentListReq) returns (ga.slipnotelogic.PullSlipNoteCommentListResp) {
      option (logic.gateway.command) = {
        id: 30589
      };
    }

    rpc ReportSlipNoteStatus (ga.slipnotelogic.ReportSlipNoteStatusReq) returns (ga.slipnotelogic.ReportSlipNoteStatusResp) {
      option (logic.gateway.command) = {
        id: 30590
      };
    }

    rpc GetAccountsForSlipNote (ga.slipnotelogic.GetAccountsForSlipNoteReq) returns (ga.slipnotelogic.GetAccountsForSlipNoteResp) {
      option (logic.gateway.command) = {
        id: 30591
      };
    }

}