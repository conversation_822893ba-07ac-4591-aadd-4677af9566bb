syntax = "proto3";

package smash_egg_logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "smash_egg/smash-egg-logic_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/smash-egg-logic";

service SmashEggLogic {
    option (logic.gateway.service_ext) = {
        service_name: "smash-egg-logic"
    };

    //获取消费记录
    rpc GetConsumeRecord (ga.smash_egg.SmashEgg_GetConsumeRecordReq) returns (ga.smash_egg.SmashEgg_GetConsumeRecordResp) {
        option (logic.gateway.command) = {
            id: 30030
        };
    }

    //获取全局中奖记录
    rpc GetRecentWinningRecord (ga.smash_egg.SmashEgg_GetRecentWinningRecordReq) returns (ga.smash_egg.SmashEgg_GetRecentWinningRecordResp) {
        option (logic.gateway.command) = {
            id: 30031
        };
    }

    //获取中奖记录
    rpc GetWinningRecord (ga.smash_egg.SmashEgg_GetWinningRecordReq) returns (ga.smash_egg.SmashEgg_GetWinningRecordResp) {
        option (logic.gateway.command) = {
            id: 30032
        };
    }

    //续费麦位框
    rpc Recharge (ga.smash_egg.SmashEgg_RechargeReq) returns (ga.smash_egg.SmashEgg_RechargeResp) {
        option (logic.gateway.command) = {
            id: 30033
        };
    }

    //砸蛋
    rpc Smash (ga.smash_egg.SmashEgg_SmashReq) returns (ga.smash_egg.SmashEgg_SmashResp) {
        option (logic.gateway.command) = {
            id: 30034
        };
    }

    //获取砸蛋状态
    rpc GetSmashStatus(ga.smash_egg.SmashEgg_GetSmashStatusReq) returns (ga.smash_egg.SmashEgg_GetSmashStatusResp){
        option (logic.gateway.command) = {
            id: 30035
        };
    }

    //获取服务配置
    rpc GetConfig(ga.smash_egg.SmashEgg_GetConfigReq) returns (ga.smash_egg.SmashEgg_GetConfigResp){
        option (logic.gateway.command) = {
            id: 30036
        };
    }

  //获取活动主题资源配置
  rpc GetResourceConfig(ga.smash_egg.SmashEgg_GetResourceConfigReq) returns (ga.smash_egg.SmashEgg_GetResourceConfigResp){
    option (logic.gateway.command) = {
      id: 30038
    };
  }

  //获取用户道具列表
  rpc GetUserPropList(ga.smash_egg.GetUserPropListReq) returns (ga.smash_egg.GetUserPropListResp){
    option (logic.gateway.command) = {
      id: 30039
    };
  }

  //获取用户即将过期道具提醒
  rpc GetUserExpirePropNotify(ga.smash_egg.GetUserExpirePropNotifyReq) returns (ga.smash_egg.GetUserExpirePropNotifyResp){
    option (logic.gateway.command) = {
      id: 30040
    };
  }
}
