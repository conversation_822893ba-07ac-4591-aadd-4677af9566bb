syntax = "proto3";

package smash_egg_notify_logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "smash_egg_notify/smash-egg-notify-logic_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/smash-egg-notify-logic";

service SmashEggNotifyLogic {
  option (logic.gateway.service_ext) = {
    service_name: "smash-egg-notify-logic"
  };

  //获取变更通知
  rpc GetNotify (ga.smash_egg_notify.GetNotifyReq) returns (ga.smash_egg_notify.GetNotifyResp) {
    option (logic.gateway.command) = {
      id: 30037
    };
  }
}