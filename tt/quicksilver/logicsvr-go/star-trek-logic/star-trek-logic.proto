syntax = "proto3";

package logic.star_trek_logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "star_trek_logic/star-trek-logic_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/star-trek-logic";

service StarTrekLogic {
  option (logic.gateway.service_ext) = {
    service_name: "star-trek-logic"
  };

  rpc StarTrekEntryAndNotify (ga.star_trek_logic.StarTrekEntryAndNotifyReq) returns (ga.star_trek_logic.StarTrekEntryAndNotifyResp) {
    option (logic.gateway.command) = {
      id: 32141   //改成正确的id
    };
  }

  rpc GetStatTrekInfo (ga.star_trek_logic.GetStatTrekInfoReq) returns (ga.star_trek_logic.GetStatTrekInfoResp) {
    option (logic.gateway.command) = {
      id: 32142   //改成正确的id
    };
  }

  rpc GetSupplyConf (ga.star_trek_logic.GetSupplyConfReq) returns (ga.star_trek_logic.GetSupplyConfResp) {
    option (logic.gateway.command) = {
      id: 32143   //改成正确的id
    };
  }

  rpc DoInvest (ga.star_trek_logic.DoInvestReq) returns (ga.star_trek_logic.DoInvestResp) {
    option (logic.gateway.command) = {
      id: 32144   //改成正确的id
    };
  }

  rpc GetMyTrekRecord (ga.star_trek_logic.GetMyTrekRecordReq) returns (ga.star_trek_logic.GetMyTrekRecordResp) {
    option (logic.gateway.command) = {
      id: 32145   //改成正确的id
    };
  }

  rpc GetAllTrekHistory (ga.star_trek_logic.GetAllTrekHistoryReq) returns (ga.star_trek_logic.GetAllTrekHistoryResp) {
    option (logic.gateway.command) = {
      id: 32146   //改成正确的id
    };
  }

  rpc GetSupplyValueChange (ga.star_trek_logic.GetSupplyValueChangeReq) returns (ga.star_trek_logic.GetSupplyValueChangeResp) {
    option (logic.gateway.command) = {
      id: 32147   //改成正确的id
    };
  }
}