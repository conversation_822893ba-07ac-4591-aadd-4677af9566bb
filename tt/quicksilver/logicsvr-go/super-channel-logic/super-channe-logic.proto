syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/super-channel-logic";

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "super_channel/super-channel_.proto";


service SuperChannelLogic {
    option (logic.gateway.service_ext) = {
        service_name: "super-channel-logic"
    };
    rpc Enter (ga.super_channel.SuperChannelEnterReq) returns (ga.super_channel.SuperChannelEnterResp) {
        option (logic.gateway.command) = {
          id: 100001
        };
    }

    rpc Quit (ga.super_channel.SuperChannelQuitReq) returns (ga.super_channel.SuperChannelQuitResp) {
        option (logic.gateway.command) = {
          id: 100002
        };
    }

    rpc HoldMic (ga.super_channel.SuperChannelHoldMicReq) returns (ga.super_channel.SuperChannelHoldMicResp) {
        option (logic.gateway.command) = {
          id: 100003
        };
    }

    rpc ReleaseMic (ga.super_channel.SuperChannelReleaseMicReq) returns (ga.super_channel.SuperChannelReleaseMicResp) {
        option (logic.gateway.command) = {
          id: 100004
        };
    }

    rpc SetMicStatus (ga.super_channel.SuperChannelSetMicStatusReq) returns (ga.super_channel.SuperChannelSetMicStatusResp) {
        option (logic.gateway.command) = {
          id: 100005
        };
    }

    rpc SetMicMode (ga.super_channel.SuperChannelSetMicModeReq) returns (ga.super_channel.SuperChannelSetMicModeResp) {
        option (logic.gateway.command) = {
          id: 100006
        };
    }

    rpc GetMicList (ga.super_channel.SuperChannelGetMicListReq) returns (ga.super_channel.SuperChannelGetMicListResp) {
        option (logic.gateway.command) = {
          id: 100007
        };
    }
    rpc GetMemberList(ga.super_channel.SuperChannelGetMemberListReq) returns (ga.super_channel.SuperChannelGetMemberListResp) {
        option (logic.gateway.command) = {
            id: 100008
        };
    }

    rpc GetExtInfo(ga.super_channel.SuperChannelGetExtInfoReq) returns (ga.super_channel.SuperChannelGetExtInfoResp) {
        option (logic.gateway.command) = {
            id: 100009
        };
    }

    rpc SendHoldMicInvite(ga.super_channel.SuperChannelSendHoldMicInviteReq) returns (ga.super_channel.SuperChannelSendHoldMicInviteResp) {
        option (logic.gateway.command) = {
            id: 100015
        };
    }

    rpc ReplyHoldMicInvite(ga.super_channel.SuperChannelReplyHoldMicInviteReq) returns (ga.super_channel.SuperChannelReplyHoldMicInviteResp) {
        option (logic.gateway.command) = {
            id: 100011
        };
    }

    rpc ChangeMic(ga.super_channel.SuperChannelChangeMicReq) returns (ga.super_channel.SuperChannelChangeMicResp) {
        option (logic.gateway.command) = {
            id: 100012
        };
    }

    rpc GetChannelList(ga.super_channel.SuperChannelGetChannelListReq) returns (ga.super_channel.SuperChannelGetChannelListResp) {
        option (logic.gateway.command) = {
            id: 100013
        };
    }

    rpc CplSearch (ga.super_channel.SuperChannelSearchReq) returns (ga.super_channel.SuperChannelSearchResp){
        option (logic.gateway.command) = {
            id: 100014
        };
    }

    rpc GetSuperChannelSchemeInfo (ga.super_channel.GetSuperChannelSchemeInfoReq) returns (ga.super_channel.GetSuperChannelSchemeInfoResp){
        option (logic.gateway.command) = {
            id: 100016
        };
    }
}