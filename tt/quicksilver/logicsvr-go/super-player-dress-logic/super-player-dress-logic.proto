syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "super_player_dress_logic/super-player-dress-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/super-player-dress-logic";

service SuperPlayerDressLogic {
  option (logic.gateway.service_ext) = {
    service_name: "super-player-dress-logic"
  };

  // 获取会员装扮配置
  rpc GetDressConfigList (ga.super_player_dress_logic.GetDressConfigListReq) returns (ga.super_player_dress_logic.GetDressConfigListResp) {
    option (logic.gateway.command) = {
      id: 3750
    };
  }

  // 获取会员装扮配置版本
  rpc GetDressConfigMaxVersion (ga.super_player_dress_logic.GetDressConfigMaxVersionReq) returns (ga.super_player_dress_logic.GetDressConfigMaxVersionResp) {
    option (logic.gateway.command) = {
      id: 3751
    };
  }

  // 获取房间当前装扮套装
  rpc GetChannelCurrDressId (ga.super_player_dress_logic.GetChannelCurrDressIdReq) returns (ga.super_player_dress_logic.GetChannelCurrDressIdResp) {
    option (logic.gateway.command) = {
      id: 3752
    };
  }

  // 获取用户当前特别关心装扮
  rpc GetUserCurrSpecialConcernDressId (ga.super_player_dress_logic.GetUserCurrSpecialConcernDressIdReq) returns (ga.super_player_dress_logic.GetUserCurrSpecialConcernDressIdResp) {
    option (logic.gateway.command) = {
      id: 3753
    };
  }

  // 取消佩戴房间当前装扮套装
  rpc RemoveChannelCurrDressId (ga.super_player_dress_logic.RemoveChannelCurrDressIdReq) returns (ga.super_player_dress_logic.RemoveChannelCurrDressIdResp) {
    option (logic.gateway.command) = {
      id: 3754
    };
  }

  // 获取用户当前聊天气泡装扮
  rpc GetUserCurrChatBubbleDressId (ga.super_player_dress_logic.GetUserCurrChatBubbleDressIdReq) returns (ga.super_player_dress_logic.GetUserCurrChatBubbleDressIdResp) {
    option (logic.gateway.command) = {
      id: 3755
    };
  }

  // 获取用户当前聊天背景装扮
  rpc GetUserCurrChatBgDressIdList (ga.super_player_dress_logic.GetUserCurrChatBgDressIdListReq) returns (ga.super_player_dress_logic.GetUserCurrChatBgDressIdListResp) {
    option (logic.gateway.command) = {
      id: 3756
    };
  }
  // 获取装扮列表
  rpc GetUserDressList(ga.super_player_dress_logic.GetUserDressListReq) returns (ga.super_player_dress_logic.GetUserDressListResp) {
    option (logic.gateway.command) = {
      id: 3757
    };
  }
  // 设置装扮使用状态
  rpc SetDressInUse(ga.super_player_dress_logic.SetDressInUseReq) returns (ga.super_player_dress_logic.SetDressInUseResp) {
    option (logic.gateway.command) = {
      id: 3758
    };
  }
}