syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "superplayerlogic/super-player-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/superplayerlogic";

service SuperPlayerLogic {

  //会员系统相关配置
  rpc GetSuperPlayerConf (ga.superplayerlogic.GetSuperPlayerConfReq) returns (ga.superplayerlogic.GetSuperPlayerConfResp) {
    option (logic.gateway.command) = {
      id: 3700
    };
  }

  //获取会员信息
  rpc GetSuperPlayerInfo (ga.superplayerlogic.GetSuperPlayerInfoReq) returns (ga.superplayerlogic.GetSuperPlayerInfoResp) {
    option (logic.gateway.command) = {
      id: 3701
    };
  }

  //批量获取会员信息
  rpc BatchGetSuperPlayerInfo (ga.superplayerlogic.BatchGetSuperPlayerInfoReq) returns (ga.superplayerlogic.BatchGetSuperPlayerInfoResp) {
    option (logic.gateway.command) = {
      id: 3702
    };
  }

  // 获取用户特别关心
  rpc GetUserSpecialConcern (ga.superplayerlogic.GetUserSpecialConcernReq) returns (ga.superplayerlogic.GetUserSpecialConcernResp) {
    option (logic.gateway.command) = {
      id: 3703
    };
  }

  // 添加用户特别关心
  rpc AddUserSpecialConcern (ga.superplayerlogic.AddUserSpecialConcernReq) returns (ga.superplayerlogic.AddUserSpecialConcernResp) {
    option (logic.gateway.command) = {
      id: 3704
    };
  }

  // 移除用户特别关心
  rpc DelUserSpecialConcern (ga.superplayerlogic.DelUserSpecialConcernReq) returns (ga.superplayerlogic.DelUserSpecialConcernResp) {
    option (logic.gateway.command) = {
      id: 3705
    };
  }

  // 获取IM搭讪特权剩余次数
  rpc GetIMPrivilegeCount (ga.superplayerlogic.GetIMPrivilegeCountReq) returns (ga.superplayerlogic.GetIMPrivilegeCountResp) {
    option (logic.gateway.command) = {
      id: 3706
    };
  }

  // 使用一次IM搭讪特权
  rpc UseIMPrivilege (ga.superplayerlogic.UseIMPrivilegeReq) returns (ga.superplayerlogic.UseIMPrivilegeResp) {
    option (logic.gateway.command) = {
      id: 3707
    };
  }

  // 获取SVIP权益设置
  rpc GetUserSVIPPrivilegeProfile (ga.superplayerlogic.GetUserSVIPPrivilegeProfileReq) returns (ga.superplayerlogic.GetUserSVIPPrivilegeProfileResp) {
    option (logic.gateway.command) = {
      id: 3709
    };
  }

  // 更新SVIP权益设置
  rpc SetUserSVIPPrivilegeProfile (ga.superplayerlogic.SetUserSVIPPrivilegeProfileReq) returns (ga.superplayerlogic.SetUserSVIPPrivilegeProfileResp) {
    option (logic.gateway.command) = {
      id: 3710
    };
  }

  // 提前使用SVIP隐身权益天数
  rpc UseSVIPStealthAhead (ga.superplayerlogic.UseSVIPStealthAheadReq) returns (ga.superplayerlogic.UseSVIPStealthAheadResp) {
    option (logic.gateway.command) = {
      id: 3711
    };
  }

  rpc GetRenewalReminder(ga.superplayerlogic.GetRenewalReminderReq) returns (ga.superplayerlogic.GetRenewalReminderResp) {
    option (logic.gateway.command) = {
      id: 3712;
    };
  }

  rpc ReportSneakilyRead(ga.superplayerlogic.ReportSneakilyReadReq) returns (ga.superplayerlogic.ReportSneakilyReadResp) {
    option (logic.gateway.command) = {
      id: 3713;
    };
  }
  rpc CheckCouponPopUp(ga.superplayerlogic.CheckCouponPopUpReq) returns (ga.superplayerlogic.CheckCouponPopUpResp) {
    option (logic.gateway.command) = {
      id: 3714;
    };
  }
  rpc GetCouponPopUpLimit(ga.superplayerlogic.GetCouponPopUpLimitReq) returns (ga.superplayerlogic.GetCouponPopUpLimitResp) {
    option (logic.gateway.command) = {
      id: 3715;
    };
  }
}
