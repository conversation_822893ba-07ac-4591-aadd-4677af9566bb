syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "tbeanlogic/tbean-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/tbean-logic";

service TBeanLogic {
    option (logic.gateway.service_ext) = {
        service_name: "tbean-logic"
    };
    
    rpc GetUserTbeanConsume (ga.tbeanlogic.GetUserTbeanConsumeReq) returns (ga.tbeanlogic.GetUserTbeanConsumeResp) {
        option (logic.gateway.command) = {
            id: 32001
        };
    }

    rpc GetUserGroupInfo (ga.tbeanlogic.GetUserGroupInfoReq) returns (ga.tbeanlogic.GetUserGroupInfoResp) {
        option (logic.gateway.command) = {
            id: 32002
        };
    }
}