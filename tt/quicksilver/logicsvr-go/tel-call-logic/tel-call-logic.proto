syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "telcalllogic/tel-call-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/telcalllogic";

service TelCallLogic {
    rpc HandleCallGroup (ga.telcalllogic.HandleCallGroupReq) returns (ga.telcalllogic.HandleCallGroupResp) {
        option (logic.gateway.command) = {
            id: 102051
        };
    }

    rpc GetNotifyMsg (ga.telcalllogic.GetNotifyMsgReq) returns (ga.telcalllogic.GetNotifyMsgResp) {
        option (logic.gateway.command) = {
            id: 102052
        };
    }

    rpc JoinCallGroup (ga.telcalllogic.JoinCallGroupReq) returns (ga.telcalllogic.JoinCallGroupResp) {
        option (logic.gateway.command) = {
            id: 102053
        };
    }

    rpc GetCallGroupUsers (ga.telcalllogic.GetCallGroupUsersReq) returns (ga.telcalllogic.GetCallGroupUsersResp) {
        option (logic.gateway.command) = {
            id: 102054
        };
    }

    rpc CallStart (ga.telcalllogic.CallStartReq) returns (ga.telcalllogic.CallStartResp) {
        option (logic.gateway.command) = {
            id: 102055
        };
    }

    rpc GetCallUserStatus (ga.telcalllogic.GetCallUserStatusReq) returns (ga.telcalllogic.GetCallUserStatusResp) {
        option (logic.gateway.command) = {
            id: 102056
        };
    }

    rpc GetCallGroup (ga.telcalllogic.GetCallGroupReq) returns (ga.telcalllogic.GetCallGroupResp) {
        option (logic.gateway.command) = {
            id: 102057
        };
    }

    rpc GetSmsVerifyCode (ga.telcalllogic.GetSmsVerifyCodeReq) returns (ga.telcalllogic.GetSmsVerifyCodeResp) {
        option (logic.gateway.command) = {
            id: 102058
        };
    }

    rpc GetCallGroupAllUsers (ga.telcalllogic.GetCallGroupAllUsersReq) returns (ga.telcalllogic.GetCallGroupAllUsersResp) {
        option (logic.gateway.command) = {
            id: 102059
        };
    }

    rpc HandleNotifyMsg (ga.telcalllogic.HandleNotifyMsgReq) returns (ga.telcalllogic.HandleNotifyMsgResp) {
        option (logic.gateway.command) = {
            id: 102060
        };
    }

    rpc BatchRemoveGroupUser (ga.telcalllogic.BatchRemoveGroupUserReq) returns (ga.telcalllogic.BatchRemoveGroupUserResp) {
        option (logic.gateway.command) = {
            id: 102061
        };
    }

    rpc GetCallGroupUser (ga.telcalllogic.GetCallGroupUserReq) returns (ga.telcalllogic.GetCallGroupUserResp) {
        option (logic.gateway.command) = {
            id: 102062
        };
    }

    rpc GetEnterRoomInfo (ga.telcalllogic.GetEnterRoomInfoReq) returns (ga.telcalllogic.GetEnterRoomInfoResp) {
        option (logic.gateway.command) = {
            id: 102063
        };
    }
}