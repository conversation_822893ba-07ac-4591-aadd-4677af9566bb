syntax = "proto3";

package logic.topic_channel;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "topic_channel/topic_channel_.proto";
import "channel/channel_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/topic_channel_logic";

service TopicChannelLogic {
    option (logic.gateway.service_ext) = {
        service_name: "topic-channel-logic"
    };

    rpc CreateTopicChannel(ga.topic_channel.CreateTopicChannelReq) returns(ga.topic_channel.CreateTopicChannelResp) {
        option (logic.gateway.command) = {
            id: 3002
        };
    }

    rpc GetTopicChannelInfo(ga.topic_channel.GetTopicChannelInfoReq) returns(ga.topic_channel.GetTopicChannelInfoResp) {
        option (logic.gateway.command) = {
            id: 3003
        };
    }

    rpc ListRecommendTopicChannel(ga.topic_channel.ListRecommendTopicChannelReq) returns(ga.topic_channel.ListRecommendTopicChannelResp) {
        option (logic.gateway.command) = {
            id: 3000
        };
    }

    rpc TabTopicChannel(ga.topic_channel.TabTopicChannelReq) returns(ga.topic_channel.TabTopicChannelResp) {
        option (logic.gateway.command) = {
            id: 3001
        };
    }

    rpc GetTopicChannelRoomName (ga.topic_channel.GetTopicChannelRoomNameReq) returns (ga.topic_channel.GetTopicChannelRoomNameResp) {
        option (logic.gateway.command) = {
            id: 3004
        };
    }

    rpc GetRoomProxyTip (ga.topic_channel.GetRoomProxyTipReq) returns (ga.topic_channel.GetRoomProxyTipResp) {
        option (logic.gateway.command) = {
            id: 3010
        };
    }

    rpc GetBannerList (ga.channel.GetChannelAdvReq) returns (ga.channel.GetChannelAdvResp) {
        option (logic.gateway.command) = {
            id: 3011
        };
    }

    rpc HideTopicChannel (ga.topic_channel.HideTopicChannelReq) returns (ga.topic_channel.HideTopicChannelResp) {
        option (logic.gateway.command) = {
            id: 3020
        };
    }

    rpc KeepAliveTopicChannel (ga.topic_channel.KeepAliveTopicChannelReq) returns (ga.topic_channel.KeepAliveTopicChannelResp) {
        option (logic.gateway.command) = {
            id: 3021
        };
    }
    rpc GetChannelRoomNameConfig (ga.topic_channel.GetChannelRoomNameConfigReq) returns (ga.topic_channel.GetChannelRoomNameConfigResp) {
        option (logic.gateway.command) = {
            id: 3022
        };
    }

    //------------------------------------------------------------v2----------------------
    rpc CreateTopicChannelV2 (ga.topic_channel.CreateTopicChannelV2Req) returns (ga.topic_channel.CreateTopicChannelV2Resp) {
        option (logic.gateway.command) = {
            id: 3030
        };
    }
    rpc QuickFormTeam (ga.topic_channel.QuickFormTeamReq) returns (ga.topic_channel.QuickFormTeamResp) {
        option (logic.gateway.command) = {
            id: 3031
        };
    }
    rpc ListTopicChannelV2 (ga.topic_channel.ListTopicChannelV2Req) returns (ga.topic_channel.ListTopicChannelV2Resp) {
        option (logic.gateway.command) = {
            id: 3032
        };
    }
    rpc GetChannelDialog (ga.topic_channel.GetChannelDialogReq) returns (ga.topic_channel.GetChannelDialogResp) {
        option (logic.gateway.command) = {
            id: 3033
        };
    }
    // deprecate
    rpc GetDialogV2 (ga.topic_channel.GetDialogV2Req) returns (ga.topic_channel.GetDialogV2Resp) {
        option (logic.gateway.command) = {
            id: 3040
        };
    }
    // ListTabBlocks 用于获取某个 tab 的所有 block。
    rpc ListTabBlocks (ga.topic_channel.ListTabBlocksReq) returns (ga.topic_channel.ListTabBlocksResp) {
        option (logic.gateway.command) = {
            id: 3034
        };
    }
    rpc GetFormTeamInfo (ga.topic_channel.GetFormTeamInfoReq) returns (ga.topic_channel.GetFormTeamInfoResp) {
        option (logic.gateway.command) = {
            id: 3035
        };
    }
    rpc GetGuideConfig (ga.topic_channel.GetGuideConfigReq) returns (ga.topic_channel.GetGuideConfigResp) {
        option (logic.gateway.command) = {
            id: 3038
        };
    }

    //------------------------------------------切换玩法相关接口-----------------------------------------------------------
    rpc SwitchGamePlay (ga.topic_channel.SwitchGamePlayReq) returns (ga.topic_channel.SwitchGamePlayResp) {
        option (logic.gateway.command) = {
            id: 3036
        };
    }

    //-----------------------------------------小游戏相关接口--------------------------------------------------
    // GetTabList是TabTopicChannel的第二版本
    rpc GetTabList (ga.topic_channel.GetTabListReq) returns (ga.topic_channel.GetTabListResp) {
        option (logic.gateway.command) = {
            id: 3037
        };
    }

    // 快速匹配第二版，快速匹配临时房
    rpc QuickFormTeamV2 (ga.topic_channel.QuickFormTeamV2Req) returns (ga.topic_channel.QuickFormTeamV2Resp) {
        option (logic.gateway.command) = {
            id: 3041
        };
    }

    //------------------------------------------------首页广告位相关------------------------------------------------------
    rpc GetHomePageCardList (ga.topic_channel.GetHomePageCardListReq) returns (ga.topic_channel.GetHomePageCardListResp) {
        option (logic.gateway.command) = {
            id: 3039
        };
    }

    //------------------------------------------------IM列表萌新房推荐------------------------------------------------------
    rpc ListFreshmanRecommendedChannel (ga.topic_channel.ListFreshmanRecommendedChannelReq) returns (ga.topic_channel.ListFreshmanRecommendedChannelResp) {
        option (logic.gateway.command) = {
            id: 3042
        };
    }
    //------------------------------------------------找玩伴空白列表------------------------------------------------------
    rpc ListPlaymateRecommendedChannel (ga.topic_channel.ListPlaymateRecommendedChannelReq) returns (ga.topic_channel.ListPlaymateRecommendedChannelResp) {
        option (logic.gateway.command) = {
            id: 3043
        };
    }
    //------------------------------------------------------------5.5.0----------------------
    rpc CreateAndReleaseTopicChannel (ga.topic_channel.CreateAndReleaseTopicChannelReq) returns (ga.topic_channel.CreateAndReleaseTopicChannelResp) {
        option (logic.gateway.command) = {
            id: 3051
        };
    }

    rpc DistributionTopicChannel (ga.topic_channel.DistributionTopicChannelReq) returns (ga.topic_channel.DistributionTopicChannelResp) {
        option (logic.gateway.command) = {
            id: 3053
        };
    }

    rpc GetTabListWhenCreate (ga.topic_channel.GetTabListWhenCreateReq) returns (ga.topic_channel.GetTabListWhenCreateResp) {
        option (logic.gateway.command) = {
            id: 3054
        };
    }
    rpc ListTopicChannelV3 (ga.topic_channel.ListTopicChannelV3Req) returns (ga.topic_channel.ListTopicChannelV3Resp) {
        option (logic.gateway.command) = {
            id: 3055
        };
    }
    //迁移至channel-play-logic.proto
    rpc ShowTopicChannelTabList (ga.topic_channel.ShowTopicChannelTabListReq) returns (ga.topic_channel.ShowTopicChannelTabListResp) {
        option (logic.gateway.command) = {
            id: 3056
            deprecated: true
        };
    }

    rpc GetSubTabList (ga.topic_channel.GetSubTabListReq) returns (ga.topic_channel.GetSubTabListResp) {
        option (logic.gateway.command) = {
            id: 3057
        };
    }

    rpc GetQuickMatchTabList (ga.topic_channel.GetQuickMatchTabListReq) returns (ga.topic_channel.GetQuickMatchTabListResp) {
        option (logic.gateway.command) = {
            id: 3058
        };
    }

    rpc GetNegativeFeedBackOption (ga.topic_channel.GetNegativeFeedBackOptionReq) returns (ga.topic_channel.GetNegativeFeedBackOptionResp) {
        option (logic.gateway.command) = {
            id: 3059
        };
    }

    rpc NegativeFeedBack (ga.topic_channel.NegativeFeedBackReq) returns (ga.topic_channel.NegativeFeedBackResp) {
        option (logic.gateway.command) = {
            id: 3060
        };
    }

    rpc GetLiveTogetherConfig (ga.topic_channel.GetLiveTogetherConfigReq) returns (ga.topic_channel.GetLiveTogetherConfigResp) {
        option (logic.gateway.command) = {
            id: 3062
        };
    }
    rpc SetLiveTogetherStatus (ga.topic_channel.SetLiveTogetherStatusReq) returns (ga.topic_channel.SetLiveTogetherStatusResp) {
        option (logic.gateway.command) = {
            id: 3063
        };
    }
    //------------------------------------------------首页玩法------------------------------------------------------
    rpc LabelSearch (ga.topic_channel.LabelSearchReq) returns (ga.topic_channel.LabelSearchResp) {
        option (logic.gateway.command) = {
            id: 3070
        };
    }
    rpc GetGameLabels (ga.topic_channel.GetGameLabelsReq) returns (ga.topic_channel.GetGameLabelsResp) {
        option (logic.gateway.command) = {
            id: 3071
        };
    }
    rpc GetLabelSearchGuide (ga.topic_channel.GetLabelSearchGuideReq) returns (ga.topic_channel.GetLabelSearchGuideResp) {
        option (logic.gateway.command) = {
            id: 3072
        };
    }

    rpc GetTCCache(ga.topic_channel.GetTCCacheReq) returns (ga.topic_channel.GetTCCacheResp) {}


}