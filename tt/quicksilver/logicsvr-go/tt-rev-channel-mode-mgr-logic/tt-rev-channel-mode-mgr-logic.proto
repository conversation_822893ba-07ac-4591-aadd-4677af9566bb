syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "tt_rev_channel_mode_mgr_logic/tt-rev-channel-mode-mgr-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/tt-rev-channel-mode-mgr-logic";

 
service ChannelModeMgrLogic {
  option (logic.gateway.service_ext) = {
    service_name: "tt-rev-channel-mode-mgr-logic"
  };


  // 获取房间玩法列表
  rpc GetChannelMode (ga.tt_rev_channel_mode_mgr_logic.GetChannelModeReq) returns (ga.tt_rev_channel_mode_mgr_logic.GetChannelModeResp) {
    option (logic.gateway.command) = {
      id: 33210   //改成正确的id
    };
  }

  // 设置房间当前玩法
  rpc SetChannelMode (ga.tt_rev_channel_mode_mgr_logic.SetChannelModeReq) returns (ga.tt_rev_channel_mode_mgr_logic.SetChannelModeResp) {
    option (logic.gateway.command) = {
      id: 33211   //改成正确的id
    };
  }
}