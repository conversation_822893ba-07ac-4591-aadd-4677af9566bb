syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "udeskapilogic/udesk-api-logic_.proto";
// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/udeskapilogic";

service UdeskApiLogic {
    option (logic.gateway.service_ext) = {
      service_name: "udesk-api-logic"
    };
    // 获取未读消息的数量
    rpc GetUdeskUnReadMsg (ga.udeskapilogic.GetUdeskUnReadMsgReq) returns (ga.udeskapilogic.GetUdeskUnReadMsgResp) {
        option (logic.gateway.command) = {
            id: 95000
        };
    }
    // 检查vip客服入口权限
    rpc CheckVipKefuAccess (ga.udeskapilogic.CheckVipKefuAccessReq) returns (ga.udeskapilogic.CheckVipKefuAccessResp) {
        option (logic.gateway.command) = {
            id: 95001
        };
    }
    // 确认收到vip客服入口可见
    rpc AckVipKefuAccess (ga.udeskapilogic.AckVipKefuAccessReq) returns (ga.udeskapilogic.AckVipKefuAccessResp) {
        option (logic.gateway.command) = {
            id: 95002
        };
    }
    // 进入vip客服
    rpc EnterVipKefu (ga.udeskapilogic.EnterVipKefuReq) returns (ga.udeskapilogic.EnterVipKefuResp) {
        option (logic.gateway.command) = {
            id: 95003
        };
    }

}
