syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "ugc/ugc_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/ugc-friendship-logic";

//import "google/api/annotations.proto";

service UgcFriendshipLogic {
    option (logic.gateway.service_ext) = {
        service_name: "ugc-friendship-logic"
    };
    //////////////////////////////////////////////////

    rpc GetUserFriendships (ga.ugc.GetUserFriendshipsReq) returns (ga.ugc.GetUserFriendshipsResp) {
        option (logic.gateway.command) = {
            id: 2561
        };
    }

    rpc FollowUser (ga.ugc.FriendshipOperationReq) returns (ga.ugc.FriendshipOperationResp) {
        option (logic.gateway.command) = {
            id: 2562
        };
    }

    rpc UnfollowUser (ga.ugc.FriendshipOperationReq) returns (ga.ugc.FriendshipOperationResp) {
        option (logic.gateway.command) = {
            id: 2563
        };
    }

    rpc RemoveFollower (ga.ugc.FriendshipOperationReq) returns (ga.ugc.FriendshipOperationResp) {
        option (logic.gateway.command) = {
            id: 2564
        };
    }

    rpc QuickCheckFriendship (ga.ugc.QuickCheckFriendshipReq) returns (ga.ugc.QuickCheckFriendshipResp) {
        option (logic.gateway.command) = {
            id: 2565
        };
    }

    //批量添加好友
    rpc FollowBatchUser(ga.ugc.FollowBatchUserReq)returns(ga.ugc.FollowBatchUserResp){
        option (logic.gateway.command) = {
            id: 2566
        };
    }

    rpc GetShowUserFollow(ga.ugc.GetShowUserFollowReq) returns (ga.ugc.GetShowUserFollowResp) {
        option (logic.gateway.command) = {
            id: 2568
        };
    }

    rpc SetShowUserFollow(ga.ugc.SetShowUserFollowReq) returns (ga.ugc.SetShowUserFollowResp) {
        option (logic.gateway.command) = {
            id: 2569
        };
    }

}
