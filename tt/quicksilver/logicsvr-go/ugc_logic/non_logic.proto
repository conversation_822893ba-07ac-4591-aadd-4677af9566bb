syntax = "proto3";
// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package logic.ugc_non_public;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "ugc_non_public/ugc_non_public.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/ugc_logic";

//import "google/api/annotations.proto";

service NonPublicLogic {
  option (logic.gateway.service_ext) = {
    service_name: "ugc-logic"
  };

  rpc PostNonPublicPost(ga.ugc_non_public.PostNonPublicPostReq) returns (ga.ugc_non_public.PostNonPublicPostResp) {
    option (logic.gateway.command) = {
      id: 2632
    };
  }

  rpc GetNonPublicPostPublishExtend(ga.ugc_non_public.GetNonPublicPostPublishExtendReq) returns (ga.ugc_non_public.GetNonPublicPostPublishExtendResp) {
    option (logic.gateway.command) = {
      id: 2633
    };
  }

  rpc MarkNonPublicPostAttachmentUploaded(ga.ugc_non_public.MarkNonPublicPostAttachmentUploadedReq) returns (ga.ugc_non_public.MarkNonPublicPostAttachmentUploadedResp) {
    option (logic.gateway.command) = {
      id: 2634
    };
  }

  rpc GetNonPublicNewsFeeds(ga.ugc_non_public.GetNonPublicNewsFeedsReq) returns (ga.ugc_non_public.GetNonPublicNewsFeedsResp) {
    option (logic.gateway.command) = {
      id: 2635
    };
  }

  rpc GetNonPublicPost(ga.ugc_non_public.GetNonPublicPostReq) returns (ga.ugc_non_public.GetNonPublicPostResp) {
    option (logic.gateway.command) = {
      id: 2636
    };
  }

  rpc PostNonPublicComment(ga.ugc_non_public.PostNonPublicCommentReq) returns (ga.ugc_non_public.PostNonPublicCommentResp) {
    option (logic.gateway.command) = {
      id: 2637
    };
  }

  rpc GetNonPublicCommentList(ga.ugc_non_public.GetNonPublicCommentListReq) returns (ga.ugc_non_public.GetNonPublicCommentListResp) {
    option (logic.gateway.command) = {
      id: 2638
    };
  }

  rpc NonPublicExpressAttitude(ga.ugc_non_public.NonPublicExpressAttitudeReq) returns (ga.ugc_non_public.NonPublicExpressAttitudeResp) {
    option (logic.gateway.command) = {
      id: 2639
    };
  }

  rpc MarkUserStreamRecord(ga.ugc_non_public.MarkUserStreamRecordReq) returns (ga.ugc_non_public.MarkUserStreamRecordResp) {
    option (logic.gateway.command) = {
      id: 2640
    };
  }

  rpc ReportNonPublicPostShare(ga.ugc_non_public.ReportNonPublicPostShareRequest) returns (ga.ugc_non_public.ReportNonPublicPostShareResponse) {
    option (logic.gateway.command) = {
      id: 2641
    };
  }

  rpc DeleteNonPublicPost(ga.ugc_non_public.DeleteNonPublicPostRequest) returns (ga.ugc_non_public.DeleteNonPublicPostResponse) {
    option (logic.gateway.command) = {
      id: 2642
    };
  }

  rpc DeleteNonPublicComment(ga.ugc_non_public.DeleteNonPublicCommentRequest) returns (ga.ugc_non_public.DeleteNonPublicCommentResponse) {
    option (logic.gateway.command) = {
      id: 2643
    };
  }

  /*注释*/
  rpc EditNonPublicPost(ga.ugc_non_public.EditNonPublicPostRequest) returns (ga.ugc_non_public.EditNonPublicPostResponse) {
    option (logic.gateway.command) = {
      id: 2644
    };
  }

  /*注释*/
  rpc GetNonPublicEditPostExtend(ga.ugc_non_public.GetNonPublicEditPostExtendRequest) returns (ga.ugc_non_public.GetNonPublicEditPostExtendResponse) {
    option (logic.gateway.command) = {
      id: 2645
    };
  }

  /*批量转换社群讨论贴*/
  rpc BatchConversionNonPublicPost(ga.ugc_non_public.BatchConversionNonPublicPostRequest) returns (ga.ugc_non_public.BatchConversionNonPublicPostResponse) {
    option (logic.gateway.command) = {
      id: 2646
    };
  }

  /*强插转换过的帖子推送*/
  rpc ForceConversionPostPush(ga.ugc_non_public.ForceConversionPostPushRequest) returns (ga.ugc_non_public.ForceConversionPostPushResponse) {
    option (logic.gateway.command) = {
      id: 2647
    };
  }

}
