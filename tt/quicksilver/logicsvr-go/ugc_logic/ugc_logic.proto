syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package logic.ugc;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "ugc/ugc_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/ugc_logic";

//import "google/api/annotations.proto";

service ContentLogic {
    option (logic.gateway.service_ext) = {
        service_name: "ugc-logic"
    };

    //订阅 订阅主题
    rpc SubscribeTopic (ga.ugc.SubscribeTopicReq) returns (ga.ugc.SubscribeTopicResp) {
        option (logic.gateway.command) = {
            id: 2550
        };
    }

    //取消订阅
    rpc UnsubscribeTopic (ga.ugc.UnsubscribeTopicReq) returns (ga.ugc.UnsubscribeTopicResp) {
        option (logic.gateway.command) = {
            id: 2551
        };
    }

    //获取主题列表
    rpc GetTopicList (ga.ugc.GetTopicListReq) returns (ga.ugc.GetTopicListResp) {
        option (logic.gateway.command) = {
            id: 2552
        };
    }

    
    //返回指定主题详细资料
    rpc GetTopicInfo (ga.ugc.GetTopicInfoReq) returns (ga.ugc.GetTopicInfoResp) {
        option (logic.gateway.command) = {
            id: 2553
        };
    }

    //获取订阅的主题列表
    rpc GetSubscriberTopicList (ga.ugc.GetSubscribeTopicListReq) returns (ga.ugc.GetSubscribeTopicListResp) {
        option (logic.gateway.command) = {
            id: 2554
        };
    }

    //获取未订阅的主题列表
    rpc GetUnSubscribeTopicList(ga.ugc.GetUnSubscribeTopicListReq) returns (ga.ugc.GetUnSubscribeTopicListResp){
        option (logic.gateway.command) = {
            id: 2555
        };
    }

    //判断主题是否已经订阅
    rpc CheckTopicsIsSubscribe(ga.ugc.CheckTopicsIsSubscribeReq)returns(ga.ugc.CheckTopicsIsSubscribeResp){
        option (logic.gateway.command) = {
            id: 2556
        };
    }

    //按名字搜索topic
    rpc SearchTopic(ga.ugc.SearchTopicReq)returns(ga.ugc.SearchTopicResp){
        option (logic.gateway.command) = {
            id: 2557
        };
    }



    // 更新附件的隐私设置
    rpc UpdateAttachmentPrivacy (ga.ugc.UpdateAttachmentDownloadPrivacyReq) returns (ga.ugc.UpdateAttachmentDownloadPrivacyResp) {
        option (logic.gateway.command) = {
            id: 2587
        };
    }

    // 置顶帖子/评论
    rpc MarkContentSticky(ga.ugc.MarkContentStickyReq) returns (ga.ugc.MarkContentStickyResp) {
        option (logic.gateway.command) = {
            id: 2588
        };
    }

    // 发帖
    rpc PostPost (ga.ugc.PostPostReq) returns (ga.ugc.PostPostResp) {
        option (logic.gateway.command) = {
            id: 2590
        };
    }

    // 发帖附件上传完毕
    rpc MarkPostAttachmentUploaded (ga.ugc.MarkPostAttachmentUploadedReq) returns (ga.ugc.MarkPostAttachmentUploadedResp) {
        option (logic.gateway.command) = {
            id: 2591
        };
    }

    rpc DeletePost (ga.ugc.DeletePostReq) returns (ga.ugc.DeletePostResp) {
        option (logic.gateway.command) = {
            id: 2592
        };
    }

    // 帖子详情
    rpc GetPost (ga.ugc.GetPostReq) returns (ga.ugc.GetPostResp) {
        option (logic.gateway.command) = {
            id: 2593
        };
//        option (google.api.http) = {
//            get:"/v1/ugc/post/{base_req.app_id}/{post_id}"
//        };
    }

    // 评论
    rpc PostComment (ga.ugc.PostCommentReq) returns (ga.ugc.PostCommentResp) {
        option (logic.gateway.command) = {
            id: 2594
        };
    }

    // 删除评论
    rpc DeleteComment (ga.ugc.DeleteCommentReq) returns (ga.ugc.DeleteCommentResp) {
        option (logic.gateway.command) = {
            id: 2595
        };
    }

    // 评论列表
    rpc GetCommentList (ga.ugc.GetCommentListReq) returns (ga.ugc.GetCommentListResp) {
        option (logic.gateway.command) = {
            id: 2596
        };
    }

    rpc ReportPostView (ga.ugc.ReportPostViewReq) returns (ga.ugc.ReportPostViewResp) {
        option (logic.gateway.command) = {
            id: 2597
        };
    }

    //帖子点赞用户列表
    rpc GetAttitudeUserList(ga.ugc.GetAttitudeUserListReq) returns (ga.ugc.GetAttitudeUserListResp) {
        option (logic.gateway.command) = {
            id: 2598
        };
    }

    rpc ReportPostShare (ga.ugc.ReportPostShareReq) returns (ga.ugc.ReportPostShareResp) {
        option (logic.gateway.command) = {
            id: 2599
        };
    }

    // 点赞
    rpc ExpressAttitude (ga.ugc.ExpressAttitudeReq) returns (ga.ugc.ExpressAttitudeResp) {
        option (logic.gateway.command) = {
            id: 2600
        };
    }

    //get feeds
    rpc GetNewsFeeds (ga.ugc.GetNewsFeedsReq) returns (ga.ugc.GetNewsFeedsResp) {
        option (logic.gateway.command) = {
            id: 2570
        };
    }


    //remove feeds
    rpc RemoveFeeds (ga.ugc.RemoveFeedsReq) returns (ga.ugc.RemoveFeedsResp) {
        option (logic.gateway.command) = {
            id: 2571
        };
    }


    //上报浏览记录
    rpc ReportVisitRecord (ga.ugc.ReportVisitRecordReq) returns (ga.ugc.ReportVisitRecordResp) {
        option (logic.gateway.command) = {
            id: 2572
        };
    }

    //收藏
    rpc AddFavourite (ga.ugc.AddFavouriteReq) returns (ga.ugc.AddFavouriteResp) {
        option (logic.gateway.command) = {
            id: 2573
        };
    }
    
    // report post multimedia view
    rpc ReportPostMultimediaView (ga.ugc.ReportPostMultimediaViewReq) returns (ga.ugc.ReportPostMultimediaViewResp) {
        option (logic.gateway.command) = {
            id: 2589
        };
    }

    // ----------------- audio post -----------------
    rpc AudioPostScriptTabs ( ga.ugc.AudioPostScriptTabsReq ) returns ( ga.ugc.AudioPostScriptTabsResp ) {
        option (logic.gateway.command) = {
            id: 2610
        };
    }

    rpc AudioPostScripts ( ga.ugc.AudioPostScriptsReq ) returns ( ga.ugc.AudioPostScriptsResp ) {
        option (logic.gateway.command) = {
            id: 2611
        };
    }

    rpc RandomAudioPostScripts ( ga.ugc.RandomAudioPostScriptsReq ) returns ( ga.ugc.RandomAudioPostScriptsResp ) {
        option (logic.gateway.command) = {
            id: 2612
        };
    }

    rpc RandomAudioPostImages ( ga.ugc.RandomAudioPostImagesReq ) returns ( ga.ugc.RandomAudioPostImagesResp ) {
        option (logic.gateway.command) = {
            id: 2613
        };
    }

    rpc AudioPostImages ( ga.ugc.AudioPostImagesReq ) returns ( ga.ugc.AudioPostImagesResp ) {
        option (logic.gateway.command) = {
            id: 2614
        };
    }

    rpc RandomAudioPostMusics ( ga.ugc.RandomAudioPostMusicsReq ) returns ( ga.ugc.RandomAudioPostMusicsResp ) {
        option (logic.gateway.command) = {
            id: 2615
        };
    }

    rpc AudioPostMusics ( ga.ugc.AudioPostMusicsReq ) returns ( ga.ugc.AudioPostMusicsResp ) {
        option (logic.gateway.command) = {
            id: 2616
        };
    }

    rpc AudioPostMusicTabs ( ga.ugc.AudioPostMusicTabsReq ) returns ( ga.ugc.AudioPostMusicTabsResp ) {
        option (logic.gateway.command) = {
            id: 2617
        };
    }

    rpc XunfeiSignature ( ga.ugc.XunfeiSignatureReq ) returns ( ga.ugc.XunfeiSignatureResp ) {
        option (logic.gateway.command) = {
            id: 2618
        };
    }

    rpc AudioPostScriptsForMobile ( ga.ugc.AudioPostScriptsForMobileReq ) returns ( ga.ugc.AudioPostScriptsForMobileResp ) {
        option (logic.gateway.command) = {
            id: 2619
        };
    }

    rpc DelRandList ( ga.ugc.DelRandListReq ) returns ( ga.ugc.DelRandListResp ) {
        option (logic.gateway.command) = {
            id: 2620
        };
    }

    rpc AudioPostScriptTabsV2 ( ga.ugc.AudioPostScriptTabsV2Req ) returns ( ga.ugc.AudioPostScriptTabsV2Resp ) {
        option (logic.gateway.command) = {
            id: 2621
        };
    }

    rpc AudioPostScriptsV2 ( ga.ugc.AudioPostScriptsV2Req ) returns ( ga.ugc.AudioPostScriptsV2Resp ) {
        option (logic.gateway.command) = {
            id: 2622
        };
    }

    rpc RandomAudioPostScriptsV2 ( ga.ugc.RandomAudioPostScriptsV2Req ) returns ( ga.ugc.RandomAudioPostScriptsV2Resp ) {
        option (logic.gateway.command) = {
            id: 2623
        };
    }

    rpc RandomAudioPostImagesV2 ( ga.ugc.RandomAudioPostImagesV2Req ) returns ( ga.ugc.RandomAudioPostImagesV2Resp ) {
        option (logic.gateway.command) = {
            id: 2624
        };
    }

    rpc AudioPostImagesV2 ( ga.ugc.AudioPostImagesV2Req ) returns ( ga.ugc.AudioPostImagesV2Resp ) {
        option (logic.gateway.command) = {
            id: 2625
        };
    }

    rpc RandomAudioPostMusicsV2 ( ga.ugc.RandomAudioPostMusicsV2Req ) returns ( ga.ugc.RandomAudioPostMusicsV2Resp ) {
        option (logic.gateway.command) = {
            id: 2626
        };
    }

    rpc AudioPostMusicsV2 ( ga.ugc.AudioPostMusicsV2Req ) returns ( ga.ugc.AudioPostMusicsV2Resp ) {
        option (logic.gateway.command) = {
            id: 2627
        };
    }

    rpc AudioPostMusicTabsV2 ( ga.ugc.AudioPostMusicTabsV2Req ) returns ( ga.ugc.AudioPostMusicTabsV2Resp ) {
        option (logic.gateway.command) = {
            id: 2628
        };
    }

    rpc XunfeiSignatureV2 ( ga.ugc.XunfeiSignatureV2Req ) returns ( ga.ugc.XunfeiSignatureV2Resp ) {
        option (logic.gateway.command) = {
            id: 2629
        };
    }

    rpc AudioPostScriptsForMobileV2 ( ga.ugc.AudioPostScriptsForMobileV2Req ) returns ( ga.ugc.AudioPostScriptsForMobileV2Resp ) {
        option (logic.gateway.command) = {
            id: 2630
        };
    }

    rpc DelRandListV2 ( ga.ugc.DelRandListV2Req ) returns ( ga.ugc.DelRandListV2Resp ) {
        option (logic.gateway.command) = {
            id: 2631
        };
    }

    rpc GetAttentionPeople (ga.ugc.AttentionPeopleReq) returns (ga.ugc.AttentionPeopleRsp) {
        option (logic.gateway.command) = {
            id: 2900
        };
    }

    rpc IsKolExist (ga.ugc.IsKolExistReq) returns (ga.ugc.IsKolExistRsp) {
        option (logic.gateway.command) = {
            id: 2901
        };
    }

    rpc HideHighContent (ga.ugc.HideHighContentReq) returns (ga.ugc.HideHighContentRsp) {
        option (logic.gateway.command) = {
            id: 2902
        };
    }
    
    rpc GetPublisherTopicByUid (ga.ugc.GetPublisherTopicByUidReq) returns (ga.ugc.GetPublisherTopicByUidResp) {
        option (logic.gateway.command) = {
            id: 30145
        };
    }

    rpc UpdatePostPrivacyPolicy(ga.ugc.UpdatePostPrivacyPolicyReq) returns (ga.ugc.UpdatePostPrivacyPolicyResp) {
        option (logic.gateway.command) = {
            id: 2903
        };
    }

    rpc GetNewestPosts(ga.ugc.GetNewestPostsReq) returns (ga.ugc.GetNewestPostsResp) {
        option (logic.gateway.command) = {
            id: 30147
        };
    }


    //获取帖子id获取相应的流接口 命令号还没开放
    rpc GetFeedsByPostIDs(ga.ugc.GetFeedsByPostIDsReq) returns (ga.ugc.GetNewsFeedsResp) {
        option (logic.gateway.command) = {
            id: 30148
        };
    }

    /*推荐流插入话题*/
    rpc TopicInRecommendFeed(ga.ugc.TopicInRecommendFeedReq) returns (ga.ugc.TopicInRecommendFeedResp) {
        option (logic.gateway.command) = {
            id: 30149
        };
    }

    /* 话题详情页广告 */
    rpc GetTopicAds(ga.ugc.GetTopicAdsReq) returns (ga.ugc.GetTopicAdsResp){
        option (logic.gateway.command) = {
            id: 2604
        };
    }

    rpc GetMoonDetail(ga.ugc.GetMoonDetailReq) returns (ga.ugc.GetMoonDetailRsp){
        option (logic.gateway.command) = {
            id: 2606
        };
    }

    rpc GetMoodConfig(ga.ugc.GetMoodConfigReq) returns (ga.ugc.GetMoodConfigRsp){
        option (logic.gateway.command) = {
            id: 2607
        };
    }

     rpc GetTabConfig(ga.ugc.GetTabConfigReq) returns (ga.ugc.GetTabConfigRsp){
        option (logic.gateway.command) = {
            id: 2608
        };
    }

    //上报帖子与话题无关
    rpc ReportUnrelatedTopic(ga.ugc.ReportUnrelatedTopicReq) returns (ga.ugc.ReportUnrelatedTopicRsp){
        option (logic.gateway.command) = {
            id: 2609
        };
    }

    rpc VotePost(ga.ugc.VotePostReq) returns (ga.ugc.VotePostResp){
        option (logic.gateway.command) = {
            id: 2574
        };
    }

    //获取个人主页游戏入口
    rpc GetGameEntrance(ga.ugc.GetGameEntranceReq) returns (ga.ugc.GetGameEntranceResp) {
        option (logic.gateway.command) = {
            id: 2575
        };
    }

    //发布按钮引导配置 
    rpc GetPostButtonGuide(ga.ugc.GetPostButtonGuideReq) returns (ga.ugc.GetPostButtonGuideResp) {
        option (logic.gateway.command) = {
            id: 37001
        };
    }
}
