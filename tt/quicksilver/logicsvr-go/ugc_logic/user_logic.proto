syntax = "proto3";
// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package logic.ugc;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "ugc/ugc_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/ugc_logic";

//import "google/api/annotations.proto";

service UserLogic {
    option (logic.gateway.service_ext) = {
        service_name: "ugc-logic"
    };
    //////////////////////////////////////////////////
    // 用户相关
    rpc GetUserInfo (ga.ugc.GetUserUGCInfoReq) returns (ga.ugc.GetUserUGCInfoResp) {
        option (logic.gateway.command) = {
            id: 2560
        };
    }

    // 推荐需要关注用户
    rpc RecommendAttentionUser(ga.ugc.RecommendAttentionUserReq) returns(ga.ugc.RecommendAttentionUserResp) {
        option (logic.gateway.command) = {
            id: 2567
        };
    }

    rpc GetInteractiveMsg (ga.ugc.GetInteractiveMsgReq) returns (ga.ugc.GetInteractiveMsgResp) {
        option (logic.gateway.command) = {
            id: 2580
        };
    }

    rpc MarkRead (ga.ugc.MarkReadReq) returns (ga.ugc.MarkReadResp) {
        option (logic.gateway.command) = {
            id: 2581
        };
    }

    rpc DelInteractiveMsg (ga.ugc.DelInteractiveMsgReq) returns (ga.ugc.DelInteractiveMsgResp) {
        option (logic.gateway.command) = {
            id: 2583
        };
    }

    rpc Concern(ga.ugc.ConcernReq) returns (ga.ugc.ConcernResp) {
        option (logic.gateway.command) = {
            id: 2584
        };
    }

    //获取用户地理信息 用户相关
    rpc GetUserGeoInfo(ga.ugc.GetUserGeoInfoReq) returns (ga.ugc.GetUserGeoInfoRsp) {
        option (logic.gateway.command) = {
            id: 603
        };
    }

    rpc GetUserFollowPost(ga.ugc.GetUserFollowPostReq) returns (ga.ugc.GetUserFollowPostRsp){
        option (logic.gateway.command) = {
            id: 2602
        };
    }

    rpc ReportUserFollowPost(ga.ugc.ReportUserFollowPostReq) returns (ga.ugc.ReportUserFollowPostRsp){
        option (logic.gateway.command) = {
            id: 2603
        };
    }
}
