syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "unclaimed/unclaimed_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/unclaimed-logic";

service UnclaimedLogic {
  option (logic.gateway.service_ext) = {
    service_name: "unclaimed-logic"
  };

  rpc XunfeiAstSignature (ga.unclaimed.XunfeiAstSignatureReq) returns (ga.unclaimed.XunfeiAstSignatureResp) {
    option (logic.gateway.command) = {
      id: 30170
    };
  }

  rpc GetExpandEntranceList (ga.unclaimed.GetExpandEntranceListReq) returns (ga.unclaimed.GetExpandEntranceListResp) {
    option (logic.gateway.command) = {
      id: 30171
    };
  }

  rpc GetOpenGameAuthorization (ga.unclaimed.GetOpenGameAuthorizationReq) returns (ga.unclaimed.GetOpenGameAuthorizationResp) {
    option (logic.gateway.command) = {
      id: 30172
    };
  }

  rpc SetChannelMedia (ga.unclaimed.SetChannelMediaReq) returns (ga.unclaimed.SetChannelMediaResp){
    option (logic.gateway.command) = {
      id: 30173
    };
  }

  rpc GetObsUploadToken (ga.unclaimed.GetObsUploadTokenReq) returns (ga.unclaimed.GetObsUploadTokenResp){
    option (logic.gateway.command) = {
      id: 30174
    };
  }

  rpc CheckUserIdentity (ga.unclaimed.CheckUserIdentityReq) returns (ga.unclaimed.CheckUserIdentityResp){
    option (logic.gateway.command) = {
      id: 30176
    };
  }

  rpc GetMarketingUserInfo (ga.unclaimed.GetMarketingUserInfoReq) returns (ga.unclaimed.GetMarketingUserInfoResp){
    option (logic.gateway.command) = {
      id: 50061
    };
  }

  rpc GetBlackWhiteBoxSuperviseSwitch (ga.unclaimed.GetBlackWhiteBoxSuperviseSwitchReq) returns (ga.unclaimed.GetBlackWhiteBoxSuperviseSwitchResp){
    option (logic.gateway.command) = {
      id: 30175
    };
  }

}
