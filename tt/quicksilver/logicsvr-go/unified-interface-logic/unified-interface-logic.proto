syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/unified-interface-logic";

import "unified_interface/unified-interface_.proto";
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";

service UnifiedInterfaceLogic {
    option (logic.gateway.service_ext) = {
        service_name: "unified-interface-logic"
    };
    // 统一房间内显示功能判断接口
    rpc UnifiedChannelShowStatus (ga.unified_interface.UnifiedChannelShowStatusReq) returns (ga.unified_interface.UnifiedChannelShowStatusResp) {
        option (logic.gateway.command) = {
            id: 30622
        };
    }
}
