syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/ancient-search-logic";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "ancient_search/ancient-search_.proto";

service AncientSearchLogic {
  option (logic.gateway.service_ext) = {
    service_name: "unified-search-logic"
  };

  rpc SearchContact (ga.ancient_search.SearchContactReq) returns (ga.ancient_search.SearchContactResp) {
    option (logic.gateway.command) = {
      id: 36
    };
  }

  rpc SearchGuild (ga.ancient_search.GuildSearchReq) returns (ga.ancient_search.GuildSearchResp) {
    option (logic.gateway.command) = {
      id: 42
    };
  }

  rpc SearchChannel (ga.ancient_search.SearchChannelReq) returns (ga.ancient_search.SearchChannelResp) {
    option (logic.gateway.command) = {
      id: 448
    };
  }
}
