syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/unified-search-logic";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "unified_search/unified-search_.proto";
import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";

service UnifiedSearchLogic {
  option (logic.gateway.service_ext) = {
    service_name: "unified-search-logic"
  };

  // 统一搜索接口
  rpc UnifiedSearch (ga.unified_search.UnifiedSearchReq) returns (ga.unified_search.UnifiedSearchResp) {
    option (logic.gateway.command) = {
      id: 30621
    };
  };

  // 统一搜索接口 新
  rpc CommonSearch (ga.unified_search.CommonSearchReq) returns (ga.unified_search.CommonSearchResp) {
    option (logic.gateway.command) = {
      id: 30623
    };
  };

    // 获取引导词接口
  rpc SearchGuideWords (ga.unified_search.SearchGuideWordsReq) returns (ga.unified_search.SearchGuideWordsResp) {
    option (logic.gateway.command) = {
      id: 30624
    };
  };

    // 统一搜索接口
  rpc SearchSuggest (ga.unified_search.SearchSuggestReq) returns (ga.unified_search.SearchSuggestResp) {
    option (logic.gateway.command) = {
      id: 30625
    };
  };

}
