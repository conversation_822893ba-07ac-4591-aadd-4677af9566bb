syntax = "proto3";

package logic.userblacklist;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "user_black_list_logic/user-black-list-logic_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/user-black-list-logic";

service UserBlackListLogic {
    option (logic.gateway.service_ext) = {
        service_name: "user-black-list-logic"
    };

    rpc GetUserBlackList (ga.user_black_list_logic.GetUserBlackListReq) returns (ga.user_black_list_logic.GetUserBlackListResp) {
        option (logic.gateway.command) = {
                id: 30020   //改成正确的id
            };
    }
    
    rpc AddUserBlackList (ga.user_black_list_logic.AddUserBlackListReq) returns (ga.user_black_list_logic.AddUserBlackListResp) {
        option (logic.gateway.command) = {
                id: 30021   //改成正确的id
            };
    }

    rpc DelUserBlackList (ga.user_black_list_logic.DelUserBlackListReq) returns (ga.user_black_list_logic.DelUserBlackListResp) {
        option (logic.gateway.command) = {
                id: 30022   //改成正确的id
        };
    }

    rpc CheckIsInBlackList (ga.user_black_list_logic.CheckIsInBlackListReq) returns (ga.user_black_list_logic.CheckIsInBlackListResp) {
        option (logic.gateway.command) = {
                id: 30023   //改成正确的id
        };
    }

    rpc BarV2 (ga.user_black_list_logic.CheckIsInBlackListReq) returns (ga.user_black_list_logic.CheckIsInBlackListResp) {
        option (logic.gateway.command) = {
                id: 30024   //改成正确的id
        };
    }
}
