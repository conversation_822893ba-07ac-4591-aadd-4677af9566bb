syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "usercheckinlogic/user-checkin-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/usercheckinlogic";

service UserCheckinLogic {
  option (logic.gateway.service_ext) = {
    service_name: "user-checkin-logic"
  };

  rpc ReceiveAward (ga.usercheckinlogic.ReceiveAwardReq) returns (ga.usercheckinlogic.ReceiveAwardRsp) {
            option (logic.gateway.command) = {
                id: 30452   //改成正确的id
            };
    }
    /*入口信息*/
    rpc CheckInEntranceInfo (ga.usercheckinlogic.CheckInEntranceInfoReq) returns (ga.usercheckinlogic.CheckInEntranceInfoResp) {
      option (logic.gateway.command) = {
        id: 30451
      };
    }
    rpc SetDeeplinkSource (ga.usercheckinlogic.SetDeeplinkSourceReq) returns (ga.usercheckinlogic.SetDeeplinkSourceRsp) {
      option (logic.gateway.command) = {
        id: 30453
      };
    }

}