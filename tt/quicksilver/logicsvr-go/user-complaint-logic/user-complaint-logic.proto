syntax = "proto3";

package logic.usercomplaint;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "usercomplaint_logic/user-complaint-logic_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/usercomplaint_logic";

service UsercomplaintLogic {
    option (logic.gateway.service_ext) = {
        service_name: "user-complaint-logic"
    };

    rpc GetUserComplaintEntry ( ga.usercomplaint_logic.GetUserComplaintEntryReq ) returns ( ga.usercomplaint_logic.GetUserComplaintEntryResp ) {
        option (logic.gateway.command) = {
            id: 32006
        };
    }
}