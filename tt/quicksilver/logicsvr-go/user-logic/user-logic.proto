syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "userlogic/user-logic_.proto";
import "im/im.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/userlogic";

service UserLogic {
    rpc GetUserContractInfo (ga.userlogic.GetUserContractInfoNewReq) returns (ga.userlogic.GetUserContractInfoNewResp) {
        option (logic.gateway.command) = {
            id: 2528
        };
    }

    rpc AgreeUserContract (ga.userlogic.AgreeUserContractNewReq) returns (ga.userlogic.AgreeUserContractNewResp) {
        option (logic.gateway.command) = {
            id: 2529
        };
    }

    rpc QueryMsgSetting (ga.im.QueryMsgSettingReq) returns (ga.im.QueryMsgSettingResp) {
        option (logic.gateway.command) = {
            id: 411
        };
    }

    rpc SetUgcChannelDenoiseMode(ga.userlogic.SetUgcChannelDenoiseModeReq) returns (ga.userlogic.SetUgcChannelDenoiseModeResp) {
        option (logic.gateway.command) = {
            id: 2530
        };
    }

    rpc GetUgcChannelDenoiseMode(ga.userlogic.GetUgcChannelDenoiseModeReq) returns (ga.userlogic.GetUgcChannelDenoiseModeResp) {
        option (logic.gateway.command) = {
            id: 2531
        };
    }

    rpc GetUgcMoreConfigList(ga.userlogic.GetUgcMoreConfigListReq) returns (ga.userlogic.GetUgcMoreConfigListResp) {
        option (logic.gateway.command) = {
            id: 2532
        };
    }
}