syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/user-music-rank";


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "user_music_rank/user-music-rank-logic_.proto";

service UserMusicRankLogic {
  option (logic.gateway.service_ext) = {
    service_name: "user-music-rank-logic"
  };

  rpc GetMusicRankUserInfo(ga.user_music_rank.BatchMusicRankUserInfoReq ) returns (ga.user_music_rank.BatchMusicRankUserInfoResp ) {
    option (logic.gateway.command) = {
      id: 31600
    };
  }

  rpc GetUserMusicRankDialog(ga.user_music_rank.GetUserMusicRankDialogReq ) returns (ga.user_music_rank.GetUserMusicRankDialogResp ) {
    option (logic.gateway.command) = {
      id: 31601
    };
  }

  rpc ListUserMusicRankSingerScore(ga.user_music_rank.ListUserMusicRankSingerScoreReq ) returns (ga.user_music_rank.ListUserMusicRankSingerScoreResp ) {
    option (logic.gateway.command) = {
      id: 31602
    };
  }

  rpc GetUserMusicRankSingerScoreDetail(ga.user_music_rank.GetUserMusicRankSingerScoreDetailReq ) returns (ga.user_music_rank.GetUserMusicRankSingerScoreDetailResp ) {
    option (logic.gateway.command) = {
      id: 31603
    };
  }

  rpc UserMusicRankDialogConfirm(ga.user_music_rank.UserMusicRankDialogConfirmReq ) returns (ga.user_music_rank.UserMusicRankDialogConfirmResp ) {
    option (logic.gateway.command) = {
      id: 31604
    };
  }
}