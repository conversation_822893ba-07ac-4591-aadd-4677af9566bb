syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel/user_tag_.proto";
import "user_tag_v2/user_tag_v2_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/user-tag-logic-go";

service UserTagLogicGo {
    option (logic.gateway.service_ext) = {
      service_name: "user-tag-logic-go"
    };

    rpc GetRegistTagConf (ga.channel.GetRegistTagConfReq) returns (ga.channel.GetRegistTagConfResp) {
        option (logic.gateway.command) = {
            id:30631
      };

    }

    rpc SetRegistTag (ga.channel.SetRegistTagReq) returns (ga.channel.SetRegistTagResp) {
        option (logic.gateway.command) = {
            id:30632
      };
    }

    rpc GetUserTagV2 (ga.user_tag_v2.GetUserTagV2Req) returns (ga.user_tag_v2.GetUserTagV2Resp) {
        option (logic.gateway.command) = {
            id:30634
        };
    }
    rpc SetUserTagV2 (ga.user_tag_v2.SetUserTagV2Req) returns (ga.user_tag_v2.SetUserTagV2Resp) {
        option (logic.gateway.command) = {
            id:30635
        };
    }
    rpc GetUserAllTagConfigV2 (ga.user_tag_v2.GetUserAllTagConfigV2Req) returns (ga.user_tag_v2.GetUserAllTagConfigV2Resp) {
        option (logic.gateway.command) = {
            id:30636
        };
    }

}