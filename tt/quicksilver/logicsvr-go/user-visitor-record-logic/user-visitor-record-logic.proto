syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/user-visitor-record-logic";
import "user_visitor_record/user-visitor-record-logic_.proto";
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;


import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";

service UserVisitorRecordLogic {
    option (logic.gateway.service_ext) = {
        service_name: "user-visitor-record-logic"
    };

    rpc ReportUserVisitorRecord (ga.user_visitor_record.ReportUserVisitorRecordReq) returns (ga.user_visitor_record.ReportUserVisitorRecordResp) {
        option (logic.gateway.command) = {
            id: 30150
        };
    }
    rpc GetUserVisitorRecordList (ga.user_visitor_record.GetUserVisitorRecordListReq) returns (ga.user_visitor_record.GetUserVisitorRecordListResp) {
        option (logic.gateway.command) = {
            id: 30151
        };
    }
    rpc GetUserBeVisitorRecordList (ga.user_visitor_record.GetUserBeVisitorRecordListReq) returns (ga.user_visitor_record.GetUserBeVisitorRecordListResp) {
        option (logic.gateway.command) = {
            id: 30152
        };
    }
    rpc GetUserBeVisitorRecordCount (ga.user_visitor_record.GetUserBeVisitorRecordCountReq) returns (ga.user_visitor_record.GetUserBeVisitorRecordCountResp) {
        option (logic.gateway.command) = {
            id: 30153
        };
    }
    // 获取任务状态
    rpc GetAllTaskStatus (ga.user_visitor_record.GetAllTaskStatusReq) returns (ga.user_visitor_record.GetAllTaskStatusResp) {
        option (logic.gateway.command) = {
            id: 30154
        };
    }
    // 设置用户是否显示访客数
    rpc SetShowUserBeVisitorRecordCount (ga.user_visitor_record.SetShowUserBeVisitorRecordCountReq) returns (ga.user_visitor_record.SetShowUserBeVisitorRecordCountResp) {
        option (logic.gateway.command) = {
            id: 30155
        };
    }
    // 获取用户是否显示访客数
    rpc GetShowUserBeVisitorRecordCount (ga.user_visitor_record.GetShowUserBeVisitorRecordCountReq) returns (ga.user_visitor_record.GetShowUserBeVisitorRecordCountResp) {
        option (logic.gateway.command) = {
            id: 30156
        };
    }
    // 获取用户设置的隐藏列表
    rpc GetHideList (ga.user_visitor_record.GetHideListReq) returns (ga.user_visitor_record.GetHideListResp) {
        option (logic.gateway.command) = {
            id: 30157
        };
    }
    // 获取用户对其他用户的隐藏设置
    rpc GetHideStatusByUid (ga.user_visitor_record.GetHideStatusByUidReq) returns (ga.user_visitor_record.GetHideStatusByUidResp) {
        option (logic.gateway.command) = {
            id: 30158
        };
    }
    // 设置用户对其他用户的隐藏
    rpc SetHideStatusByUid (ga.user_visitor_record.SetHideStatusByUidReq) returns (ga.user_visitor_record.SetHideStatusByUidResp) {
        option (logic.gateway.command) = {
            id: 30159
        };
    }
}