syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "usual_device_logic/usual-device-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/usual-device-logic";

service UsualDeviceLogic {
    option (logic.gateway.service_ext) = {
        service_name: "usual-device-logic"
    };

    rpc GetFaceAuthCheckResult (ga.usual_device_logic.GetFaceAuthCheckResultReq) returns (ga.usual_device_logic.GetFaceAuthCheckResultResp) {
        option (logic.gateway.command) = {
            id: 30801
        };
    }
    rpc GetMessageCheckInfo (ga.usual_device_logic.GetMessageCheckInfoReq) returns (ga.usual_device_logic.GetMessageCheckInfoResp) {
        option (logic.gateway.command) = {
            id: 30802
        };
    }
    rpc GetMessageCheckResult (ga.usual_device_logic.GetMessageCheckResultReq) returns (ga.usual_device_logic.GetMessageCheckResultResp) {
        option (logic.gateway.command) = {
            id: 30803
        };
    }
}



