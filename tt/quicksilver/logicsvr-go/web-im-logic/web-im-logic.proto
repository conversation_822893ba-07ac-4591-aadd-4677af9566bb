syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "web_im_logic/web_im_logic.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/web-im-logic";

service WebImLogic {
    option (logic.gateway.service_ext) = {
    	service_name: "web-im-logic"
    };

    // 发送 WEB IM 消息
    rpc SendWebImMsg(ga.web_im_logic.SendWebImMsgRequest) returns(ga.web_im_logic.SendWebImMsgResponse) {
        option (logic.gateway.command) = {
            id: 1350
        };
    }

    // 获取 WEB IM 消息
    rpc GetWebImMsgList(ga.web_im_logic.GetWebImMsgListRequest) returns(ga.web_im_logic.GetWebImMsgListResponse) {
        option (logic.gateway.command) = {
            id: 1351
        };
    }


    // 标记 WEB IM 消息已读 
    rpc ReadWebImMsg(ga.web_im_logic.ReadWebImMsgRequest) returns(ga.web_im_logic.ReadWebImMsgResponse) {
        option (logic.gateway.command) = {
            id: 1352
        };
    }

    // 获取用户的 WEB IM 消息
    rpc GetUserWebImMsgList(ga.web_im_logic.GetUserWebImMsgListRequest) returns(ga.web_im_logic.GetUserWebImMsgListResponse) {
        option (logic.gateway.command) = {
            id: 1353
        };
    }
}
