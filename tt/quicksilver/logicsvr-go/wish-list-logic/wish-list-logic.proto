syntax = "proto3";
// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;

import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "wishlistlogic/wish-list-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/wishlistlogic";

service WishListLogic {
  option (logic.gateway.service_ext) = {
    service_name: "wish-list-logic"
  };

  rpc GetAnchorWishList(ga.wishlistlogic.GetAnchorWishListReq) returns (ga.wishlistlogic.GetAnchorWishListResp) {
    option (logic.gateway.command) = {
      id: 50700
    };
  }
  rpc SetAnchorWishList(ga.wishlistlogic.SetAnchorWishListReq) returns (ga.wishlistlogic.SetAnchorWishListResp) {
    option (logic.gateway.command) = {
      id: 50701
    };
  }
  rpc GetWishGratitudeWords(ga.wishlistlogic.GetWishGratitudeWordsReq) returns (ga.wishlistlogic.GetWishGratitudeWordsResp) {
    option (logic.gateway.command) = {
      id: 50702
    };
  }
}