syntax = "proto3";

// buf:lint:ignore PACKAGE_SAME_DIRECTORY
package logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "youknowwhologic/you-know-who-logic_.proto";

// buf:lint:ignore PACKAGE_SAME_GO_PACKAGE
option go_package = "golang.52tt.com/protocol/services/logicsvr-go/youknowwhologic";

service YouKnowWhoLogic {
	  option (logic.gateway.service_ext) = {
		    service_name: "you-know-who-logic"
	  };

		rpc ChangeUKWSwitch (ga.youknowwhologic.ChangeUKWSwitchReq) returns (ga.youknowwhologic.ChangeUKWSwitchResp) {
				option (logic.gateway.command) = {
						id: 3790   //改成正确的id
				};
		}

		rpc GetUKWInfo (ga.youknowwhologic.GetUKWInfoReq) returns (ga.youknowwhologic.GetUKWInfoResp) {
				option (logic.gateway.command) = {
						id: 3791   //改成正确的id
				};
		}

		rpc GetUKWUserProfile (ga.youknowwhologic.GetUKWUserProfileReq) returns (ga.youknowwhologic.GetUKWUserProfileResp) {
				option (logic.gateway.command) = {
						id: 3792   //改成正确的id
				};
		}

	  rpc ChangeRankSwitch (ga.youknowwhologic.ChangeRankSwitchReq) returns (ga.youknowwhologic.ChangeRankSwitchResp) {
		    option (logic.gateway.command) = {
			      id: 3793   //改成正确的id
		    };
	  }

    rpc SendShowUpMsg (ga.youknowwhologic.SendShowUpMsgReq) returns (ga.youknowwhologic.SendShowUpMsgResp) {
        option (logic.gateway.command) = {
              id: 3794   //改成正确的id
        };
    }

    //消息列表
    rpc GetShowUpMsgList (ga.youknowwhologic.GetShowUpMsgListReq) returns (ga.youknowwhologic.GetShowUpMsgListResp) {
        option (logic.gateway.command) = {
              id: 3795   //改成正确的id
        };
    }

    //消息文案列表
    rpc GetShowUpTextList (ga.youknowwhologic.GetShowUpTextListReq) returns (ga.youknowwhologic.GetShowUpTextListResp) {
        option (logic.gateway.command) = {
              id: 3796   //改成正确的id
        };
    }

		// 神秘人现身
		rpc ExposureUKW (ga.youknowwhologic.ExposureUKWReq) returns (ga.youknowwhologic.ExposureUKWResp) {
				option (logic.gateway.command) = {
						id: 3797   //改成正确的id
				};
		}

		// 神秘人现身
		rpc UserChangeUKWEnterNotice (ga.youknowwhologic.UserChangeUKWEnterNoticeReq) returns (ga.youknowwhologic.UserChangeUKWEnterNoticeResp) {
				option (logic.gateway.command) = {
						id: 3798   //改成正确的id
				};
		}
}