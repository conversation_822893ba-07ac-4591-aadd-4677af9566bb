syntax = "proto3";

package smash_egg;

import "tt/quicksilver/extension/options/options.proto";

option go_package = "golang.52tt.com/protocol/services/smash-egg";
import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";

// 砸蛋方式
enum Source{
  Manual = 0;  //手动
  AUTO = 1;  //自动
}

// 砸蛋状态
enum Flag {
  NORMAL = 0;  //正常
  MORPH = 1;  //变身
}

// 砸蛋方式
enum Mode {
  NORMAL_MODE = 0;  //普通
  GOLD_MODE = 1;  //金色
}

enum Status{
  ENABLE = 0;    //正常
  DISABLE = 1;  //禁用
}

// 平台
enum Platform {
  Unknown_Platform = 0;
  Android = 1;
  iOS = 2;
}

// 应用
enum App {
  Unknown_App = 0;
  TT = 1;        //ttvoice
  HUANYOU = 2;    //huanyou
  ZAIYA = 3;      //zaiya
}


//消费记录
message ConsumeRecord {
  uint64 id = 1;
  uint32 uid = 2;
  uint32 amount = 3;    //购买数量
  uint32 fee = 4;      //花费T豆
  string order_id = 5;  //订单号
  uint32 create_time = 6;
}

//中奖记录
message WinningRecord {
  uint64 id = 1;
  uint32 uid = 2;

  Source source = 3;    //中奖来源，0：手动， 1：自动

  Flag flag = 4;      //0: 普通礼物 1：变身礼物

  uint32 pack_id = 5;    //礼包id
  uint32 pack_worth = 6;  //礼包价值
  string pack_name = 7;  //礼包名
  string pack_pic = 8;  //礼包图片
  uint32 pack_amount = 9;  //礼包数量
  string pack_desc = 10;  //礼包描述，备用

  uint32 create_time = 11;
  uint32 channel_id = 12;

  string order_id = 13;

  Mode mode = 14;

  // 中奖光效
  uint32 light_effect_id = 15;
  string light_effect_text = 16; // 中将光效文案（XXX占位符）
  string light_effect_url = 17;  // 中奖光效链接
}

message Config {
  uint32 morph_hits = 1;    //变身击打次数，也就是变身值
  uint32 morph_duration = 2;  //变身持续时间

  uint32 daily_limit = 3;    //用户每日砸蛋次数上限

  repeated Platform limit_platform = 4; //限制砸蛋的平台,已经废弃
  repeated App limit_app = 5;  //限制砸蛋的应用,已经废弃

  uint32 status = 6;      //0：禁用，1：启用，2：异常

  uint32 deficit_limit = 7;  //亏损停服阈值
  uint32 deficit_warning = 8;  //亏损警告阈值

  uint64 wealth_limit = 9;  //财富值限制,已经废弃

  uint32 speed_step = 10;  //加速基数
  uint32 speed_up = 11;  //加速条件
  float max_speed = 12;  //最大加速度  每分钟虚拟击打次数 = 加速基数*（1 + min（实际击打次数 / 加速条件, 最大加速度））

  uint32 max_rand_n = 13;

  uint64 charm_limit = 14;//魅力值限制,已经废弃
  uint32 level_limit = 15;//经验值限制,已经废弃

  uint32 overall_speed = 16; //最大幸运值

  // 充值红钻包裹id
  uint32 recharge_pack_id = 17;
}

message Prize {
  uint64 id = 1;

  Flag flag = 2;        //0: 普通礼物 1：变身礼物

  Status status = 3;    //0: 正常 1：禁用，被删除的礼物配置会设置为禁用，禁用礼物不会在获取时返回

  uint32 weight = 4;    //权重

  uint32 pack_id = 5;     //礼包id
  uint32 pack_worth = 6;  //礼包价值
  string pack_name = 7;   //礼包名
  string pack_pic = 8;    //礼包图片
  uint32 pack_amount = 9; //礼包数量
  string pack_desc = 10;  //礼包描述，备用

  uint32 create_time = 11;

  Mode mode = 12;
}

message GetConsumeRecordReq {
  uint32 uid = 1;
  string order_id = 2;
  uint64 offset = 3;
  uint32 limit = 4;
  uint32 begin_time = 5;
  uint32 end_time = 6;
}

message GetConsumeRecordResp {
  repeated ConsumeRecord consume_record_list = 1;
}

message GetWinningRecordReq {
  uint32 uid = 1;
  uint64 offset = 2;
  uint32 limit = 3;
  uint32 begin_time = 4;
  uint32 end_time = 5;
  bool rare = 6;
  string page = 7;

  Mode mode = 8;
  Flag morph_flag = 9;
}

message GetWinningRecordResp {
  repeated WinningRecord winning_record_list = 1;
  string page = 2;
}

message GuaranteedInfo {
  Mode mode = 1;
  Flag morph_flag = 2;
  uint32 speed = 3; // 用户当前保底值
  uint32 overall_speed = 4; // 配置最大保底值
}

message GetSmashStatusReq {
  uint32 uid = 1;
}

message GetSmashStatusResp {
  uint32 current_hits = 1;

  uint32 my_remain_hits = 2;
  uint32 my_today_hits = 3;

  Flag morph_flag = 4;
  uint32 morph_end_time = 5;
  uint32 speed = 6; // 废弃
  uint32 overall_speed = 7; // 废弃

  repeated GuaranteedInfo guaranteed_info_list = 8;
}

message RechargeReq {
  ConsumeRecord record = 1;
}

message RechargeResp {
  uint32 amount = 1;
  string order_id = 2;
}

message SmashReq {
  uint32 uid = 1;
  uint32 amount = 2;

  uint32 channel_id = 3;
  uint32 channel_type = 4;

  Platform platform = 5;
  Source source = 6;
  Mode mode = 7;
  Flag morph_flag = 8;
}

message SmashResp {
  uint32 remain_chance = 1;

  repeated WinningRecord winning_record_list = 2;

  uint32 current_hits = 3;

  uint32 speed = 4;
  uint32 overall_speed = 5;

  bool isBingo = 6;
}

message GetSmashConfigReq {
  uint32 uid = 1;
}

message GetSmashConfigResp {
  Config config = 1;

  bool accessible = 2;
}

message SetSmashConfigReq {
  Config config = 1;
}

message SetSmashConfigResp {

}

message GetPrizePoolReq {
  Flag flag = 1;
  Mode mode = 2;
}

message GetPrizePoolResp {
  repeated Prize prize_list = 1;
}

// 获取未生效奖池
message GetPrizePoolTmpReq{
  Flag flag = 1;
  Mode mode = 2;
}

message GetPrizePoolTmpResp {
  repeated Prize prize_list = 1;
  uint32 effect_time = 2;
}

message SetPrizePoolReq {
  Flag flag = 1;

  repeated Prize prize_list = 2;

  Mode mode = 3;

  uint32 effect_time = 4;  // 奖池生效时间戳(s)
}

message SetPrizePoolResp {

}

message DelTmpPrizePoolReq{
    Flag flag = 1;
    Mode mode = 2;
    uint32 effect_time =3; //未生效奖池的生效时间
}
message DelTmpPrizePoolResp{
    
}

message SimulateWithPrizePoolReq {
  uint32 smash_profit = 1;
  uint32 smash_times = 2;

  Flag flag = 3;

  repeated Prize prize_list = 4;

  Mode mode = 5;
}

message SimulateWithPrizePoolResp {
  int32 expect_profit = 1;
  int32 average_profit = 2;
}

message CheckWhitelistReq {
  uint32 uid = 1;
}

message CheckWhitelistResp {
  bool exist = 1;
}

message CheckTesterReq {
  uint32 uid = 1;
}

message CheckTesterResp {
  bool exist = 1;
}

message GetSmashEggExemptValueReq{
    uint32 uid =1;
}

message GetSmashEggExemptValueResp{
    bool smash_flag = 1;
    uint32 remain_chance = 2;
    uint32 luck_value = 3;
}

service SmashEgg {
  option (service.options.old_package_name) = "ga.smash_egg.SmashEgg";

  //获取消费记录
  rpc GetConsumeRecord (smash_egg.GetConsumeRecordReq) returns (smash_egg.GetConsumeRecordResp) {

  }

  //获取中奖记录
  rpc GetWinningRecord (smash_egg.GetWinningRecordReq) returns (smash_egg.GetWinningRecordResp) {

  }

  //充值
  rpc Recharge (smash_egg.RechargeReq) returns (smash_egg.RechargeResp) {

  }

  rpc UpdateConsumeTBeanTime (smash_egg.UpdateConsumeTBeanTimeReq) returns (smash_egg.UpdateConsumeTBeanTimeResp) {}

  //砸
  rpc Smash (smash_egg.SmashReq) returns (smash_egg.SmashResp) {

  }

  //获取状态
  rpc GetSmashStatus (smash_egg.GetSmashStatusReq) returns (smash_egg.GetSmashStatusResp){

  }

  //获取配置
  rpc GetSmashConfig(smash_egg.GetSmashConfigReq) returns (smash_egg.GetSmashConfigResp){

  }

  //设置配置
  rpc SetSmashConfig(smash_egg.SetSmashConfigReq) returns (smash_egg.SetSmashConfigResp){

  }

  //获取奖励池
  rpc GetPrizePool(smash_egg.GetPrizePoolReq) returns (smash_egg.GetPrizePoolResp){

  }

  //获取未生效奖励池
  rpc GetPrizePoolTmp(smash_egg.GetPrizePoolTmpReq) returns (smash_egg.GetPrizePoolTmpResp){

}


  //设置奖励池
  rpc SetPrizePool(smash_egg.SetPrizePoolReq) returns (smash_egg.SetPrizePoolResp){

  }

  rpc DelTmpPrizePool(smash_egg.DelTmpPrizePoolReq) returns(smash_egg.DelTmpPrizePoolResp){

  }

  //指定奖励池进行收益模拟
  rpc SimulateWithPrizePool(smash_egg.SimulateWithPrizePoolReq) returns (smash_egg.SimulateWithPrizePoolResp){

  }

  //白名单检测
  rpc CheckWhitelist(smash_egg.CheckWhitelistReq) returns (smash_egg.CheckWhitelistResp){

  }

  rpc CheckTester(smash_egg.CheckTesterReq) returns (smash_egg.CheckTesterResp){

  }

  // 豁免条件值
  rpc GetSmashEggExemptValue(GetSmashEggExemptValueReq) returns(GetSmashEggExemptValueResp){}

  /*********对账接口**********/
  // 发放包裹数据对账
  rpc GetAwardTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetAwardOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  // T豆消费数据对账
  rpc GetConsumeTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetConsumeOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}

  // ======================== 转转换皮玩法信息配置 ===========================
  
  rpc UpdateSmashEggActivityConfig(smash_egg.UpdateSmashEggActivityConfigReq) returns (smash_egg.UpdateSmashEggActivityConfigResp) {} // 不存在则新增
  rpc GetSmashEggActivityConfig(smash_egg.GetSmashEggActivityConfigReq) returns (smash_egg.GetSmashEggActivityConfigResp) {}
  rpc CheckSmashEggActivityConfig(smash_egg.CheckSmashEggActivityConfigReq) returns (smash_egg.CheckSmashEggActivityConfigResp) {}
  rpc DelSmashEggActivityConfig(smash_egg.DelSmashEggActivityConfigReq) returns (smash_egg.DelSmashEggActivityConfigResp) {}
  rpc GetSmashEggActivityConfWithCache(smash_egg.GetSmashEggActivityConfWithCacheReq) returns (smash_egg.GetSmashEggActivityConfWithCacheResp) {}

  // 中奖光效管理
  rpc AddSmashLightEffectsV2(AddSmashLightEffectsV2Req) returns (AddSmashLightEffectsV2Resp){}
  rpc UpdateSmashLightEffectV2(UpdateSmashLightEffectV2Req) returns (UpdateSmashLightEffectV2Resp){}
  rpc GetAllSmashLightEffectsV2(GetAllSmashLightEffectsV2Req) returns (GetAllSmashLightEffectsV2Resp){}
  rpc DelLightEffectByconfId(DelLightEffectByconfIdReq) returns (DelLightEffectByconfIdResp){}// 删除光效

  // 用户道具
  rpc GetUserProp(GetUserPropReq) returns (GetUserPropResp){}

  // 获取当前活动类型
  rpc GetCurActivityType(GetCurActivityTypeReq) returns (GetCurActivityTypeResp){}

  // 道具退还相关
  rpc GetRefundPropInfo(GetRefundPropInfoReq) returns (GetRefundPropInfoResp){}
  rpc ConfirmRefundProp(ConfirmRefundPropReq) returns (ConfirmRefundPropResp) {}
  rpc SetRefundStatus(SetRefundStatusReq) returns (SetRefundStatusResp) {}
  rpc PropSettlement(PropSettlementReq) returns (PropSettlementResp) {}

  // ----------------- 用户道具查询后台 -----------------
  // 分页获取用户道具有效期
  rpc GetUserPropExpireDetail(GetUserPropExpireDetailReq) returns (GetUserPropExpireDetailResp){}
}


// ========================= 换皮后台 ==========================

message SmashActivityConfig{
    string activity_name = 1;  // 活动名
    int64 begin_time = 2;      // 开始时间
    int64 end_time = 3;        // 结束时间
    uint32 smash_prop_id = 4;     // 参与道具id
    uint32 prop_duration = 5;       // 道具有效期，正整数，天
    uint32 prop_gift_pack_id = 6; // 红钻礼物包裹id
    uint32 bingo_pack_id = 7;  // 大奖包裹id
    uint32 vision_id = 8;      // 视觉id
    uint32 risk_id = 9;         // 风控id
    string risk_secret = 10;     // 风控秘钥
    uint32 bingo_breaking_new_id = 11; // 大奖全服样式id
    string tbean_app_id = 12;   // T豆道具货币id

    // 规则资源后缀
    string activity_rules = 13;     // 玩法规则
    string regular_prize_pool = 14; // 查看奖池-普通奖池
    string gold_prize_pool = 15;    // 查看奖池-高级奖池
    string morph_rules = 16;        // 变身玩法规则
    string luck_point_rules = 17;   // 普通模式幸运值说明
    string gold_luck_point_rules = 18;   // 金色转转幸运值说明
    string composite_rules = 19;  // 合成页规则
    string dark_composite_rules = 20; // 黑暗合成页规则
    string dark_energy_stones = 21;   // 能量石规则页
    string gold_activity_rules = 22; // 金色模式规则

    int64 update_time = 23; // 更新时间

    uint32 prop_price = 24; // 道具价值

    string morph_prize_pool = 25; // 查看奖池-变身奖池
    string morph_luck_point_rules = 26;   // 变身期间幸运值说明
}


// 覆盖更新玩法基本信息配置,不存在则新增
message UpdateSmashEggActivityConfigReq{
    SmashActivityConfig config = 1;
}

message UpdateSmashEggActivityConfigResp{
}

// 各模式配置
message ModeCfg {
  Mode mode = 1;
  Flag morph_flag = 2;
  repeated uint32 recharge_prop_num_options = 3;  // 购买道具数量选项
  uint32 smash_cost_prop_num = 4; // 每次抽奖消耗道具数量
  uint32 guaranteed_pack_id = 5;  // 保底奖励包裹id
}

// 获取玩法基本信息配置
message GetSmashEggActivityConfigReq{
    bool with_rules_conf = 1;       // 是否需要返回规则配置
}

message GetSmashEggActivityConfigResp{
    SmashActivityConfig config = 1;
    repeated ModeCfg mode_cfg_list = 2;
}

message GetSmashEggActivityConfWithCacheReq{
    bool with_rules_conf = 1;       // 是否需要返回规则配置
}

message GetSmashEggActivityConfWithCacheResp{
    SmashActivityConfig config = 1;
    repeated ModeCfg mode_cfg_list = 2;
}

// 检查玩法基本信息配置
message CheckSmashEggActivityConfigReq{
    SmashActivityConfig config = 1;
}

// 玩法信息校验不通过时，报错返回
message CheckSmashEggActivityConfigResp{
    
}

// 删除玩法基本信息配置
message DelSmashEggActivityConfigReq{
}

message DelSmashEggActivityConfigResp{

}

message SmashLightEffect{
    uint32 conf_id = 1;   // 光效id
    uint32 level = 2;     // 光效等级
    string effect_res = 3;  // 光效图片url
    string text = 4;        // 光效文案
    string op_user = 5;     // 操作人
    string op_remind = 6;   // 备注

    int64 update_time = 7; // 更新时间
}


// 中奖光效管理
message AddSmashLightEffectsV2Req{
    SmashLightEffect conf = 1;
}

message AddSmashLightEffectsV2Resp{
}

// 编辑中奖光效,根据光效id覆盖更新
message UpdateSmashLightEffectV2Req{
    SmashLightEffect conf = 1;
}

message UpdateSmashLightEffectV2Resp{

}

// 获取对应玩法的所有光效配置
message GetAllSmashLightEffectsV2Req{
    uint32 search_val = 1;              // 搜索值，有值时返回 光效id==search_val 或 等级==search_val 的记录列表。为0则返回所有配置
}

message GetAllSmashLightEffectsV2Resp{
    repeated SmashLightEffect conf_list = 1;
}

// 删除光效
message DelLightEffectByconfIdReq{
    uint32 conf_id = 1;
    string op_user = 3;   // 操作人
}

message DelLightEffectByconfIdResp{
}

// 用户道具
message UserProp {
  uint32 prop_id = 1;
  uint32 num = 2;
  int64 expire_time = 3;
}

// 获取用户未过期的道具
message GetUserPropReq {
  uint32 uid = 1;
  uint32 prop_id = 2;
}

message GetUserPropResp {
  repeated UserProp user_prop_list = 1;
}

enum CurThemeType{
  CUR_THEME_TYPE_UNSPECIFIED = 0;
  CUR_THEME_TYPE_A = 1;              // 活动A
  CUR_THEME_TYPE_B = 2;              // 活动B
}

message GetCurActivityTypeReq{}

message GetCurActivityTypeResp{
    CurThemeType cur_activity_type = 1;
}

// 道具退还相关
enum RefundStatus{
    REFUND_STATUS_UNSPECIFIED = 0;  // 无效
    REFUND_STATUS_NOT_SUMMITED = 1; // 未提交
    REFUND_STATUS_APPLYING = 2;     // 审批中
    REFUND_STATUS_REFUNDING = 3;    // 发放中
    REFUND_STATUS_REFUNDED = 4;     // 已发放
    REFUND_STATUS_NOT_SHOW = 5;     // 已阅，前端不展示
}

message RefundPropInfo{
    string activity_name = 1;   // 活动名称
    //下架时间
    int64 offline_time = 2; // 下架时间
    string prop_name = 3; // 道具名称
    uint32 remain_user_cnt = 4;  // 道具剩余人数
    uint32 remain_prop_cnt = 5;  // 道具剩余个数
    RefundStatus status = 6;  // 道具退还状态

    int64 finish_time = 7; // 方法时间
}

// 查询当前是否有可退还道具的记录信息
//（先调用GetCurActivityType接口(请求任意a\b服务的该接口都可)判断当前生效中的活动类型，如果cur_activity_type==CUR_THEME_TYPE_A,则调用smash-egg-b服务的该接口
// 反之，则调用smash-egg-a服务的该接口）
message GetRefundPropInfoReq{
}

message GetRefundPropInfoResp{
    RefundPropInfo info = 1;
}

// 设置退还记录状态，可设置 REFUND_STATUS_NOT_SUMMITED，REFUND_STATUS_APPLYING，REFUND_STATUS_NOT_SHOW
message SetRefundStatusReq{
    RefundStatus status = 1;  // 道具退还状态
}

message SetRefundStatusResp{
    
}

// 提交退还道具发放请求
message ConfirmRefundPropReq{
    RefundPropInfo info = 1;
}

message ConfirmRefundPropResp{
}

// 结算操作类型
enum SettlementType{
    SETTLEMENT_TYPE_UNSPECIFIED = 0;
    SETTLEMENT_TYPE_FREEZE = 1;  // 冻结充值、消耗
    SETTLEMENT_TYPE_COMMIT = 2;  // 确认结算   
}

// 道具结算
message PropSettlementReq{
    SettlementType op_type = 1; // 结算类型
    string secret_key = 2;      // 操作密钥
    int64 offline_time = 3;     // 真正下架时间
}

message PropSettlementResp{
    string msg = 1;     // 操作结果
}

message GetUserPropExpireDetailReq{
    uint32 uid = 1;
    uint32 prop_id = 2;

    int64 begin_ts = 3;
    int64 end_ts = 4;

    uint32 page_size = 5;
    uint32 page_idx = 6;
}

message GetUserPropExpireDetailResp{
    repeated UserProp user_prop_list = 1;
    uint32 total_cnt = 2;
}

message UpdateConsumeTBeanTimeReq {
    uint32 uid = 1;
    string order_id = 2; // 订单号
    int64 create_time = 3; // 创建时间
    int64 tbean_time = 4; // 消费时间
}

message UpdateConsumeTBeanTimeResp {}