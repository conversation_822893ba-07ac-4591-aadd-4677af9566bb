syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package youknowwhosettlement;
option go_package = "golang.52tt.com/protocol/services/youknowwho/youknowwhosettlement";

import "tt/quicksilver/extension/options/options.proto";

service YouKnowWhoSettlement {
  option (service.options.old_package_name) = "youknowwho.YouKnowWhoSettlement";
  option (service.options.service_ext) = {
    service_name: "you-know-who-settlement"
  };

  // 主动触发神秘人财务报表推送
  rpc PushUKWReport (PushUKWReportReq) returns (PushUKWReportResp) {}
  // 开通神秘人
  rpc OpenUKW (OpenUKWReq) returns (OpenUKWResp) {}
  // 对账接入接口
  rpc OrderTimeRangeInfo (OrderTimeRangeInfoReq) returns (OrderTimeRangeInfoResp) {}
  // 清除锁接口
  rpc RemoveLock (RemoveLockReq) returns (RemoveLockResp) {}
  // 神秘人下单
  rpc PlaceOrder (PlaceOrderReq) returns (PlaceOrderResp) {}
  // 对账时间范围内订单查询
  rpc TimeRangeOrderIds (TimeRangeReq) returns (OrderIdsResp) {}
  // 神秘人补单
  rpc ReplaceOrder (ReplaceOrderReq) returns (EmptyResp) {}
}

// 主动触发神秘人财务报表推送
message PushUKWReportReq {
  uint64 start_time = 1;                                // 结算开始范围
  uint64 end_time = 2;                                  // 结算结束范围
  string token = 3;                                     // 操作人token（邮箱的MD5值）
}
message PushUKWReportResp {}

// 开通神秘人信息
message OpenUKWReq {
  uint32 uid = 1;                                       // 神秘人Uid
  string order_id = 2;                                  // 订单ID
  uint32 open_time = 3;                                 // 开通时间
  string token = 4;                                     // 校验链路
  uint32 price = 5;                                     // 价值
  uint32 order_type = 6;                                // 订单类型
  uint32 client_version = 7;                            // 客户端版本
  uint32 client_type = 8;                               // 客户端类型
  uint32 place_time = 9;                                // 下单时间
}
message OpenUKWResp {
  uint32 uid = 1;                                       // 神秘人Uid
  uint32 server_time = 2;                               // 服务生效时间
  uint32 expire_time = 3;                               // 当前身份结束时间
}

// 对账接入接口
message OrderTimeRangeInfoReq {
  int64 begin_time = 1;
  int64 end_time = 2;
  string params = 3;    //配置的参数，固定值，一般为json
}
message OrderTimeRangeInfoResp {
  uint32 count = 1;
  uint32 value = 2; //总价值，如果没有填0
}

enum LockType {
  UnKnowType = 0;
  TypeString = 1;
  TypeHash = 2;
}

message RemoveLockReq {
  LockType lock_type = 1;
  string key = 2;
  string field = 3;
}

message RemoveLockResp {}

// 开通神秘人信息
message PlaceOrderReq {
  uint32 uid = 1;                                       // 神秘人Uid
  string order_id = 2;                                  // 订单ID
  uint32 price = 3;                                     // 价值
  uint32 order_type = 4;                                // 订单类型
  uint32 place_time = 5;                                // 下单时间
  uint32 client_version = 6;                            // 客户端版本
  uint32 client_type = 7;                               // 客户端类型
}
message PlaceOrderResp {
}

//请求时间范围
message TimeRangeReq {
  int64 begin_time = 1;
  int64 end_time = 2;
  string params = 3;
}
//响应orderId详情
message OrderIdsResp {
  repeated string order_ids = 1;
  repeated uint32 values = 2;
}

message ReplaceOrderReq {
  string order_id = 1;
  string params = 2;
}
message EmptyResp {

}